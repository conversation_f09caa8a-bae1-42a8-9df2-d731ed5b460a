/* You can add global styles to this file, and also import other style files */
/* You can add global styles to this file, and also import other style files */
@import "../node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css";
html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  background-color: #f9f9f9 !important;
}
*{
  font-family: "adani", sans-serif !important;
}
/* You can add global styles to this file, and also import other style files */
@font-face {
  font-family: "adani";
  src: url("/assets/fonts/adani_bold-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_bold-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_extralight-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_extralight-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_light-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_light-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_medium-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_medium-webfont.woff") format("woff");
  font-family: "adani";
  src: url("/assets/fonts/adani_regular-webfont.woff2") format("woff2"),
    url("/assets/fonts/adani_regular-webfont.woff") format("woff");
  // font-family: 'adani';
  // src: url('/assets/fonts/adani_semibold-webfont.woff2') format('woff2'),
  //   url('/assets/fonts/adani_semibold-webfont.woff') format('woff');

  font-weight: normal;
  font-style: normal;
}
// @import "~bootstrap/dist/css/bootstrap.css";
@import "./assets/scss/custom.scss";
@import "./assets/scss/table.scss";
@import "./assets/scss/toggle.scss";

body {
  margin: 0;
  padding: 0;
  font-family: "adani";
  // line-height: 0px !important;
}

body h1,
h2,
h3,
h4,
h5,
h6,
div,
textarea,
input,
select option,
ul li,
ul li a,
span {
  font-family: "adani" !important;
}
mat-select{
  font-family: "adani" !important;
}
li,
p {
  font-family: "adani";
}

body label {
  font-family: "adani";
}

body .bd-container {
  // height: 100%;
  height: 100vh !important;
  background: #f9f9f9;
}
mat-checkbox.mat-checkbox-checked {
  box-shadow: none !important; /* Remove box shadow */
}

img {
  height: 100%;
  width: 100%;
}

.error-message {
  color: red;
  font-weight: 400;
  font-size: 14px;
}

.fixed-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.modal-backdrop.show {
  opacity: 0.1 !important;
}

.modal-header {
  padding: 10px 16px !important;
  border-radius: 0px !important;
  margin-top: 0px !important;
  >.modal-title{
    font-size: 21px;
  }
}

input,
select,
option,
textarea {
  font-size: 12px !important;
  font-weight: 600 !important;
}

.page-header {
  font-size: 22px;
  font-weight: 600;
}

.section-header {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 0;
}

.label-header {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 0;
  color: #555555;
}

.input-text {
  border: 1px solid #ced4da;
  height: 40px;
  border-radius: 6px;
  width: 100%;
  padding: 9px;
}

.input-text:focus-visible {
  border: 0.5px solid #000;
}

.input-text-group {
  .w-260 {
    width: 260px;
  }

  label {
    z-index: 1000;
    font-family: Adani;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0em;
    text-align: left;
    color: #000;
    /* position: relative; */
    /* top: 10px; */
    /* left: 11px; */
    background: transparent;
  }
  >input{
    border-radius: 10px;
  }
  >select{
    border-radius: 10px;
  }
}

.dropdown-list {
  border: 1px solid #ced4da;
  height: 37px;
  border-radius: 6px;
  width: 100%;
  padding: 9px;
}

.space-between {
  display: flex;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dropdown-list select {
  border: none;
  width: 100%;
  outline: none;
}

.dropdown-list-autocomplete {
  font-size: 12px;
  font-weight: 600;
  // margin-bottom: 9px !important;
  // border-radius: 12px !important;
  // height: 24px !important;
}

.text-textarea {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 9px;
}

.button-submit {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  border: 0;
  border-radius: 30px;
  height: 39px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  width: 156px;
}

.button-back {
  border: 1px solid;
  border-radius: 30px;
  height: 39px;
  color: #bd3861 !important;
  font-size: 12px;
  font-weight: 600;
  width: 183px;
  margin: 0 20px;
  width: 156px;
  background: #fff;
}

.button-browser {
  padding: 6px 6px !important;
  font-size: 12px !important;
  font-weight: 600;
  background: #fcfdfd !important;
  border-top-right-radius: 5px !important;
  border-bottom-right-radius: 5px !important;
  border: solid 1px #d5d5d5 !important;
}

.bt-section {
  display: flex;
  justify-content: flex-end;
  // position: absolute;
  // right: 26px;
  // width: 330px;
}

.field-line {
  border-style: solid;
  border-color: #b5b5b5;
  border-width: 1px 0 0 0;
  width: 39px;
  height: 0px;
  transform-origin: 0 0;
  transform: rotate(90deg) scale(1, 1);
  margin-left: 15px;
}

.label-mandatory-field,
.asterisk {
  color: #ff0000 !important;
  padding-left: 3px;
}

.text-danger {
  font-family: "adani";
  font-size: 12px;
  font-weight: 600;
}

.button-right {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0px !important;

  span {
    flex: 1;
    margin: 0;
    // margin-right: 40px;
    text-align: center;
  }

  .right-arrow {
    width: 60px;
    height: 60px;
    // padding-left: 5px;
    // margin-left: -23px;
  }
}

.button-left {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0px !important;

  span {
    flex: 1;
    margin: 0;
    // margin-right: 40px;
    text-align: center;
  }

  .left-arrow {
    width: 60px;
    height: 60px;
    margin-right: -12px;
    // padding-left: 5px;
    // margin-left: -22px;
  }
}

button[disabled] {
  opacity: 0.5;
  cursor: no-drop !important;
}

.page-heading {
  font-size: 21px;
  font-weight: 700;
}

.fixed-bootom {
  position: fixed;
  bottom: 0;
  width: 100%;
}

.submit {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  border-radius: 30px !important;
  width: 120px;
  color: white !important;
}

.feature-card {
  background-color: #fff;
  padding-top: 20px;
  padding-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0px 0px 24px 0px rgba(146, 171, 186, 0.20);
  border: none;
  // border-radius: 16px;
  // background: #fff;
  // box-shadow: 0px 0px 24px 0px rgba(146, 171, 186, 0.20);
}

/* Rectangle 22873 */

::ng-deep mat-form-field, 
::ng-deep mat-label, 
::ng-deep mat-select, 
::ng-deep mat-option,
::ng-deep mat-input-element,
::ng-deep mat-form-field{  
  font-family: "adani", sans-serif !important;
  font-size: 12px !important;      
  font-weight: 600 !important;    
}

::ng-deep .matTooltip {
  // background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%) !important;
  background: #75479c;
  color: #fff !important;           /* White text */
  font-size: 14px;       /* Font size */
  padding: 8px 12px;     /* Padding */
  border-radius: 4px;    /* Rounded corners */
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.3); /* Subtle shadow */
}

.autocomplete-container {
  position: relative;
  // width: 300px;
}

input {
  width: 100%;
  // padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.autocomplete-list {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  width: 96%;
  background: white;
  border: 1px solid #ccc;
  border-radius: 5px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.autocomplete-list li {
  padding: 10px;
  cursor: pointer;
}

.autocomplete-list li:hover {
  background: #f0f0f0;
}

/* styles.scss */
/* Match Bootstrap's .form-control-lg */
