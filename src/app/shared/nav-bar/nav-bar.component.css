.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
    padding: 25px 20px;
  }
  .nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  /* Styling individual nav items */
.nav-links li {
    margin-left: 20px;
  }
  .nav-links a {
    color: white;
    text-decoration: none;
    font-size: 16px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
  }
  
  /* Hover effect for links */
  .nav-links a:hover {
    background-color: #ffffff;
    padding-top: 20px;
    padding-bottom: 28px;
    padding-left: 14px;
    padding-right: 14px;
    color: #0B74B0;
  }