.top-bar{
    height: 74px;
    padding-top: 12px;
    padding-left: 40px;
}
.switch-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
.switch {
    position: relative;
    display: inline-block;
    width: 81px;
    height: 42px;
  }
  
  .switch-input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .switch-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: background-color 0.3s;
    border-radius: 34px;
  }
  
  .switch-label:before {
    content: "";
    position: absolute;
    height: 32px;
    width: 32px;
    border-radius: 50%;
    left: 4px;
    bottom: 4px;
    background: linear-gradient(141.04deg, #0B74B0 16.88%, #75479C 52.26%, #BD3861 82.19%);
    transition: transform 0.3s, background 0.3s;
  }
  
  
  .switch-input:checked + .switch-label:before {
    transform: translateX(40px);
  }