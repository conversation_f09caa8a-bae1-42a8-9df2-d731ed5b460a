import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class MrStatusService {

  constructor(private apiService: ApiService) { }

  async getMRStatus(param:any){
    const response = await this.apiService.getData(ENDPOINTS.MR_STATUS.GET_MR_STATUS,param)
    return response
  }
}
