import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class SectionsService {

  constructor(private apiService: ApiService) { }

  async getSections(data: any){
    const response = await this.apiService.getData(ENDPOINTS.SECTIONS.GET_ALL_SECTIONS,data)
    return response
  }

  async assignSection(data: any){
    const response = await this.apiService.postData(ENDPOINTS.ADMIN.ASSIGN_SECTIONS,data)
    return response
  }

  async addSectionToStandBy(data: any){
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_SECTION.ADD,data)
    return response
  }

  async removeSectionToStandBy(data: any){
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_SECTION.REMOVE,data)
    return response
  }
}
