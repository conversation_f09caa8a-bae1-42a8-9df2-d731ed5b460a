import { Injectable } from '@angular/core';
import { ENDPOINTS } from '../../core/endpoints';
import { ApiService } from '../api/api.service';

@Injectable({
  providedIn: 'root'
})
export class SecurityService {

  constructor(private apiService: ApiService) { }

  async getSecurityData(param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_MODULE.GET_SECURITY_DATA, param)
    return response
  }

  async registerNewPlant(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_MODULE.REGISTER_NEW_PLANT, data)
    return response
  }
  async EditPlant(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_MODULE.EDIT_PLANT, data)
    return response
  }

  async getAdvisoryData(param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.ADVISORY_MODULE.GET_FILES, param)
    return response
  }
  async getLostAndFoundData(param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.GET_LOST_AND_FOUND, param)
    return response
  }
  async getLostAndFoundDataById(id?: string, param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.GET_LOST_AND_FOUND + `/${id}`, param)
    return response
  }
  async getFeedBack(param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.GET_FEEDBACK, param)
    return response
  }
  async getSecurityCount(param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.SECURITY_COUNT, param)
    return response
  }

  async UploadAdvisoryFile(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.ADVISORY_MODULE.UPLOAD_FILES, data)
    return response
  }
  async updateFile(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.ADVISORY_MODULE.UPDATE_FILE, data)
    return response
  }
  async deleteFile(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.ADVISORY_MODULE.DELETE_FILE, data)
    return response
  }

  async updateLostAndFound(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.UPDATE, data);
    return response;
  }
  async addLostAndFound(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.ADD_LOST_AND_FOUND, data);
    return response;
  }

  async getIncidentReport(data: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.GET_INCIDENT_REPORT, data);
    return response;
  }

  async addIncident(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.ADD_INCIDENT_REPORT, data);
    return response;
  }

  async updateIncidentReport(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.UPDATE, data);
    return response;
  }

  async deleteIncidentReport(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.UPDATE, data);
    return response;
  }

  async getSOSCallHistory(param?: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.GET_SOS_CALL_HISTORY, param)
    return response
  }

  async deleteCallHistory(param?: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.DELETE_CALL_HISTORY, param)
    return response
  }

  async lostandfoundGraph(param?: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.LOST_AND_FOUND_GRAPH, param)
    return response
  }

  async plantwiseGraph(param: any) {
    const response = await this.apiService.getData(ENDPOINTS.SECURITY_DASHBOARD.PLANTWISE_GRAPH, param)
    return response
  }

  async activeUser(param?: any) {
    const response = await this.apiService.postData(ENDPOINTS.SECURITY_DASHBOARD.ACTIVE_USER, param)
    return response
  }

}
