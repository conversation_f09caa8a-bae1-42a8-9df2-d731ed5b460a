import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class ClusterService {

  constructor(private apiService: ApiService) { }
  
     async getClusters(param?:any){
      const response = await this.apiService.getData(ENDPOINTS.CLUSTER.GET_CLUSTERS,param)
      return response
    }
}
