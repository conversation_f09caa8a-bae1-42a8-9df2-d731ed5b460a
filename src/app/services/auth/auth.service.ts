import { Injectable, signal } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';
import { AdminModel } from '../../models/admin.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  constructor(private apiService : ApiService) {}

  sendOtp(credentials : {email : string}){
    return this.apiService.postData(ENDPOINTS.AUTH.SEND_OTP,credentials)
  }

  async verifyOtp(otp : {email: string , otp: string}){
    const response = await this.apiService.postData(ENDPOINTS.AUTH.VERIFY_OTP,otp)
    let role:string;
    if (response['user'].wbiRoleId==1) {
      role='superadmin'
    } else role='plantadmin'
    if(response['responseCode'] == 200){
      localStorage.setItem('token', response['accessToken']);
      localStorage.setItem('user',JSON.stringify(response['user']));
      localStorage.setItem('userRole',role);
    }
    return response;
  }

  signup(userData: any){
    return this.apiService.postData('',userData)
  }

  logout(){
    localStorage.removeItem('token')
  }
}
