import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class StandByEquipmentsService {

  constructor(private apiService: ApiService) { }

  async getStandByEquipments(param: any) {
    const response = await this.apiService.getData(ENDPOINTS.STAND_BY_EQUIPMENTS.GET_STANDBY_EQUIPMENTS, param)
    return response
  }

  async bulkUplodEquipments(formData: FormData) {
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_EQUIPMENTS.BULK_UPLOAD_EXCEL, formData)
    return response
  }

  async backupUploadFile(formData: any) {
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_EQUIPMENTS.ROLL_BACK, formData)
    return response
  }

  async getEquipmentsBySection(data: any) {
    const response = await this.apiService.getData(ENDPOINTS.STAND_BY_EQUIPMENTS.EQUIPMENTS_BY_SECTION, data)
    return response
  }

  async addEquipmentToStandBy(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_EQUIPMENTS.ADD, data)
    return response
  }

  async removeEquipmentFromStandBy(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_EQUIPMENTS.REMOVE, data)
    return response
  }

  async standByEquipmentGraph(data: any) {
    const response = await this.apiService.postData(ENDPOINTS.STAND_BY_EQUIPMENTS.GRAPH, data)
    return response
  }
}
