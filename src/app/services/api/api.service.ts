import { Injectable } from '@angular/core';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { environment } from '../../enviornments/enviornments';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private axiosInstance: AxiosInstance;

  constructor(private router: Router) {
    this.axiosInstance = axios.create({
      baseURL: environment.apiUrl, // Set your base API URL here
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
    });

    // Request Interceptor
    this.axiosInstance.interceptors.request.use(config => {
      const authToken = localStorage.getItem('token');
      // Don't set content-type for FormData - axios will set it automatically

      if (config.data instanceof FormData) {
        config.headers['Content-Type'] = 'multipart/form-data';
      } else {
        config.headers['Content-Type'] = 'application/json';
      }

      if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
      }
      return config;
    });

    // Response Interceptor
    this.axiosInstance.interceptors.response.use(
      response => response,
      error => {
        console.log(error.response?.status); // Log the status code if available
        const status = error.response ? error.response.status : null; // Correct key for the status code
        if (status === 401) {
          // Handle unauthorized errors
          localStorage.clear();
          this.router.navigate(['']);
        } else if (status === 404) {
          // Handle not found errors
          console.log('Resource not found');
        } else {
          // Handle other errors
          console.error('An error occurred:', error.message);
        }
    
        return Promise.reject(error); // Forward the error for further handling
      }
    );
    
  }

  // GET request
  async getData(endpoint: string, params: any) {
    const response = await this.axiosInstance.get(endpoint, params);
    return response.data;
  }

  // POST request
  async postData(endpoint: string, data: any) {
    const response = await this.axiosInstance.post(endpoint, data);
    return response.data;
  }

  // PUT request
  async putData(endpoint: string, data: any) {
    const response = await this.axiosInstance.put(endpoint, data);
    return response.data;
  }

  // PUT request
  async patchData(id: any, endpoint: string, data: any) {
    const response = await this.axiosInstance.patch(`${endpoint}/${id}`, data);
    return response.data;
  }

  // DELETE request
  async deleteData(id: any, endpoint: string, data: any) {
    const response = await this.axiosInstance.patch(`${endpoint}/${id}`, data);
    return response.data;
  }

  // Get data as blob
  async getDataAsBlob(endpoint: string, params: any): Promise<Blob> {
    const response = await this.axiosInstance.get(endpoint, {
      params: params,
      responseType: 'blob',
    });
    return response.data;
  }
}
