import * as XLSX from 'xlsx';
import { Injectable } from '@angular/core';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class ExcelExportService {
  private data = [
    {
      PlantName: 'Madukkarai',
      ckname: 'Static & EEL Circuit',
      functionLocation: 'MK.611-BE1',
      equipmentNumber: '1000009674',
      barcode: 'MK.611-BE1',
      name: 'MK.611-BE1',
      equipment: 'Bucket Elevator-1',
      checklistname: 'Elevator',
      Checkpointsname:
        'Check for abnormal noise and vibration; check if buckets are banging against the housing or evidence of back spillage',
      permissibleValue: '',
      permissibleNumber: 'No abnormal noise',
    },
    {
      PlantName: 'Coimbatore',
      ckname: 'Cooling System',
      functionLocation: 'CB.101-CS2',
      equipmentNumber: '1000005678',
      barcode: 'CB.101-CS2',
      name: 'CB.101-CS2',
      equipment: 'Cooling Fan-2',
      checklistname: 'Fan Inspection',
      Checkpointsname:
        'Ensure fan blades are clean; check for unusual noise or loose fittings',
      permissibleValue: '',
      permissibleNumber: 'No loose fittings',
    },
    {
      PlantName: 'Salem',
      ckname: 'Power Circuit',
      functionLocation: 'SM.321-PC3',
      equipmentNumber: '1000012345',
      barcode: 'SM.321-PC3',
      name: 'SM.321-PC3',
      equipment: 'Power Panel-3',
      checklistname: 'Power Panel Inspection',
      Checkpointsname: 'Inspect circuit breakers for proper operation',
      permissibleValue: '',
      permissibleNumber: 'All breakers operational',
    },
  ];

  exportToExcel(fileName: string): void {
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.data);

    // Enable text wrapping for all cells
    Object.keys(ws).forEach((cell) => {
      if (cell.startsWith('!')) return; // Skip meta cells
      ws[cell].s = { alignment: { wrapText: true, vertical: 'top' } };
    });

    // Define column widths
    const wscols = [
      { wch: 20 }, // PlantName
      { wch: 30 }, // ckname
      { wch: 25 }, // functionLocation
      { wch: 20 }, // equipmentNumber
      { wch: 20 }, // barcode
      { wch: 20 }, // name
      { wch: 20 }, // equipment
      { wch: 25 }, // checklistname
      { wch: 50 }, // Checkpointsname
      { wch: 20 }, // permissibleValue
      { wch: 25 }, // permissibleNumber
    ];
    ws['!cols'] = wscols;

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    const excelBuffer: any = XLSX.write(wb, {
      bookType: 'xlsx',
      type: 'array',
    });
    this.saveAsExcelFile(excelBuffer, fileName);
  }

  private saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: 'application/octet-stream',
    });
    saveAs(data, `${fileName}.xlsx`);
  }
}
