import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class ExcelService {
  constructor() {}

  public exportToExcel(data: any[], fileName: string): void {
    // Transform the data if needed
    const transformedData = data.map((item) => ({
      ID: item.id,
      "Admin Added By": item.addByAdminId,
      "Admin Removed By": item.removeByAdminId,
      "Stand By": item.isEquipmentStandBy ? "Yes" : "No",
      Plant: item.plantId,
      Section: item.sectionId,
      "Equipment Name": item.equipment?.equipmentName || "",
      "Condition": item.condition,
      "From Date": item.fromDate,
      "To Date": item.toDate,
      "Reason": item.reason,
    }));

    // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(transformedData);

    // Create a workbook
    const workbook: XLSX.WorkBook = {
      Sheets: { 'Sheet 1': worksheet },
      SheetNames: ['Sheet 1'],
    };

    // Write to Excel file
    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    });

    // Save file
    const blob = new Blob([excelBuffer], {
      type: 'application/octet-stream',
    });
    saveAs(blob, `${fileName}.xlsx`);
  }
}
