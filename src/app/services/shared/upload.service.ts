import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class UploadService {

  constructor(private apiService: ApiService) { }

  async uploadImage(formData: FormData) {
    const response = await this.apiService.postData(ENDPOINTS.UPLOAD.UPLOAD, formData)
    return response
  }

  async fileUploadHistory(formData: any) {
    const response = await this.apiService.postData(ENDPOINTS.UPDATE.UPDATE, formData)
    return response
  }

  async getUploadFiles(data: any) {
    const response = await this.apiService.getData(ENDPOINTS.BULKUPLOADFILE.GET_UPLOAD_FILE, data)
    return response
  }

  async getExcelFile(data: any): Promise<Blob> {
    const response = await this.apiService.getDataAsBlob(ENDPOINTS.BULKUPLOADFILE.GET_DOWNLOAD_FILE, data);
    return response;
  }


}
