import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class PlantService {

  constructor(private apiService: ApiService) { }

  async getPlantTransferRequest(data: any){
    const response = await this.apiService.getData(ENDPOINTS.PLANT.GET_PLANT_TRANSFER_REQUEST,data)
    return response
  }

  async getAllPlants(data: any){
    const response = await this.apiService.getData(ENDPOINTS.PLANT.GET_PLANTS,data)
    return response
  }

  async requestPlantTransfer(data: any){
    const response = await this.apiService.postData(ENDPOINTS.PLANT.REQUEST_PLANT_TRANSFER,data)
    return response
  }

  async approveTransferRequest(data: any){
    const response = await this.apiService.postData(ENDPOINTS.PLANT.PLANT_REQUEST_APPROVE,data)
    return response
  }

  async rejectTransferRequest(data: any){
    const response = await this.apiService.postData(ENDPOINTS.UPDATE.UPDATE,data)
    return response
  }
}
