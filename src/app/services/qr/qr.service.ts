import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class QrService {

  constructor(private apiService: ApiService) {}

   async downloadQrCodes(data: any){
    const response = await this.apiService.postData(ENDPOINTS.QR_CODE.PLANT_BULK_QR,data)
    return response
  }
}
