import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { ENDPOINTS } from '../../core/endpoints';
import { AdminModel } from '../../models/admin.model';
import { signal } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  constructor(private apiService: ApiService) { }

  async meCall() {
    const data = {}
    const response = await this.apiService.postData(ENDPOINTS.ADMIN.ME_CALL, data)
    return response
  }

  async getUsers(data: any) {
    const response = await this.apiService.getData(ENDPOINTS.ADMIN.GET_ADMINS,data)
    return response
  };

  async getAdminAndUsers(data:any){
    const response=await this.apiService.postData(ENDPOINTS.ADMIN.SUPER_ADMINS,data)
    return response
  }

  async registerUser(data: any){
    const response = await this.apiService.postData(ENDPOINTS.ADMIN.REGISTER_USER,data)
    return response
  }

  async updateUser(data:any){
    const response = await this.apiService.postData(ENDPOINTS.UPDATE.UPDATE,data)
    return response
  }

  async checkContact(data:any){
    const response=await this.apiService.postData(ENDPOINTS.ADMIN.CHECK_CONTACT,data);
    return response;
  }
 

  async getDesignation(param: any){
    const response = await this.apiService.getData(ENDPOINTS.ADMIN.GET_DESIGNATION,param)
    return response
  }

  async getDepartment(param: any){
    const response = await this.apiService.getData(ENDPOINTS.ADMIN.GET_DEPARTMENT,param)
    return response
  }

  async getRegisterUserGraph(data: any){
    const response = await this.apiService.postData(ENDPOINTS.ADMIN.REGISTER_USER_GRAPH,data)
    return response
  }

  async weekWiseReportGraph(data: any){
    const response = await this.apiService.postData(ENDPOINTS.ADMIN.WEEKWISE_REPORT_GRAPH,data)
    return response
  }

  async currentDayReportGraph(data: any){
    const response = await this.apiService.postData(ENDPOINTS.ADMIN.CURRENTDAY_REPORT_GRPH,data)
    return response
  }

  async approveUser(data:any){
    const response = await this.apiService.postData(ENDPOINTS.UPDATE.UPDATE,data)
    return response
  }

  async deleteUser(data:any){
    const response = await this.apiService.postData(ENDPOINTS.UPDATE.UPDATE,data)
    return response
  }
}
