import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class AdminPlantSelectionService {

  private selectedPlantSource = new BehaviorSubject<any>(null);
  selectedPlant$ = this.selectedPlantSource.asObservable();

  setSelectedPlant(plantId: any, plantName:any) {
    this.selectedPlantSource.next(plantId);
    localStorage.setItem('currPlantName',plantName);
  }
  get selectedPlantId() {
    return this.selectedPlantSource.value;
  }
}
