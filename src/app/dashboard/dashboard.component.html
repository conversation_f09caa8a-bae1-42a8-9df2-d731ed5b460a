<div class="dashboard-root">
    <app-top-bar></app-top-bar>
    <app-nav-bar></app-nav-bar>
    <div class="row statistic">
        <div class="col-sm" *ngFor="let stat of dataArray">
            <app-statistic [title]="stat.title" [count]="stat.count" [primaryColor]="stat.primaryColor"
                [secondaryColor]="stat.secondaryColor"></app-statistic>
        </div>
    </div>
    <div class="row graphs">
        <div class="col-sm-6">
            <div class="card graph-card">
                <app-register-user-graph></app-register-user-graph>
            </div>
        </div>
        <div class="col-sm-6 ">
            <div class="card graph-card"></div>
        </div>
        <div class="col-sm-6">
            <div class="card graph-card"></div>
        </div>
        <div class="col-sm-6">
            <div class="card graph-card"></div>
        </div>
    </div>
</div>