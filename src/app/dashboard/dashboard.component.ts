import { Component } from '@angular/core';
import { StatisticComponent } from "./statistic/statistic.component";
import { CommonModule } from '@angular/common';  // Import CommonModule
import { TopBarComponent } from '../shared/top-bar/top-bar.component';
import { NavBarComponent } from '../shared/nav-bar/nav-bar.component';
import { RegisterUserGraphComponent } from "./register-user-graph/register-user-graph.component";

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [TopBarComponent, NavBarComponent, StatisticComponent, CommonModule, RegisterUserGraphComponent],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent {
  dataArray = [
    { title: 'Total Resource', count: 2500, primaryColor: "#48459E", secondaryColor:"#5E5BA2" },
    { title: 'Total Register User', count: 40, primaryColor: "#2E6FB4", secondaryColor:"#3B80C8"},
    { title: 'Total Stand By Equipment', count: 1535, primaryColor: "#45497B", secondaryColor:"#575B86"},
    { title: 'Total Provisioned Section', count: 1334, primaryColor: "#5B1979", secondaryColor:"#732F92" },
    { title: 'Total Provision Section', count: 15487, primaryColor: "#1F5077", secondaryColor:"#30628A" }
  ];
}
