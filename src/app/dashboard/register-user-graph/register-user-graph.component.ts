import { Component } from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTitleSubtitle,
  NgApexchartsModule
} from 'ng-apexcharts';



@Component({
  selector: 'app-register-user-graph',
  standalone: true,
  imports: [NgApexchartsModule],
  templateUrl: './register-user-graph.component.html',
  styleUrl: './register-user-graph.component.css'
})
export class RegisterUserGraphComponent {
  public chartOptions: {
    series: ApexAxisChartSeries;
    chart: ApexChart;
    xaxis: ApexXAxis;
    dataLabels: ApexDataLabels;
    colors: string[];
    title: ApexTitleSubtitle;
  };

  constructor() {
    this.chartOptions = {
      series: [
        {
          name: 'Sales',
          data: [10, 41, 35, 51, 49, 62, 69, 91, 148]
        }
      ],
      chart: {
        type: 'bar',
        height: 350,
      },
      colors: [
        "#008FFB",
        "#00E396",
        "#FEB019",
        "#FF4560",
        "#775DD0",
        "#546E7A",
        "#26a69a",
        "#D10CE8"
      ],
      title: {
        text: 'Register User (Month Wise)',
        align: 'left'
      },
      xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep']
      },
      dataLabels: {
        enabled: true
      },
    };
  }
}
