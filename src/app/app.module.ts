import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HttpClientModule } from '@angular/common/http';
import { HeaderComponent } from './common/header/header.component';
import { FooterComponent } from './common/footer/footer.component';
import { LoginComponent } from './common/login/login.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { BreadcrumbComponent } from './common/breadcrumb/breadcrumb.component';
import { ManageUserComponent } from './pages/manage-user/manage-user.component';
import { MrStatusComponent } from './pages/mr-status/mr-status.component';
import { StandbyEquipmentComponent } from './pages/standby-equipment/standby-equipment.component';
import { SectionManagementComponent } from './pages/section-management/section-management.component';
import { HomeComponent } from './pages/home/<USER>';
import { NgBtnComponent } from './pages/ng-btn/ng-btn.component';
import { FormsModule, NgSelectOption, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { CommonModule, DatePipe } from '@angular/common';
import { CustomModalComponent } from './common/custom-modal/custom-modal.component';
import { ToastMessageComponent } from './common/toast-message/toast-message.component';
import { UserRegistryComponent } from './pages/user-registry/user-registry.component';
import { ProvisionSectionComponent } from './pages/provision-section/provision-section.component';
import { WorkSummaryComponent } from './pages/work-summary/work-summary.component';
import { SelectTileComponent } from './pages/provision-section/select-user-tile/select-tile.component';
import { CurrentDayReportComponent } from './pages/work-summary/current-day-report/current-day-report.component';
import { WeeklyReportComponent } from "./pages/work-summary/weekly-report/weekly-report.component";
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgOtpInputModule } from 'ng-otp-input';
import { UnassignSectionTileComponent } from './pages/provision-section/unassign-section-tile/unassign-section-tile.component';
import { AssignSectionTileComponent } from './pages/provision-section/assign-section-tile/assign-section-tile.component';
import { WorkSummaryGraphComponent } from "./pages/dashboard/work-summary-graph/work-summary-graph.component";
import { NgxSpinnerModule } from 'ngx-spinner';
import { SpinnerService } from './services/shared/spinner.service';
import { LoadingModalComponent } from './common/loading/loading-modal/loading-modal.component';
import { SecurityManagementComponent } from './pages/security-management/security-management.component';
import { SearchFilterComponent } from './common/search-filter/search-filter.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule, MatIconButton } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { CdkAccordionModule } from '@angular/cdk/accordion';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AdvisoryManagementComponent } from './pages/advisory-management/advisory-management.component';
import { PdfComponent } from './common/pdf/pdf.component';
import { SafeURLPipe } from './shared/pipes/safe-url.pipe';
import { SecurityDashboardComponent } from './pages/security-dashboard/security-dashboard.component';
import { StatisticComponent } from './dashboard/statistic/statistic.component';
import { FeedbackManagementComponent } from './pages/security-dashboard/feedback-management/feedback-management.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { IncidentReportComponent } from './pages/security-dashboard/incident-report/incident-report.component';
import { LostAndFoundComponent } from './pages/security-dashboard/lost-and-found/lost-and-found.component';
import { AdTableComponent } from './common/ad-table/ad-table.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSortModule } from '@angular/material/sort';
import { SosSearchFilterComponent } from './common/sos-search-filter/sos-search-filter.component';
import { SosCallHistoryComponent } from './pages/security-dashboard/sos-call-history/sos-call-history.component';
import { IncidentReportGraphComponent } from './pages/security-dashboard/incident-report-graph/incident-report-graph.component';
import { LostAndFoundGraphComponent } from './pages/security-dashboard/lost-and-found-graph/lost-and-found-graph.component';
import { CallHistoryGraphComponent } from './pages/security-dashboard/call-history-graph/call-history-graph.component';
import { ActiveApplicationUsersGraphComponent } from './pages/security-dashboard/active-application-users-graph/active-application-users-graph.component';
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import { BulkUploadExcelComponent } from './pages/bulk-upload-excel/bulk-upload-excel.component';

@NgModule({
  declarations: [
    AppComponent,
    HeaderComponent,
    FooterComponent,
    LoginComponent,
    BreadcrumbComponent,
    ManageUserComponent,
    MrStatusComponent,
    StandbyEquipmentComponent,
    SectionManagementComponent,
    PdfComponent,
    HomeComponent,
    NgBtnComponent,
    CustomModalComponent,
    ToastMessageComponent,
    UserRegistryComponent,
    ProvisionSectionComponent,
    WorkSummaryComponent,
    SelectTileComponent,
    UnassignSectionTileComponent,
    AssignSectionTileComponent,
    LoadingModalComponent,
    SecurityManagementComponent,
    SearchFilterComponent,
    AdvisoryManagementComponent,
    PdfComponent,
    SafeURLPipe,
    SecurityDashboardComponent,
    FeedbackManagementComponent,
    IncidentReportComponent,
    LostAndFoundComponent,
    AdTableComponent,
    SosSearchFilterComponent,
    SosCallHistoryComponent,
    IncidentReportGraphComponent,
    LostAndFoundGraphComponent,
    CallHistoryGraphComponent,
    ActiveApplicationUsersGraphComponent,
    BulkUploadExcelComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    NgSelectModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    WeeklyReportComponent,
    CurrentDayReportComponent,
    BrowserAnimationsModule,
    NgOtpInputModule,
    WorkSummaryGraphComponent,
    MatAutocompleteModule,
    MatButtonModule,
    MatInputModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    CdkAccordionModule,
    MatIconModule,
    MatTooltipModule,
    StatisticComponent,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatSortModule,
    ChartComponent,
    NgApexchartsModule
  ],
  providers: [DatePipe, SpinnerService, SafeURLPipe,],
  bootstrap: [AppComponent]
})
export class AppModule { }
