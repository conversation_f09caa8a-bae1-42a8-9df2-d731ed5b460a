export const ENDPOINTS = {
  AUTH: {
    SEND_OTP: '/admins/wbisuperadmin-otp',
    VERIFY_OTP: '/admins/verify-otp',
    LOGOUT: '/admins/logout',
  },

  UPDATE: {
    UPDATE: '/update',
  },

  ADMIN: {
    ME_CALL: '/admins/admin-me',
    GET_ADMINS: '/admins',
    SUPER_ADMINS:'/admins/get-user',
    ASSIGN_SECTIONS: '/admins/wbiuser-provisionsection',
    REGISTER_USER: '/admins/wbiuser-register',
    GET_DESIGNATION: '/designation',
    GET_DEPARTMENT: '/department',
    REGISTER_USER_GRAPH: '/admins/monthwiseuser-graph',
    WEEKWISE_REPORT_GRAPH: '/admins/dayWise-currentweek-graph',
    CURRENTDAY_REPORT_GRPH: '/admins/currentdayreport-graph',
    CHECK_CONTACT:'/admins/check-contactNumber'
  },

  SECTIONS: {
    GET_ALL_SECTIONS: '/wbi-section',
  },

  MR_STATUS: {
    GET_MR_STATUS: '/wbi-MRrequest'
  },

  STAND_BY_EQUIPMENTS: {
    GET_STANDBY_EQUIPMENTS: '/wbi-standby',
    BULK_UPLOAD_EXCEL: '/wbi-equipment/bulkuploadxl',
    ROLL_BACK: '/wbi-equipment/role-back',
    EQUIPMENTS_BY_SECTION: '/wbi-equipment',
    ADD: 'wbi-standby/add',
    REMOVE: '/wbi-standby/remove',
    GRAPH: '/wbi-standby/standby-graph'
  },

  STAND_BY_SECTION: {
    ADD: 'wbi-standby-section/add',
    REMOVE: 'wbi-standby-section/remove',
  },

  QR_CODE: {
    PLANT_BULK_QR: '/wbi-qr-code/plant-pdf',
    GET_PLANT_QR: '/qr-code/plant-pdf',
  },

  UPLOAD: {
    UPLOAD: '/common/upload'
  },

  CLUSTER: {
    GET_CLUSTERS: 'cluster',
  },

  SECURITY_MODULE: {
    GET_SECURITY_DATA: 'sos',
    REGISTER_NEW_PLANT: 'sos',
    EDIT_PLANT: 'update'
  },
  PLANT: {
    GET_PLANT_TRANSFER_REQUEST: '/plant-transfer-request',
    GET_PLANTS: '/plant',
    REQUEST_PLANT_TRANSFER: '/plant-transfer-request/add',
    PLANT_REQUEST_APPROVE: '/plant-transfer-request/approve'
  },
  ADVISORY_MODULE: {
    GET_FILES: 'advisory',
    UPLOAD_FILES: 'advisory',
    UPDATE_FILE: 'update',
    DELETE_FILE: 'delete'
  },
  SECURITY_DASHBOARD: {
    GET_LOST_AND_FOUND: 'lost-found',
    UPDATE: 'update',
    GET_FEEDBACK: 'feedback',
    SECURITY_COUNT: 'admins/security-count',
    ADD_LOST_AND_FOUND: 'lost-found/add',
    GET_INCIDENT_REPORT: '/security-incident',
    ADD_INCIDENT_REPORT: '/security-incident/add',
    UPDATE_INCIDENT_REPORT: '/security-incident/update',
    DELETE_INCIDENT_REPORT: '/security-incident/delete',
    GET_SOS_CALL_HISTORY: '/callhistory',
    DELETE_CALL_HISTORY: '/callhistory/delete',
    LOST_AND_FOUND_GRAPH: 'admins/security/lost-found-count',
    PLANTWISE_GRAPH: 'admins/security/plantwise/graph',
    ACTIVE_USER: 'admins/security/active-user'
  },

  BULKUPLOADFILE: {
    GET_UPLOAD_FILE: 'wbi-backup-checkpoint',
    FILEUPLOADHISTORY: '/wbi-backup-checkpoint',
    GET_DOWNLOAD_FILE: '/wbi-backup-checkpoint/custom-filename-download'
  }
}