<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>
<p class="fw-bold">{{isSuperAdmin? 'Register': 'User Register'}}</p>
<br />
<div class="outer-container">
  <form [formGroup]="registerForm" (ngSubmit)="register()" novalidate>
    <div class="row g-4 p-4">
      <!-- Create two columns: left for form fields, right for profile -->
      <div class="col-md-9">
        <div class="row g-4">
          <!-- First Name -->
          <div class="col-md-3 form-group">
            <label for="firstName">First Name</label>
            <input
              type="text"
              class="form-control form-control-sm"
              formControlName="firstName"
              id="firstName"
              placeholder="Enter first name"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('firstName')?.invalid &&
                  registerForm.get('firstName')?.touched
              }"
            />
            <div
              *ngIf="
                registerForm.get('firstName')?.touched &&
                registerForm.get('firstName')?.invalid
              "
              class="invalid-feedback"
            >
              <div *ngIf="registerForm.get('firstName')?.errors?.['required']">
                First Name is required.
              </div>
              <div *ngIf="registerForm.get('firstName')?.errors?.['pattern']">
                First Name must be alphabetic and 1-15 characters long.
              </div>
            </div>
          </div>

          <!-- Last Name -->
          <div class="col-md-3 form-group">
            <label for="lastName">Last Name</label>
            <input
              type="text"
              class="form-control form-control-lg"
              formControlName="lastName"
              id="lastName"
              placeholder="Enter last name"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('lastName')?.invalid &&
                  registerForm.get('lastName')?.touched
              }"
            />
            <div
              *ngIf="
                registerForm.get('lastName')?.touched &&
                registerForm.get('lastName')?.invalid
              "
              class="invalid-feedback"
            >
              <div *ngIf="registerForm.get('lastName')?.errors?.['required']">
                Last Name is required.
              </div>
              <div *ngIf="registerForm.get('lastName')?.errors?.['pattern']">
                Last Name must be alphabetic and 1-15 characters long.
              </div>
            </div>
          </div>

          <!-- Gender -->
          <div class="col-md-3 form-group">
            <label for="gender">Gender</label>
            <select
              class="form-control form-control-lg"
              formControlName="gender"
              id="gender"
              placeholder="Gender"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('gender')?.invalid &&
                  registerForm.get('gender')?.touched
              }"
            >
              <option value="">Select Gender</option>
              <option value="1">Male</option>
              <option value="0">Female</option>
              <option value="2">Other</option>
            </select>
            <div
              *ngIf="
                registerForm.get('gender')?.touched &&
                registerForm.get('gender')?.invalid
              "
              class="invalid-feedback"
            >
              <div *ngIf="registerForm.get('gender')?.errors?.['required']">
                Gender is required.
              </div>
            </div>
          </div>

          <!-- Mobile Number -->
          <div class="col-md-3 form-group">
            <label for="mobile">Mobile Number</label>
            <input
              type="text"
              class="form-control form-control-lg"
              formControlName="contactNumber"
              id="mobile"
              placeholder="Enter mobile number"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('contactNumber')?.invalid &&
                  registerForm.get('contactNumber')?.touched
              }"
            />
            <div
              *ngIf="
                registerForm.get('contactNumber')?.touched &&
                registerForm.get('contactNumber')?.invalid
              "
              class="invalid-feedback"
            >
              <div
                *ngIf="registerForm.get('contactNumber')?.errors?.['required']"
              >
                Mobile Number is required.
              </div>
              <div
                *ngIf="registerForm.get('contactNumber')?.errors?.['pattern']"
              >
                Enter a valid mobile number.
              </div>
            </div>
          </div>

          <!-- Date of Birth -->
          <div class="col-md-3 form-group">
            <label for="dob">Date of Birth</label>
            <input
              type="date"
              class="form-control form-control-lg"
              formControlName="dob"
              id="dob"
              max="{{ maxDate }}"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('dob')?.invalid &&
                  registerForm.get('dob')?.touched
              }"
            />
            <div
              *ngIf="
                registerForm.get('dob')?.touched &&
                registerForm.get('dob')?.invalid
              "
              class="invalid-feedback"
            >
              <div *ngIf="registerForm.get('dob')?.errors?.['required']">
                Date of Birth is required.
              </div>
            </div>
          </div>

          <!-- Role -->
          <div class="col-md-3 form-group">
            <label for="role">Role</label>
            <select
              class="form-control form-control-lg"
              formControlName="wbiRoleId"
              id="role"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('wbiRoleId')?.invalid &&
                  registerForm.get('wbiRoleId')?.touched
              }"
              [(ngModel)]="selectedRole"
            >
              <option value="">Select Role</option>
              <option value="3">User</option>
              <option value="2" *ngIf="isSuperAdmin">Plant Admin</option>
            </select>
            <div
              *ngIf="
                registerForm.get('wbiRoleId')?.touched &&
                registerForm.get('wbiRoleId')?.invalid
              "
              class="invalid-feedback"
            >
              <div *ngIf="registerForm.get('wbiRoleId')?.errors?.['required']">
                Role is required.
              </div>
            </div>
          </div>

          <div class="col-md-3 form-group" *ngIf="selectedRole == 3  && isSuperAdmin">
            <label for="Plant">Select Plant</label>
            <select
            [ngClass]="{
              'is-invalid': showerr2
            }"
              class="form-control form-control-lg"
              (change)="getSelectValue($event)"
            >
              <option value="">Select Plant</option>
              <option
                *ngFor="let plant of plantsForUser"
                [value]="plant | json"
              >
                {{ plant.name }}
              </option>
            </select>
            <div *ngIf="showerr2" class="invalid-feedback">
              <div>Plant is required.</div>
            </div>
          </div>


          <!-- Department -->
          <div class="col-md-3 form-group">
            <label for="department">Department</label>
            <select
              class="form-control form-control-lg"
              formControlName="wbiDepartmentId"
              id="department"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('wbiDepartmentId')?.invalid &&
                  registerForm.get('wbiDepartmentId')?.touched
              }"
            >
              <option value="">Select Department</option>
              <option *ngFor="let dept of departments" [value]="dept.id">
                {{ dept.title }}
              </option>
            </select>
            <div
              *ngIf="
                registerForm.get('wbiDepartmentId')?.touched &&
                registerForm.get('wbiDepartmentId')?.invalid
              "
              class="invalid-feedback"
            >
              <div
                *ngIf="registerForm.get('wbiDepartmentId')?.errors?.['required']"
              >
                Department is required.
              </div>
            </div>
          </div>

          <!-- Email -->
          <div class="col-md-3 form-group">
            <label for="email">Email Id</label>
            <input
              type="email"
              class="form-control form-control-lg"
              formControlName="email"
              id="email"
              placeholder="Enter email"
              [ngClass]="{
                'is-invalid':
                  registerForm.get('email')?.invalid &&
                  registerForm.get('email')?.touched
              }"
            />
            <div
              *ngIf="
                registerForm.get('email')?.touched &&
                registerForm.get('email')?.invalid
              "
              class="invalid-feedback"
            >
              <div *ngIf="registerForm.get('email')?.errors?.['required']">
                Email is required.
              </div>
              <div *ngIf="registerForm.get('email')?.errors?.['email']">
                Enter a valid email address.
              </div>
              <div *ngIf="registerForm.get('email')?.hasError('adaniDomain')">
                Email must belong to the adani.com domain and must contain one
                alphabet
              </div>
            </div>
          </div>
          
          <div class="col-md-3 form-group" *ngIf="selectedRole == 2 && isSuperAdmin">
            <label for="plantIds" class="form-label">Select Plants</label>
            <ng-select
            [ngClass]="{
              'is-invalid': showerr1
            }"
            style="margin-top: 0.5px;"
            class="custom-ng-select"
              [items]="plantsForAdmin"
              bindLabel="name"
              bindValue="id"
              [multiple]="true"
              placeholder="Select plants"
              id="plantIds"
              [closeOnSelect]="false"
              [clearable]="true"
              (change)="getSelectValue($event)"
            >
            </ng-select>
            <div *ngIf="showerr1" class="invalid-feedback">
              <div>Please select atleast one plant.</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Upload Section - Right Side -->
      <div class="col-md-3">
        <div class="d-flex flex-column align-items-center">
          <div class="avatar-preview mb-3">
            <img
              *ngIf="profilePreview"
              [src]="profilePreview"
              alt="Profile Preview"
              class="rounded-circle"
            />
            <div
              *ngIf="!profilePreview"
              class="default-avatar rounded-circle bg-secondary text-white d-flex justify-content-center align-items-center"
              style="width: 120px; height: 120px"
            >
              Preview
            </div>
          </div>
          <input
            type="file"
            class="form-control form-control-lg"
            id="profile"
            #fileInput
            (change)="onProfileChange($event)"
            accept="image/*"
          />
        </div>
      </div>
    </div>

    <!-- Buttons Section -->
    <div class="row mt-3 ms-2">
      <div class="d-flex gap-3">
        <button type="submit" id="bt_approve" class="button-submit button-left">
          <span class="ps-5">Submit</span>
          <img
            alt=""
            src="../../../assets/svg/right-arrow.svg"
            class="left-arrow"
          />
        </button>
        <button
          type="button"
          id="bt_reject"
          class="button-back button-left"
          (click)="resetForm()"
        >
          <span class="ps-5">Reset</span>
          <img
            alt=""
            src="../../../assets/svg/Edit-User.svg"
            class="left-arrow"
          />
        </button>
      </div>
    </div>
  </form>
</div>
<div style="height: 100px"></div>
