.outer-container{
    background-color: white;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding-bottom: 50px;
}
.form-group {
    label{
        margin-bottom: 10px;
    }
    input{
        height: 50px;
    }
}
.buttons-for-action {
    button {
      width: 140px;
      height: 34px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #076fb61a;
      border: 0;
      border-radius: 10px;
      font-weight: 600;
      font-size: 13px;
      > img {
        height: 100%;
        width: 20%;
      }
      > span {
        padding: 0 3px;
      }
    }
  }
  .button-left {
    display: flex;
    // justify-content: center;
    align-items: center;
    > span {
      //  justify-self: center;
      margin: 0 auto;
    }
  
    > .left-arrow {
      width: 61px;
      height: 61px;
      position: relative;
      // left: 15px;
    }
  }
  select.form-control {
    height: calc(2.25rem + 14px); /* Match with the input field height */
    padding: 0.375rem 0.75rem; /* Match the padding of input fields */
    font-size: 1rem; /* Optional: Adjust font size to match input fields */
  }
  .rounded-circle{
    width: 120px;
    height: 120px;
  }
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  


  ::ng-deep .ng-select.custom-ng-select .ng-select-container {
    // font-size: 1rem;
    // border: 1px solid #ced4da;
    // border-radius: 0.375rem;
    // height: 100px;
    // // padding: 0 0.75rem;
    // width:280px;
    // background-color: #fff;
    // box-shadow: none;
    // display: flex;
    // align-items: center;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    width: 300px;
    background-color: #fff;
    box-shadow: none;
    display: flex;
    align-items: flex-start; /* changed from center */
    flex-wrap: wrap;
    padding: 0.25rem; 
  }
  
  ::ng-deep .ng-select.custom-ng-select .ng-value-container {
    max-height: 64px;
    overflow-y: auto;
    padding-top: 0;
    padding-bottom: 0;
  }
  
  ::ng-deep .ng-select.custom-ng-select .ng-value {
    font-size: 0.85rem; /* Matches input text */
    margin: 0 4px 0 0;
  }
  
  ::ng-deep .ng-select.custom-ng-select .ng-value-label {
    line-height: 0.5;
    font-weight: bold;
  }
  
  ::ng-deep .ng-select.custom-ng-select .ng-input input {
    font-size: 0.85rem;
    margin: 0;
    padding: 0;
  }
  
  ::ng-deep .ng-select.custom-ng-select .ng-dropdown-panel .ng-option-label {
    font-size: 1rem;
    line-height: 1.5;
  }
  ::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{
    top: 22%;
    // padding-bottom: 5px;
    padding-left: 0px;
    font-size: 14px;
  }
  
  ::ng-deep .ng-select .ng-select-container .ng-value-container .ng-placeholder {
    color: #595C5F;
  }
  ::ng-deep .ng-select.custom-ng-select .ng-dropdown-panel .ng-option-label{
    font-size: 12px;
    font-weight: 600;
  }
  
  ::ng-deep .ng-select.custom-ng-select .ng-value-label{
    font-size: 12px;
  }
  