import { Component, ViewChild } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdminService } from '../../services/admin/admin.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { DatePipe } from '@angular/common';
import { UploadService } from '../../services/shared/upload.service';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { Router } from '@angular/router';
import { adaniDomainValidator } from '../../core/utilities/adani-email-validator';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';
import { PlantService } from '../../services/plant/plant.service';

@Component({
  selector: 'app-user-registry',
  templateUrl: './user-registry.component.html',
  styleUrl: './user-registry.component.scss',
})
export class UserRegistryComponent {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  maxDate: string = new Date().toISOString().split('T')[0];

  successToast: boolean = false;
  errorToast: boolean = false;
  toastMsg: any = '';
  registerForm: FormGroup;
  profilePreview: any;
  departments: any = [];
  designations: any = [];
  adminId: any;
  plantId: any;
  selectedFile: File | null = null;
  clickLoading: any;
  ActiveURL: any;
  fileInput?: HTMLInputElement | null;
  selectedplantId: any;
  isSuperAdmin: boolean = false;
  plantsForUser: any = [];
  plantsForAdmin: any = [];
  selectedPlantForAdmin: any;
  selectedPlantForUser: any;
  selectedRole: any = [];
  showerr1: boolean = false;
  showerr2: boolean = false;

  constructor(
    private breadcrumbservice: BreadcrumbService,
    private fb: FormBuilder,
    private adminService: AdminService,
    private uploadService: UploadService,
    private router: Router,
    private datePipe: DatePipe,
    private adminPlantSelection: AdminPlantSelectionService,
    private plantService: PlantService
  ) {
    this.ActiveURL = this.router.url;
    this.registerForm = this.fb.group({
      firstName: [
        '',
        [
          Validators.required,
          Validators.pattern('^[A-Za-z]+( [A-Za-z]+)*$'), // Restricts length to 1-20 characters
        ],
      ],
      lastName: [
        '',
        [
          Validators.required,
          Validators.pattern('^[A-Za-z]+( [A-Za-z]+)*$'), // Restricts length to 1-20 characters
        ],
      ],
      gender: ['', Validators.required],
      contactNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      dob: ['', Validators.required],
      wbiRoleId: ['', Validators.required],
      wbiDepartmentId: ['', Validators.required],
      email: ['', [Validators.required, adaniDomainValidator()]],
    });
  }

  ngOnInit() {
    const role = localStorage.getItem('userRole');
    if (role == 'superadmin') this.isSuperAdmin = true;

    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.fileInput = document.getElementById(
            'profile'
          ) as HTMLInputElement;
          this.breadcrumbservice.setBreadcrumbUrl(this.ActiveURL);
          this.getAdminDetail();
          this.getDepartment();
          this.getDesignation();
        });
    } else {
      this.fileInput = document.getElementById('profile') as HTMLInputElement;
      this.breadcrumbservice.setBreadcrumbUrl(this.ActiveURL);
      this.getAdminDetail();
      this.getDepartment();
      this.getDesignation();
    }
    this.getPlants();
  }

  getPlants() {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: ['enabled||eq||true'],
    };
    const param = createAxiosConfig(data);
    this.plantService.getAllPlants(param).then((response) => {
      this.plantsForAdmin = response.data;
      this.plantsForUser = response.data;
      // console.log('get plants', response.data);
    });
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    console.log(currentUser);
    // this.plantsForUser = currentUser.plant;
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
  }

  getDepartment() {
    const data = {
      page: 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.adminService.getDepartment(param).then((response: any) => {
      this.departments = response.data;
    });
  }

  getDesignation() {
    const data = {
      page: 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.adminService.getDesignation(param).then((response) => {
      this.designations = response.data;
    });
  }

  getSelectValue(event: any) {
    if (this.selectedRole == 2) {
      this.selectedPlantForAdmin = event;
    } else {
      const selectedValue = JSON.parse(event.target.value);
      console.log(selectedValue.id);
      this.selectedPlantForUser = selectedValue.id;
    }
  }

  async register() {
    let plant: any = [];
    let plantIds: any = [];

    if (this.isSuperAdmin) {
      if (this.selectedRole == 2) {
        if (this.selectedPlantForAdmin.length == 0) {
          this.showerr1 = true;
          setTimeout(() => {
            this.showerr1 = false;
          }, 4000);
          return;
        } else {
          this.selectedPlantForAdmin.forEach((item: any) => {
            plantIds.push(item.id);
            let a = {
              id: item.id,
            };
            plant.push(a);
          });
        }
      } else {
        if (!this.selectedPlantForUser || this.selectedPlantForUser=='') {
          this.showerr2 = true;
          setTimeout(() => {
            this.showerr2 = false;
          }, 4000);
          return;
        } else {
          plantIds = [this.selectedPlantForUser];
          plant = [{ id: this.selectedPlantForUser }];
        }
      }
    }

    Object.keys(this.registerForm.controls).forEach((key) => {
      const control = this.registerForm.get(key);
      if (control && typeof control.value === 'string') {
        control.setValue(control.value.trim(), { emitEvent: false });
      }
    });
    if (this.registerForm.valid) {
      this.clickLoading = true;
      var formValue = this.registerForm.value;
      formValue['plantIds'] = plantIds.length > 0 ? plantIds : [this.plantId];
      formValue['plant'] = plant.length > 0 ? plant : [{ id: this.plantId }];
      formValue['wbiStatus'] = 1;
      formValue['applicationId'] = 2;
      formValue['dob'] = this.datePipe.transform(
        formValue['dob'],
        'yyyy-MM-dd HH:mm:ss.SSSSSS'
      );
      formValue['wbiRoleId'] = parseInt(formValue['wbiRoleId']);
      formValue['adminsRoleId'] = parseInt(formValue['wbiRoleId']);
      formValue['adminsRole'] = { id: parseInt(formValue['wbiRoleId']) };
      formValue['wbiDepartmentId'] = parseInt(formValue['wbiDepartmentId']);
      if (this.selectedFile) {
        formValue['profilePicture'] = await this.uploadProfilePic();
      }
      console.log(this.registerForm.value);
      this.adminService.registerUser(formValue).then((response) => {
        this.clickLoading = false;
        if (response.responseCode == 200) {
          this.toast.showSuccessToast('user addedd successfully');
          this.resetForm();
        } else {
          let message = response?.message
            ? response.message
            : 'Error! creating user';
          this.toast.showErrorToast(message);
        }
      });
    } else {
      Object.keys(this.registerForm.controls).forEach((field) => {
        const control = this.registerForm.get(field);
        if (control) {
          control?.markAsTouched();
        }
      });
    }
  }

  async uploadProfilePic(): Promise<string> {
    let url = '';
    if (this.selectedFile) {
      const formData = new FormData();
      // Append the selected file to form data
      formData.append('file', this.selectedFile);

      try {
        // Await the service call to upload the image
        url = await this.uploadService.uploadImage(formData);
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }
    return url;
  }

  resetForm() {
    this.selectedFile = null;
    this.profilePreview = null;
    this.registerForm.reset();
    if (this.fileInput) {
      this.fileInput.value = '';
    }
    this.registerForm.reset({
      gender: '',
      wbiRoleId: '',
      wbiDepartmentId: '',
    });
  }

  onProfileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (e.target?.result) {
          this.profilePreview = e.target.result as string; // Set the result as the preview URL
        }
      };
      reader.readAsDataURL(this.selectedFile); // Read the file as a data URL
      //this.uploadService.uploadImage(formData)
    } else {
      console.error('No file selected.');
    }
  }
}
