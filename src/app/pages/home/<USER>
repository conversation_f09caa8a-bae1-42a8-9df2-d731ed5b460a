.header {
    top: 0px;
    width: 100%;
}

.footer {
    bottom: 0px;
    position: fixed;
    width: 100%;
}

.tab-menu {
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
    height: auto;
    padding-left: 22px;
    padding: 0%;
}

a {
    text-decoration: none;
}

ul li a {
    font-size: 14px;
    font-weight: 500;
}

.dropdown-divider {
    padding: 0;
    margin: 0;
}

.menu-label {
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    padding: 4px 5px; /* Adjust padding for better spacing */
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s ease, color 0.3s ease;
}

.menu-label:hover {
    background-color: #fff;
    color: #0B74B0; /* Hover effect */
    width: 100%;
}

.menu-label.active {
    color: #0B74B0;
    background-color: #fbfbfb;
    text-align: center; /* Adjusted for flexibility */
    font: 600 13px "Adani", sans-serif;
    padding: 6px 14px; /* Adjusted padding */
    border-radius: 5px; /* Rounded corners */
    line-height: 1.5;
    width: 100%;
}

/* Offcanvas styles */
.offcanvas {
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
    width: 300px; /* Adjust the width of the drawer */
    transition: transform 0.3s ease-in-out;
}

/* Offcanvas drawer open state */
.offcanvas.show {
    transform: translateX(0);
}

/* Close drawer on small screens */
.offcanvas-body {
    padding: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tab-menu {
        gap: 5px; /* Reduce gap for smaller screens */
        padding: 5px 10px;
    }

    .menu-label {
        font-size: 12px; /* Smaller text size for mobile */
        padding: 8px 15px; /* Adjust padding for smaller screens */
    }

    .navbar-toggler {
        display: block;
    }

    .navbar-collapse {
        display: none;
    }

    .offcanvas {
        width: 200px; /* Smaller width on mobile */
    }
}

@media (max-width: 480px) {
    .menu-label {
        flex: 1 0 100%; /* Make each label occupy full width */
        text-align: center;
    }
}