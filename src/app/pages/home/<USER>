import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AdminService } from '../../services/admin/admin.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'], // Fixed typo here
})
export class HomeComponent implements OnInit {
  active: string | undefined;
  isDrawerOpen: any;
  isSuperAdmin: boolean = false;

  constructor(private router: Router, private adminService: AdminService) { }

  ngOnInit() {
    const savedRoutes =  sessionStorage.getItem('activeRoute');
    const role = localStorage.getItem('userRole');
    if (role == 'superadmin') this.isSuperAdmin = true;
    if(savedRoutes){
      this.active = savedRoutes;
      this.router.navigate([savedRoutes]);
    }
    else{
      this.active = this.router.url;
    }
    this.meCall(); // Ensure asynchronous handling
  }

  activateNav(url: string) {
    this.active = url;
    sessionStorage.setItem('activeRoute', url);
    this.router.navigate([url]);
  }

  async meCall() {
    try {
      const response = await this.adminService.meCall();
      // console.log('Me call successful:', JSON.stringify(response));
    } catch (error) {
      // console.error('Error during meCall:', error);
    }
  }

  getUrlFromBreadcrmb(value: any) {
    if (value && typeof value === 'string') {
      // this.active = value;
      // this.router.navigate([value]);
      const savedRoutes =  sessionStorage.getItem('activeRoute')
      if(savedRoutes){
        this.active = savedRoutes;
        this.router.navigate([savedRoutes]);
      }
      else{
        this.active = this.router.url;
        this.router.navigate([value]);
      }
    } else {
      console.error('Invalid breadcrumb value:', value);
    }
  }
  toggleDrawer() {
    this.isDrawerOpen = !this.isDrawerOpen;
  }
}
