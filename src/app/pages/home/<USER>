<div class="header">
  <app-header [showElement]="true"></app-header>
</div>

<nav class="navbar navbar-expand-lg tab-menu navbar-light bg-light">
  <button class="navbar-toggler" type="button" (click)="toggleDrawer()" aria-controls="drawer" aria-expanded="false"
    aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"><i class="fa fa-navicon" style="color:#fff; font-size:28px;"></i></span>
  </button>
  <!-- Off-canvas drawer (side menu) -->
  <div class="offcanvas offcanvas-start" [class.show]="isDrawerOpen" tabindex="-1" id="drawer"
    aria-labelledby="drawerLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="drawerLabel" style="color: white;">Navigation</h5>
      <button type="button" class="btn-close text-reset" (click)="toggleDrawer()"></button>
    </div>
    <div class="offcanvas-body">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">Dashboard</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">Provision Section</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">Work Summary</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">{{isSuperAdmin? 'Registry':'User Registry'}}</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">{{isSuperAdmin? 'Manage Roles': 'Manage User'}}</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">MR Status</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">Stand-by Equipment</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">Section Management</label>
          </a>
        </li>
        <!-- <li>
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" id="Standby" class="nav-link"><label
              class="menu-label" [class.active]="active === '/home/<USER>'">Security Management</label>
          </a>
        </li>
        <li>
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" id="Standby" class="nav-link"><label
              class="menu-label" [class.active]="active === '/home/<USER>'">Advisory Management</label>
          </a>
        </li> -->
        <li>
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" id="Standby"
            class="nav-link"><label class="menu-label" [class.active]="active?.includes('security-dashboard')">Security
              Dashboard</label>
          </a>
        </li>
        <li class="nav-item">
          <a href="javascript:void(0);" (click)="activateNav('/home/<USER>')" class="nav-link">
            <label class="menu-label" [class.active]="active === '/home/<USER>'">Bulk Upload Excel</label>
          </a>
        </li>
      </ul>
    </div>
  </div>
</nav>
<div class="m-4">
  <app-breadcrumb (getUrl)="getUrlFromBreadcrmb($event)"></app-breadcrumb>
  <router-outlet></router-outlet>
</div>

<div class="footer">
  <app-footer></app-footer>
</div>