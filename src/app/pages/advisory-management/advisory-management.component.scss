.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  
.tab-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
  
  .tab-headers {
    display: flex;
    justify-content: center;
    border: 1px solid #0B74B0;
    border-radius: 20px;
    background-color: white;
    color: #0B74B0;
  }
  
  .tab-header {
    padding: 10px 20px;
    cursor: pointer;
    width: 200px;
    transition: transform 0.3s,
  }
  
  .tab-header.active {
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
    color: white;
    border-radius: 20px;
    font-weight: bold;
  }

  .button-right {
    display: flex;
    align-items: center;
  
    >span {
      margin: 0 auto;
    }
  
    >.right-arrow {
      width: 61px;
      height: 50px;
      position: relative;
    }
  }

  .outer-container {
    height: 400px;
    overflow-y:scroll;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 10px;
    background-color: #f9f9f9;
  
    .user-list {
      max-height: 100%;
    }
  
    .user-item {
      display: flex;
      align-items: center;
      padding: 10px;
      background-color: white;
      border-radius: 10px;
      margin-bottom: 10px;
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      &:hover {
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
      }
    
      .user-avatar {
        width: 40px; /* Ensure uniform size */
        height: 40px; /* Ensure uniform size */
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f0f0f0; /* Optional: to give it a background */
        border-radius: 50%; /* Makes it circular */
        overflow: hidden;
        margin-right: 10px;
      }
      
      .user-avatar i {
        // font-size: 24px; /* Adjust the icon size */
        color: #007bff; /* Optional: Customize the icon color */
      }
    
      .user-info p {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
      }
    
      .arrow {
        display: flex;
        align-items: center; /* Vertically align items */
        margin-left: auto;
    
        .user-avatar {
          width: 40px; /* Match size of the main user avatar */
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%; /* Optional: if you want a circular background */
          background-color: #f0f0f0; /*
          margin-right: 10px; /* Spacing between icon and arrow */
        }
    
        img {
          width: 50px; /* Match size of the avatar */
          height: 50px;
        }
      }
    
      &.selected {
        background-color: #1976d2;
        color: white;
      }
    }
    
  }
  
  .profile-detail-container {
    background-color: #ffffff;
    box-shadow: 0px 0px 24px rgba(146, 171, 186, 0.2);
    height: 400px;
    border-radius: 16px;
  }
  
  .profile-header {
    display: flex;
    // border-bottom: 2px solid black;
    height: 24%;
    gap: 14px;
    align-items: center;
    margin-bottom: 8px;
    > .prof-img {
      height: 60px;
      width: 60px;
      border-radius: 50%;
      margin-left: 14px;
      // border: 1px solid rgb(174, 174, 174);
      display: flex;
      justify-content: center;
      align-items: center;
      > img {
        height: 100%;
        width: 100%;
        object-fit: fill;
      }
    }
    > .prof-name {
      font-size: 20px;
      font-weight: 600;
    }
  }
  
  .profile-details {
    > .row {
      > .col-4 {
        margin-bottom: 14px;
        > div {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 10px;
          font-size: 15px;
          > .icon {
            margin-right: 4px;
            height: 44px;
            width: 44px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background-color: rgb(243, 248, 251);
            > img {
              height: 70%;
              width: 70%;
            }
          }
          > .detail {
            > .detail-title {
              margin-bottom: 3px;
              font-size: 17px;
              font-weight: 600;
            }
            > .detail-value {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  
.profile-pic {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
  }
  
  .profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid #ddd;
  }
  .button-left {
    display: flex;
    align-items: center;
    > span {
      margin: 0 auto;
    }
  
    > .left-arrow {
      width: 61px;
      height: 61px;
      position: relative;
      left: 15px;
    }
  }
  .row2 {
    > .col-4 {
      margin-bottom: 10px;
      > div {
        display: flex;
        padding-left: 15px;
        align-items: center;
        gap: 10px;
        font-size: 15px;
        > .icon {
          margin-right: 4px;
          height: 44px;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 44px;
          border-radius: 50%;
          background-color: rgb(243, 248, 251);
          > .email-icon {
            height: 88%;
            width: 88%;
          }
          > img {
            height: 70%;
            width: 70%;
          }
        }
        > .detail {
          > .detail-title {
            margin-bottom: 3px;
            font-size: 17px;
            font-weight: 600;
          }
          > .detail-value {
            font-size: 14px;
          }
        }
      }
    }
  }
  .video-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%; /* Ensures the video container takes full height */
    padding: 10px;
    margin: 10px;
  }


  .profile-detail-container {
    position: relative; /* Establish a relative positioning context for the container */
    padding: 16px; /* Add padding to avoid overlap */
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
  }
  
  .delete-button-container {
    position: absolute;
    top: 10px; /* Distance from the top */
    right: 10px; /* Distance from the right */
  }
  
  .delete-button {
    background-color: #ff4d4d; /* Red button color */
    color: #fff; /* White text */
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .delete-button:hover {
    background-color: #e60000; /* Darker red on hover */
  }
  
  .video-container {
    padding-top: 40px; /* Add space to ensure the video doesn't overlap with the delete button */
  }
  
  .video-player {
    width: 50vw;
    height: 100%;
    border-radius: 8px;
    // max-height: 50vh; 
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .ml-12 {
    margin-left: 12px;
  }
  .confirmModalHeader {
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
    color: #ffffff;
    display:flex;
    // border-radius: 10px;
  }
  
  .confirmModalBody {
    >p {
      color: #555555;
      font-weight: 500;
    }
  
    >.d-flex {
      justify-content: center;
    }
  }
  
  .confirmModalCloseBtn {
    color: #fff !important;
  }
  
  .error {
    color: red;
  }
  .profile-detail-container {
    display: flex;
    flex-direction: column;
    height: 400px; /* Adjust based on your layout needs */
  }
  
  .pdf-container {
    flex-grow: 1;
    overflow-y: hidden; /* Enable vertical scrolling */
    overflow-x: hidden; /* Prevent horizontal scrolling */
    padding-top: 40px; 
  }
  
  .pdf-viewer {
    width: 100%;
    height: 100%; /* Take the full height of the container */
    border: none; /* Remove iframe border */
  }
  .modal-header{
    // border-radius: 20px !important;
    border-top-right-radius: 20px  !important;
    border-top-left-radius: 20px  !important;
    
  }
  
  
  