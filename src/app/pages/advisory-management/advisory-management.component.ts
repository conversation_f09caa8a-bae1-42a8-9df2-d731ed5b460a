import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { BreadcrumbComponent } from '../../common/breadcrumb/breadcrumb.component';
import { Router } from '@angular/router';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { AdminService } from '../../services/admin/admin.service';
import { DomSanitizer } from '@angular/platform-browser';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { UploadService } from '../../services/shared/upload.service';
import { SecurityService } from '../../services/security-module/security.service';
import * as pdfjsLib from 'pdfjs-dist';
import { HttpClient } from '@angular/common/http';
import { TrimFormValueService } from '../../common/Services/trim-form-value.service';

pdfjsLib.GlobalWorkerOptions.workerSrc = 'assets/pdfjs/pdf.worker.min.mjs'; 

@Component({
  selector: 'app-advisory-management',
  templateUrl: './advisory-management.component.html',
  styleUrl: './advisory-management.component.scss',
  animations:[
     trigger('tabSwitch', [
          state('active', style({ opacity: 1, transform: 'translateX(0)' })),
          state('inactive', style({ opacity: 1, transform: 'translateX(0)' })),
          transition('inactive => active', [
            animate('300ms ease-in-out')
          ]),
          transition('active => inactive', [
            animate('300ms ease-in-out')
          ]),
        ])
      ]
  
})
export class AdvisoryManagementComponent {

  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;
  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvasElement') canvasElement!: ElementRef<HTMLCanvasElement>;
  thumbnail: any;
  tabs = [
    { title: 'Files' },
    { title: 'Videos' },
  ];
  selectedTabIndex = 0;
  clickLoading:boolean = false
  totalRecords: number = 0; // 
  isUserSelected: boolean = false;
  selectedUserIndex: number | null = null;
  selectedUser: any | null = null;
  users:any[]=[]
  isLoading:boolean = false;
  currentPage: number = 1;
  pageSize: number = 30;
  plantId:any
  inactiveUsers: any[] = [];
  currentUserPlant: any;
  adminId: any;
  isUploadModelOpen: boolean = false;
  uploadForm!: FormGroup
  isEdit: boolean = false;
  ListOfPDF: any[]=[];
  ListOfVideos: any[]=[];
  FileErrorMessage!: string;
  originalValues: any;
  isUpdateEnabled: boolean = false;
  editDataId: any;
  private debounceFetch: any = null;
  pageCount: any =0;
  selectTab(tabIndex: number) {
    this.selectedTabIndex = tabIndex;
    this.isUserSelected = false;
    this.selectedUser = null;
    if(this.selectedTabIndex === 0){
      this.uploadForm.get('type')?.setValue('PDF')
    }
    else{
      this.uploadForm.get('type')?.setValue('VIDEO')
    }
    console.log('selected tab',this.selectedTabIndex);
  
  }
  maxDate: string = new Date().toISOString().split('T')[0];
  videoUrl: string = '../../../assets/files/4k_Thetestdata.mp4';
  pdfSrc:string = '../../../assets/files/dummy_1.pdf';
  submitted:boolean = false;
  selectedFile: File | null = null;
  enteredFileName: string = ''; 
  selectedFileName: string = 'example.pdf'; 
  constructor(private breadcrumb: BreadcrumbService, public sanitizer: DomSanitizer,
    private router: Router, 
    private adminService: AdminService,
    private uploadService: UploadService,
    private http: HttpClient,
    private securityService: SecurityService,
    private formService: TrimFormValueService,
    private fb: FormBuilder){

  }
  ngOnInit(){
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
    
    this.getAdvisoryData()
    this.uploadForm = this.fb.group({
      type:['',Validators.required],
      name:['',[Validators.required,Validators.pattern(/^[a-zA-Z0-9-_ ]+$/)]],
      desc:[''],
      media:['',[this.mediaValidator.bind(this)]],
      date:['', [Validators.required,this.futureDateValidator]],
      file:['']
    }, { validators: this.eitherOrValidator,},)
    this.uploadForm.get('type')?.setValue('PDF')
    this.uploadForm.valueChanges.subscribe(() => {
      if (this.isEdit) {
        this.isUpdateEnabled = this.checkIfFormChanged();
      }
    });
    this.uploadForm.get('type')?.valueChanges.subscribe(() => {
      this.uploadForm.get('media')?.updateValueAndValidity(); // Re-run the validation
    });
  }
    @HostListener('scroll', ['$event'])
    onScroll(event: any): void {
      console.log('scroll event is called')
      console.log('list of pdf',this.ListOfPDF);
      console.log('list of video',this.ListOfVideos);
      if (this.debounceFetch) {
        clearTimeout(this.debounceFetch);
      }
  
      this.debounceFetch = setTimeout(() => {
        const target = event.target;
  
        const scrolledPercentage =
          (target.scrollTop + target.clientHeight) / target.scrollHeight;
  
        if (scrolledPercentage > 0.9 && !this.isLoading) {
          const total_length = this.ListOfPDF.length + this.ListOfVideos.length;
        
          if (total_length < this.totalRecords) {
            console.log('Fetching more users...');
            const nextPage = Math.ceil(total_length / this.pageSize) + 1;
            console.log('total records:', this.totalRecords, this.users.length, nextPage,total_length,this.pageCount);
            if(nextPage <= this.pageCount)
            this.getAdvisoryData(nextPage);
          } else {

          }
        }
      }, 150); // Debounce delay
    }
    futureDateValidator(control: AbstractControl): ValidationErrors | null {
      const today = new Date();
      // this.maxDate = today.toISOString().split('T')[0];
      if (!control.value) return null; // Skip validation if empty
  
      const selectedDate = new Date(control.value);
      if (selectedDate > today) {
        return { futureDate: 'Incident Date cannot be in the future.' };
      }
  
      return null;
    }
    showUserDetails(item: any, id: any) {
      this.isUserSelected = true;
      this.selectedUserIndex = id;
      this.selectedUser = item;
      this.videoPlayer?.nativeElement?.load();
      console.log('user changes',item)
    }

      getAdvisoryData(page?:number) {
        if (this.isLoading) return; 
        this.isLoading = true;
        const data = {
          page: page?  page : 1,
          limit: this.pageSize,
          sort: 'name,ASC',
          filter: [
            'enabled||eq||true',
            'isDeleted||eq||false'
          ]
        };
        const param = createAxiosConfig(data);
        this.securityService.getAdvisoryData(param).then(response => {
          // this.ListOfPDF = [...this.ListOfPDF, ...response.data.filter((ele:any)=> ele.type === 'PDF')]
          // this.ListOfVideos = [...this.ListOfVideos , ... response.data.filter((ele:any)=> ele.type === 'VIDEO')];
          if (this.isEdit) {
            const editId = this.editDataId;

            // Update ListOfPDF
            console.log('editidi',this.editDataId)
            const pdfData = response.data.filter((ele: any) => ele.type === 'PDF');
            const pdfIndex = response.data.findIndex((item: any) => item.id === editId);
            const pdfIndex2=  this.ListOfPDF.findIndex((item: any) => item.id === editId);

            if (pdfIndex !== -1) {
              // Replace the existing item with the response data item
              this.ListOfPDF[pdfIndex2] = response.data[pdfIndex];
              this.ListOfPDF = [...this.ListOfPDF]

            } else {
              // Replace the entire list if editId is not found
              this.ListOfPDF = [...pdfData];
            }

            // Update ListOfVideos
            const videoData = response.data.filter((ele: any) => ele.type === 'VIDEO');
            const videoIndex = response.data.findIndex((item: any) => item.id === editId);
            const videoIndex2 = this.ListOfVideos.findIndex((item: any) => item.id === editId);
            if (videoIndex !== -1) {
              // Replace the existing item with the response data item

              this.ListOfVideos[videoIndex2] = response.data[videoIndex]
              this.ListOfVideos =  [...this.ListOfVideos]
            } else {
              // Replace the entire list if editId is not found
              this.ListOfVideos = [...videoData];
            }
            this.isEdit = false;
            this.editDataId = null
            console.log('editidi',this.isEdit)
          }
          else {
            this.ListOfPDF = [
              ...new Map(
                [...this.ListOfPDF, ...response.data.filter((ele: any) => ele.type === 'PDF')].map(item => [item.id, item])
              ).values()
            ];

            this.ListOfVideos = [
              ...new Map(
                [...this.ListOfVideos, ...response.data.filter((ele: any) => ele.type === 'VIDEO')].map(item => [item.id, item])
              ).values()
            ];
          }


          this.totalRecords = response.total;
          this.users = response.data
          this.pageCount = response.pageCount;
          this.isLoading = false
          console.log('response', response)
          console.log('pdf',this.ListOfPDF);
          console.log('video',this.ListOfVideos);
          
          
        });
      }

      updateMediaData(responseData: any) {
        if (!responseData || !this.editDataId) return;
      
        // ✅ Check if editId exists in ListOfPDF
        const pdfIndex = this.ListOfPDF.findIndex((ele) => ele.id === this.editDataId);
        const index = responseData.findIndex((ele:any) => ele.id === this.editDataId);
        if (pdfIndex !== -1) { 
          if(index !== -1){
            this.ListOfPDF[pdfIndex] =  responseData[index] ;
            this.ListOfPDF = [...this.ListOfPDF, ...responseData]; // 🔥 Ensures Angular detects change
            console.log('ListofPDF in if',this.ListOfPDF)

          }
          else{
            const updatedMap = new Map();
            // ✅ Add existing items to the map
            this.ListOfPDF.forEach(item => updatedMap.set(item.id, item));
            
            // ✅ Overwrite with new data (if the same ID exists, it'll replace the old one)
            responseData.forEach((item:any) => updatedMap.set(item.id, item));
            
            // ✅ Convert Map back to an array
            this.ListOfPDF = responseData;
            
            // this.ListOfPDF = [...this.ListOfPDF, ...uniqueResponseData]; // 🔥 Ensures Angular detects change
            console.log('ListofPDF in else',this.ListOfPDF)
          }
          return;
        }
      
        // ✅ Check if editId exists in ListOfVideos
        const videoIndex = this.ListOfVideos.findIndex((ele) => ele.id === this.editDataId);
        const index2 = responseData.findIndex((ele:any) => ele.id === this.editDataId);
        if (videoIndex !== -1) {
          this.ListOfVideos[videoIndex] = responseData[index2];
          this.ListOfVideos = [...this.ListOfVideos]; // 🔥 Ensures Angular detects change
        }
      }
      

      eitherOrValidator(group: FormGroup) {
        const media = group.get('media')?.value;
        const file = group.get('file')?.value;
    
        if (!media && !file) {
          return { eitherOrRequired: true }; // Neither field is filled
        }
    
        if (media && file) {
          return { eitherOrRequired: true }; // Both fields are filled
        }
    
        return null; // Valid
      }

      openUploadModel(){
        this.isUploadModelOpen= true;
        this.isEdit = false
        if(this.selectedTabIndex == 0){
          this.uploadForm.get('type')?.setValue('PDF') 
        }
        else{
          this.uploadForm.get('type')?.setValue('VIDEO')
        }
        this.uploadForm.get('type')?.enable()
        this.uploadForm.get('date')?.enable()

      }

      closeFormModal() {
        this.isEdit = false;
        this.resetUploadForm()
      }
      onClickCross() {
        this.isUploadModelOpen = false
        this.resetUploadForm()
        this.isEdit = false;
      }
      private async fetchFile(filePath: string): Promise<File> {
        const response = await fetch(filePath); // Fetch the file
        const blob = await response.blob(); // Convert the response to a Blob
        const fileName = filePath.split('/').pop() || 'default.png'; // Extract file name
        return new File([blob], fileName, { type: blob.type }); // Create a File object
      }
      mediaValidator(control: AbstractControl): ValidationErrors | null {
        const type = this.uploadForm?.get('type')?.value;
        const media = control.value;
    
        if (!media) {
          return null;
        }
    
        const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'];
        const pdfExtension = 'pdf';
    
        const fileExtension = media.split('.').pop()?.split('?')[0].toLowerCase();
        const urlRegex = /^(https?:\/\/)[\w\-.~:/?#[\]@!$&'()*+,;=]+$/;
        const localPathRegex = /^([a-zA-Z]:\\|\/)[\w\-.\\/]+$/;
        const validUrlRegex = /^(https?:\/\/|www\.)[\w\-.~:/?#[\]@!$&'()*+,;=%]+$/;
    
        if (!validUrlRegex.test(media)) {
          return { invalidMedia: 'Invalid URL. URL must start with "https://" or "www."' };
        }
        if (type === 'VIDEO' && !videoExtensions.includes(fileExtension)) {
          return { invalidMedia: 'Invalid video URL.' };
        }
    
        if (type === 'PDF' && fileExtension !== pdfExtension) {
          return { invalidMedia: 'Invalid PDF URL. The file must be a .pdf' };
        }
    
        return null;
      }
      
      async onSubmit(){
        let formValue = this.uploadForm.value;
        this.submitted = true;
        this.formService.trimFormValues(this.uploadForm)
        console.log('submit',this.uploadForm)
        if(this.uploadForm.status !== 'INVALID' && !this.FileErrorMessage){
          console.log(this.uploadForm.value)
          this.clickLoading = true
          if(this.selectedFile){
            formValue['media'] = await this.uploadProfilePic(this.selectedFile);
            formValue['thumbnail'] = await this.uploadProfilePic(this.thumbnail);
            formValue['size'] = this.selectedFile.size
          }
          const mediaValue = this.uploadForm.get('media')?.value || '';
          if(mediaValue.trim().length >0){
            formValue['media'] = this.uploadForm.value.media;
            const Type = this.uploadForm.get('type')?.value;
            const filePath = Type === 'PDF' ?  '../../../assets/img/pdf thumbnail.png' :'../../../assets/img/video_thumbnail.jpg'; // Specify the path of the file in the assets folder
            const file = await this.fetchFile(filePath);
            formValue['thumbnail'] = await this.uploadProfilePic(file);
            console.log('form value',formValue);
          }
          if(formValue['media'].length){
            if(this.isEdit){
               if (this.isEdit) {
                      this.clickLoading = true
                      let payload;
                      const updatedFields = this.getUpdatedFields();
                      delete updatedFields['file']
                      if (this.isEdit && Object.keys(updatedFields).length > 0) {
                        payload = {
                          tableName: 'advisory',
                          id: this.editDataId,
                          data: updatedFields,
                        };
                      }
                      this.securityService.updateFile(payload).then((response: any) => {
                        this.clickLoading = false;
                        if (response?.responseCode !== 400) {
                          this.toast.showSuccessToast("File Details Updated successfully");
                          this.resetUploadForm();
                          this.isUploadModelOpen = false;
                          this.getAdvisoryData();
                          this.isUserSelected = false;
                          this.selectedUser = null
                          this.selectedUserIndex = null
                          // this.editDataId = null
                        }
                        else if(response?.responseCode === 400){
                          this.toast.showErrorToast(response.message);
                          this.clickLoading = false;
                        }
                         else {
                          this.toast.showErrorToast("An error occurred.");
                          this.clickLoading = false;
                        }
                      },)
                    }
                    
            }
            else{
              this.securityService.UploadAdvisoryFile(formValue).then((response) => {
                this.clickLoading = false
                if(response){
                  this.toast.showSuccessToast(`${this.selectedTabIndex === 0 ? "File" : "Video"} Uploaded successfully`)
                  this.resetUploadForm();
                  this.isUploadModelOpen = false;
                  this.ListOfPDF = [];
                  this.ListOfVideos = []
                  this.getAdvisoryData();
                  this.isUserSelected = false;
                  this.selectedUser = null
                  this.selectedUserIndex = null
                }
                else{
                  let message = response?.message ? response.message :"Error! Uploading File"
                  this.toast.showErrorToast(message);
                }
              })
            }
           
          }
          else{
            this.clickLoading = false;
            this.isUploadModelOpen = false;
            let message = "Error! Uploading File"
            this.toast.showErrorToast(message);
          }
        }
        
      }
      onTypeChange(event:any){
        console.log(event);
        const Type = event.target?.value
        if(this.selectedFile){
          // this.selectedFile = input.files[0];
          const fileType = this.selectedFile.type;
          if(Type === 'VIDEO'){
            if(fileType.startsWith('video/')){
              this.FileErrorMessage = ''
              this.generateVideoThumbnail(this.selectedFile);
            }
            else{
              this.FileErrorMessage = 'Only Video File is allowed'
            }
          }
        else if(Type === 'PDF'){
          if(fileType === 'application/pdf'){
            this.FileErrorMessage = ''
            this.generatePdfThumbnail(this.selectedFile);
          }
          else{
            this.FileErrorMessage = 'Only PDF File is allowed'
          }
        }
        }
        
      }
      generateVideoThumbnail(file: File): void {
        const video = this.videoElement.nativeElement;
        video.src = URL.createObjectURL(file);
    
        video.addEventListener('loadeddata', () => {
          video.currentTime = 1; // Capture frame at 1s
          setTimeout(() => {
            this.captureVideoThumbnail();
          }, 500);
        });
      }
      async generatePdfThumbnail(file: File): Promise<void> {
        const fileReader = new FileReader();
    
        fileReader.onload = async (event: ProgressEvent<FileReader>) => {
          const typedArray = new Uint8Array(event.target?.result as ArrayBuffer);
    
          const pdf = await pdfjsLib.getDocument({ data: typedArray }).promise;
          const page = await pdf.getPage(1);
    
          const viewport = page.getViewport({ scale: 1 });
          const canvas = this.canvasElement.nativeElement;
          const ctx = canvas.getContext('2d');
    
          if (!ctx) {
            console.error('Canvas rendering context is null');
            return;
          }
    
          canvas.width = viewport.width;
          canvas.height = viewport.height;
    
          await page.render({ canvasContext: ctx, viewport }).promise;
    
          const base64Image = canvas.toDataURL('image/png');
          this.thumbnail = this.convertBase64ToFile(base64Image, 'pdf-thumbnail.png');
        };
        fileReader.readAsArrayBuffer(file);
      }
    
      async uploadProfilePic(file:any): Promise<string> {
        let url = "";
        if (file) {
          const formData = new FormData();
          formData.append('file', file);
          try {
            url = await this.uploadService.uploadImage(formData);
          } catch (error) {
            console.error("Error uploading image:", error);
            return ''
          }
        }
        return url;
      }

      resetUploadForm(){
        this.selectedFile  = null;
        this.uploadForm.reset()
        this.submitted = false
        // if(!this.isEdit){
          this.uploadForm.get('type')?.setValue('')
        // }
        
      }

      onFileChange(event: Event) {
        const input = event.target as HTMLInputElement;
        const Type = this.uploadForm.get('type')?.value;
        if (input.files && input.files.length > 0) {
          this.selectedFile = input.files[0];
          const fileType = this.selectedFile.type
          const reader = new FileReader();
          if(Type === 'VIDEO'){
            if(fileType.startsWith('video/')){
              this.FileErrorMessage = ''
              this.generateVideoThumbnail(this.selectedFile);
            }
            else{
              this.FileErrorMessage = 'Only Video File is allowed'
            }
          }
        else if(Type === 'PDF'){
          if(fileType === 'application/pdf'){
            this.FileErrorMessage = ''
            this.generatePdfThumbnail(this.selectedFile);
          }
          else{
            this.FileErrorMessage = 'Only PDF File is allowed'
          }
        }
          reader.onload = (e: ProgressEvent<FileReader>) => {
            if (e.target?.result) {
            }
          };
          reader.readAsDataURL(this.selectedFile); // Read the file as a data URL
        } else {
          console.error('No file selected.');
        }
        console.log('FileErrorMessage',this.FileErrorMessage);
      }

      openModal2(modalName: string) {
        const modal = document.getElementById(modalName);
        if (modal) {
          modal.style.display = 'block';
          modal?.classList.add('show');
          modal?.setAttribute('aria-hidden', 'false');
          modal?.setAttribute('aria-modal', 'true');
          modal?.setAttribute('role', 'dialog');
        }
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(backdrop);
    
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';
        document.body.style.paddingRight = '0px';
      }

      getUpdatedFields(): any {
        const currentValues = this.uploadForm.value;
        const updatedFields: any = {};
    
        Object.keys(currentValues).forEach((key) => {
          if (currentValues[key] !== this.originalValues[key]) {
            updatedFields[key] = currentValues[key];
          }
        });
    
        return updatedFields;
      }

      checkIfFormChanged(): boolean {
        const currentValues = this.uploadForm.value;
        return Object.keys(currentValues).some(
          (key) =>
            typeof currentValues[key] === 'string' &&
            currentValues[key].trim() !== this.originalValues[key]?.trim()
        );
      }
      

      closeModal2(modalName: string) {
        const modal = document.getElementById(modalName);
        this.enteredFileName = ''
        console.log('selected',this.selectedUser.name)
        if (modal) {
          modal.style.display = 'none';
          modal?.classList.remove('show');
          modal?.setAttribute('aria-hidden', 'true');
          modal?.removeAttribute('aria-modal');
          modal?.removeAttribute('role');
          this.uploadForm.reset()
        }
    
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
          backdrop.parentNode?.removeChild(backdrop);
        }
    
        const backdrop0 = document.querySelector('.modal-backdrop');
        if (backdrop0) {
          backdrop0.parentNode?.removeChild(backdrop0);
        }
    
        document.body.className = '';
        document.body.removeAttribute('style');
        document.body.removeAttribute('data-bs-overflow');
        document.body.removeAttribute('data-bs-padding-right');
      }

      deleteFile(){
        const payload = {
          tableName: 'advisory',
          id: this.selectedUser.id,
          data: {
           isDeleted: true
          },
        };
        this.clickLoading = true
        this.securityService.updateFile(payload).then((response: any) => {
          this.clickLoading = false;
          if (response.responseCode === 200 || response.status === 201) {
            this.toast.showSuccessToast(`${this.selectedTabIndex === 0 ? 'File':'Video'} deleted successfully.`);
            this.closeModal2('migrateModal')
            this.enteredFileName = ''
            this.ListOfPDF = [];
            this.ListOfVideos = [];
            this.getAdvisoryData();
            this.selectedUser = null;
            this.selectedUserIndex = null;
            this.isUserSelected = false;
          } else  if(response.statusCode == 404){
            this.toast.showErrorToast(response.message ? response.message:'An error occurred.');
          }
          else{
            this.toast.showErrorToast("An error occurred.");
          }
        },)
      }

      editFile(file:any){
        this.isEdit = true;
        this.editDataId = file.id
        this.uploadForm.get('type')?.disable()
        this.uploadForm.get('date')?.disable()
        this.isUploadModelOpen = true;
        this.uploadForm.patchValue({
          type:file.type,
          name:file.name,
          desc:file?.desc,
          media:'',
          date:file.date,
          file:''
        })
        this.originalValues = this.uploadForm.value
        console.log('upload value',this.originalValues)
        this.isUpdateEnabled = this.checkIfFormChanged();
      }

      captureVideoThumbnail(): void {
        const video = this.videoElement.nativeElement;
        const canvas = this.canvasElement.nativeElement;
        const ctx = canvas.getContext('2d');
    
        if (!ctx) return;
    
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const file = canvas.toDataURL('image/png');
        this.thumbnail = this.convertBase64ToFile(file, 'thumbnail.png');
        console.log('Image File:', file);
        console.log('thumbnail',this.thumbnail)
      }
    
      convertBase64ToFile(base64: string, filename: string): File {
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)![1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
      
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
      
        return new File([u8arr], filename, { type: mime });
      }
}


