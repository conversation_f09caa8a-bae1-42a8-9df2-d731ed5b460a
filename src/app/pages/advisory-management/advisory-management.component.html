<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>

<div class="header-container">
  <p class="fw-bold">Advisory Management</p>
  <div class="tab-container">
    <div class="tab-headers">
      <div *ngFor="let tab of tabs; let i = index" [@tabSwitch]="i === selectedTabIndex ? 'active' : 'inactive'"
        [class.active]="i === selectedTabIndex" (click)="selectTab(i)" class="tab-header">
        {{ tab.title }}
      </div>
    </div>
  </div>
</div>

<div class="active-user">
  <div class="d-flex align-items-end justify-content-end mb-4">
    <div style="display: flex;gap:5px">
      <button class="button-submit button-right" (click)="openUploadModel()" style="width: 150x;">
        <img alt="" src="../../../assets/svg/upload.svg" style="margin-left:-20px" class="right-arrow" />
        <span style="color: white;margin: 0px;">Upload {{selectedTabIndex == 0 ?'File' :'Video'}}</span>
      </button>

    </div>
  </div>
</div>

<div *ngIf="selectedTabIndex == 0" class="active-user">
  <div class="row">
    <div class="col-4">
      <div class="outer-container" (scroll)="onScroll($event)">
        <div class="user-list" *ngFor="let pdf of ListOfPDF; let i = index">
          <div class="user-item" [ngClass]="{ selected: selectedUserIndex === i }" (click)="showUserDetails(pdf, i)">
            <div class="user-avatar">
              <i class="bi bi-file-earmark"></i>
            </div>
            <div class="user-info">
              <p>{{ pdf.name }} </p>
            </div>
            <div class="arrow">
              <div class="user-avatar" (click)="editFile(pdf)">
                <i class="bi bi-pencil"></i>
              </div>
              <img [src]="
                  selectedUserIndex === i
                    ? '../../../assets/svg/whitearrow.svg'
                    : '../../../assets/svg/bluearrow.svg'
                " alt="Arrow" />
            </div>
          </div>
        </div>
        <div class="loading-container" *ngIf="isLoading">
          <div class="spinner-border text-primary" style="width: 2rem; height: 2rem;" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-8">
      <div class="profile-detail-container d-flex justify-content-center align-items-center" *ngIf="!isUserSelected">
        <div class="fs-4 fw-bold">Select a File</div>
      </div>
      <div class="profile-detail-container" *ngIf="isUserSelected">
        <div class="delete-button-container p-1">
          <button class="delete-button" (click)="openModal2('migrateModal')"> <i class="bi bi-trash"></i> </button>
        </div>
        <div class="pdf-container" *ngIf="pdfSrc; else loading">
          <iframe [src]="selectedUser.media | safeURL" class="pdf-viewer"></iframe>
        </div>
        <ng-template #loading>
          <p>Loading PDF...</p>
        </ng-template>
      </div>

    </div>

  </div>

</div>

<div *ngIf="selectedTabIndex == 1" class="active-user">
  <div class="row">
    <div class="col-4">
      <div class="outer-container" (scroll)="onScroll($event)">
        <div class="user-list" *ngFor="let videoDetails of ListOfVideos; let i = index">
          <div class="user-item" [ngClass]="{ selected: selectedUserIndex === i }"
            (click)="showUserDetails(videoDetails, i)">
            <div class="user-avatar" >
              <i class="bi bi-camera-video"></i>
            </div>
            <div class="user-info">
              <p class="user-name">{{ videoDetails.name }}</p>
            </div>

            <div class="arrow d-flex">
              <div class="user-avatar" (click)="editFile(videoDetails)">
                <i class="bi bi-pencil"></i>
              </div>
              <img class="arrow-icon " [src]="
                  selectedUserIndex === i
                    ? '../../../assets/svg/whitearrow.svg'
                    : '../../../assets/svg/bluearrow.svg'
                " alt="Arrow" />

            </div>
          </div>

        </div>
        <div class="loading-container" *ngIf="isLoading">
          <div class="spinner-border text-primary" style="width: 2rem; height: 2rem;" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-8">
      <div class="profile-detail-container d-flex justify-content-center align-items-center" *ngIf="!isUserSelected">
        <div class="fs-4 fw-bold">Select a Video</div>
      </div>

      <div class="profile-detail-container" *ngIf="isUserSelected">
        <div class="delete-button-container">
          <button class="delete-button" (click)="openModal2('migrateModal')"> <i class="bi bi-trash"></i> </button>
        </div>

        <div class="video-container">
          <video class="video-player" controls #videoPlayer *ngIf="selectedUser.media">
            <source [src]="selectedUser?.media" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div style="margin-top: 20px;">

          </div>
        </div>
        <p *ngIf="!videoUrl">Loading video...</p>
      </div>

    </div>
  </div>

</div>
<div style="margin-top: 50px; border: 2px solid green;">

</div>

<app-custom-modal style="height: auto;" [title]="selectedTabIndex == 0 ?(isEdit ? 'Update File':'Upload File') :(isEdit ? 'Update Video':'Upload Video')" [width]="'335px'"
  *ngIf="isUploadModelOpen" (closeModal)="closeFormModal()" (onClickCross)="onClickCross()">
  <div class="mb-0">
    <form [formGroup]="uploadForm" (ngSubmit)="onSubmit()">
      <div class="row d-flex">

        <div class="col-md-12 input-text-group">
          <label class="ml-12">File Type <span class="text-danger"> * </span></label>
          <select class="form-control" formControlName="type" (change)="onTypeChange($event)">
            <option value="">Select File Type</option>
            <option value="VIDEO">Video</option>
            <option value="PDF">PDF</option>
          </select>
          <span class="text-danger ml-12"
            *ngIf="(uploadForm.get('type')?.touched|| uploadForm.get('type')?.dirty || submitted) && uploadForm.get('type')?.errors?.['required']">
            File Type is required.
          </span>
        </div>
        <div class="input-text-group col-md-12">
          <label class="ml-12">Incident Date<span class="text-danger"> * </span></label>
          <input type="date" [disabled]="isEdit" class="form-control form-control-lg" formControlName="date"
            max="{{ maxDate }}">
          <div
            *ngIf="(uploadForm.get('date')?.touched|| uploadForm.get('date')?.dirty || submitted) && uploadForm.get('date')?.errors?.['required']"
            class="text-danger ml-12">
            Incident Date is required.
          </div>
          <div class="text-danger ml-12" *ngIf="uploadForm.get('date')?.errors?.['futureDate']">
            {{ uploadForm.get('date')?.errors?.['futureDate'] }}
          </div>
        </div>


        <div class="col-md-12 input-text-group">
          <label class="ml-12">Title<span class="text-danger"> * </span></label>
          <input type="text" class="form-control" formControlName="name" placeholder="Enter Title" />
          <span class="text-danger ml-12"
            *ngIf="(uploadForm.get('name')?.dirty||uploadForm.get('name')?.touched || submitted) && uploadForm.get('name')?.errors?.['required']">
            Title is required.
          </span>
          <div class="text-danger ml-12"
          *ngIf="(uploadForm.get('name')?.dirty||uploadForm.get('name')?.touched || submitted) && uploadForm.get('name')?.errors?.['pattern']">
          Enter Valid Title.
        </div>
        </div>


        <div class="col-md-12 input-text-group">
          <label class="ml-12">Description</label>
          <input type="text" class="form-control" formControlName="desc" placeholder="Enter Description" />
          <span class="text-danger ml-12"
            *ngIf="(uploadForm.get('desc')?.dirty||uploadForm.get('desc')?.touched || submitted) && uploadForm.get('desc')?.errors?.['required']">
            Description is required.
          </span>
        </div>
        <div class="col-md-12 input-text-group">
          <label class="ml-12">Link / URL</label>
          <input type="text" class="form-control" formControlName="media" placeholder="Enter Link/Url" />
          <div class="text-danger ml-12" *ngIf="uploadForm.get('media')?.errors?.['invalidMedia']">
            {{ uploadForm.get('media')?.errors?.['invalidMedia'] }}
          </div>
        </div>

        <div class="input-text-group col-md-12">
          <label class="ml-12">File</label>
          <input type="file" class="form-control form-control" id="profile" formControlName="file" #fileInput
            (change)="onFileChange($event)" accept="video/*,application/pdf" />
            <video #videoElement (loadeddata)="captureVideoThumbnail()" style="display: none;"></video>
            <canvas #canvasElement style="display: none;"></canvas>
            <!-- <img *ngIf="thumbnail" [src]="thumbnail" alt="Thumbnail" class="thumbnail-preview"> -->
        </div>
        <span class="text-danger ml-12" *ngIf="(uploadForm.errors?.['eitherOrRequired'] && submitted)">
          Please upload either a Link/URL or a File.
        </span>
        <span *ngIf="FileErrorMessage" class="text-danger ml-12">
          {{ FileErrorMessage}}
        </span>
      </div>

      <div class="row d-flex mt-4 btn-section">
        <div class="col-md-6">
          <button style="width: 100%;" class="button-submit" [disabled]="(isEdit && !isUpdateEnabled)"
            type="submit">{{isEdit?"Update":'Submit'}}</button>
        </div>
        <div class="col-md-6">
          <button *ngIf="!isEdit" style="width: 100%; margin-left: 0px;" type="button" (click)="resetUploadForm()"
            class="button-back">
            Reset
          </button>
          <button *ngIf="isEdit" style="width: 100%; margin-left: 0px;" type="button" (click)="onClickCross()"
            class="button-back">
            Cancel
          </button>
        </div>
      </div>
    </form>

  </div>
</app-custom-modal>

<div class="modal fade" tabindex="-1" id="migrateModal" aria-labelledby="migrateModalLabel" #migrateModal
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="margin-top: 40px;border-radius: 20px;">
      <div class="modal-header confirmModalHeader justify-content-between">
        <h6 class="modal-title">Are you sure you want to delete?</h6>
        <button type="button" class="btn" data-bs-dismiss="modal" aria-label="Close"
          (click)="this.closeModal2('migrateModal')" style="font-size: 16px; font-weight: 600; color: #fff">
          X
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body confirmModalBody">
        <p class="text-center fw-semibold mb-4">
          Please type the file name to confirm deletion:<br>
          <span style="color: green;">{{selectedUser?.name}}</span>
        </p>
        <input type="text" class="form-control text-center" placeholder="Enter file name"
          [(ngModel)]="enteredFileName" />

        <!-- Buttons -->
        <div class="d-flex mt-4 justify-content-center gap-3">
          <!-- Submit Button -->
          <div class="d-flex mt-4 justify-content-center" style="gap: 10px">
            <button type="button" id="bt_approve" class="button-submit button-left"
              [disabled]="enteredFileName.trim() !== selectedUser?.name.trim()" (click)="deleteFile()">
              <span class="ps-5">Delete</span>
              <img src="../../../assets/svg/accept.svg" class="left-arrow" />
            </button>
            <button type="button" id="bt_reject" class="button-back button-left"
              (click)="this.closeModal2('migrateModal')">
              <span class="ps-5">Cancel</span>
              <img src="../../../assets/svg/reject.svg" class="left-arrow" />
            </button>
          </div>


        </div>
      </div>
    </div>
  </div>
</div>