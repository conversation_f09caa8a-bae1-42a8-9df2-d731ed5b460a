.outer-layer{
    background-color: white;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  max-height: 54vh;
  overflow-y: auto;
}

.equipment-row{
    // gap: 10px;
    >.col-4{
        >div{
            border-radius: 8px;
            border: 1px solid #bd3861;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14.5px;
            padding: 6px 8px;
            font-weight: 500;
            >.equiment-title{
                font-size: 13px;
            }
            >.details-arrow{
                height: 32px;
                width: 32px;
                >img{
                    height: 100%;
                    width: 100%;
                }
            }
        }
    }
}
.confirmModalHeader {
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
    color: #ffffff;
  }
  .confirmModalBody {
     display: flex;
     justify-content: center;
     flex-direction: column;
     align-items: center;
     >h3{
      color: #008224;
     }
     >p{
      font-size: 16px;
      color:  #555555;
     }
  }
  .confirmModalCloseBtn {
    color: #fff !important;
  }

.checklist-icon{
  margin-bottom: 8px;
  height: 20%;
  width: 20%;
}
.button-left {
  display: flex;
  align-items: center;
  > span {
    margin: 0 auto;
  }
  > .left-arrow {
    width: 61px;
    height: 61px;
    position: relative;
    left: 15px;
  }
}
.gradient-header {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white; /* Optional: Make text white for better contrast */
  padding: 1rem; /* Optional: Adjust padding for aesthetics */
  border-top-left-radius: 0.3rem; /* Match modal border radius */
  border-top-right-radius: 0.3rem;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%; /* Full viewport width */
  background-color: #f8f9fa; /* Optional: light background */
}
.adani-gradient {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
  border: none;
  color: white;
}
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}