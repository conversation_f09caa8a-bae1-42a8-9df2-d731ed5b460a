<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>
<div class="d-flex align-items-center justify-content-between" style="margin-top: -8px">
  <div class="">
    <p class="fw-bold">Stand-BY-Equipment List</p>
  </div>
  <div>
    <button class="button-submit button-right" (click)="downloadAllChecklist()" style="width: 200px;">
      <img alt="" src="../../../assets/svg/download-btn-icon.svg" class="right-arrow"  style="margin-left:-6px"/>
      <span class="">Download Excel</span>
    </button>
  </div>
</div>

<div class="outer-layer mt-4">
  <div class="p-4">
    <div class="row equipment-row" *ngIf="!loading">
      <div class="col-4 mb-3" *ngFor="let item of equipmentList; index as i">
        <div (click)="openEquipmentModal(item)">
          <div class="equipment-name">{{item.equipment.name}}</div>
          <div class="details-arrow">
            <img src="../../../assets/svg/equipmet-list-arrow.svg" alt="details">
          </div>
        </div>
      </div>
      <div class="alert alert-info mt-3" role="alert" *ngIf="equipmentList.length === 0">
        No equipment found.
      </div>
    </div>
    <div class="loading-container" *ngIf="loading">
      <div class="spinner-border text-primary" style="width: 5rem; height: 5rem;" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" tabindex="-1" id="downloadChecklistModal" aria-labelledby="downloadChecklistModalLabel"
  #downloadChecklistModal aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header confirmModalHeader justify-content-between">
        <h5 class="modal-title">Stand-By-Equipment</h5>
        <button type="button" class="btn" data-bs-dismiss="modal" aria-label="Close"
          (click)="this.closeModal2('downloadChecklistModal')" style="font-size: 18px; font-weight: 600; color: #fff">
          X
        </button>
      </div>
      <div class="modal-body confirmModalBody">
        <div class="checklist-icon">
          <img src="../../../assets/svg/checklist-downloaded.svg" alt="">
        </div>
        <h3>CONGRATULATIONS</h3>
        <p>Your Checklist has been successfully downloaded</p>
        <button type="button" id="bt_approve" class="button-submit button-left"
          (click)="this.closeModal2('downloadChecklistModal')">
          <span class="ps-5">OK</span>
          <img src="../../../assets/svg/accept.svg" class="left-arrow" />
        </button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="equipmentModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header gradient-header">
        <h5 class="modal-title" id="uploadModalLabel">Stand-BY-Equipment</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <div class="row">
          <div class="col-6">
            <label class="fw-bold">Equipment Name</label>
            <p>{{ selectedEquipment?.equipment?.name || '' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">Equipment Number</label>
            <p>{{ selectedEquipment?.equipment?.equipmentNumber || '' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">Function Location</label>
            <p>{{ selectedEquipment?.equipment?.functionLocation || '' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">Section Name</label>
            <p>{{ selectedEquipment?.equipment?.sectionName || '' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">Condition</label>
            <p>{{ selectedEquipment?.condition || '' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">Reason</label>
            <p>{{ selectedEquipment?.reason || '' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">From Date</label>
            <p>{{ selectedEquipment?.fromDate | date: 'dd/MM/yyyy hh:mm a' }}</p>
          </div>
          <div class="col-6">
            <label class="fw-bold">To Date</label>
            <p>{{ selectedEquipment?.toDate | date: 'dd/MM/yyyy hh:mm a' }}</p>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer d-flex justify-content-center">
        <button class="btn adani-gradient" (click)="removeStandBy(selectedEquipment)">Remove from StandBy</button>
      </div>
    </div>
  </div>
</div>

