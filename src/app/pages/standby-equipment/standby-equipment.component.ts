import { Component, ViewChild } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { StandByEquipmentsService } from '../../services/stand-by-equipments/stand-by-equipments.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { ExcelService } from '../../services/shared/excel-import/excel-import.service';
import { Router } from '@angular/router';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';
declare var bootstrap: any;

@Component({
  selector: 'app-standby-equipment',
  templateUrl: './standby-equipment.component.html',
  styleUrl: './standby-equipment.component.scss',
})
export class StandbyEquipmentComponent {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  equipmentList: any[] = [];
  adminId: any;
  plantId: any;
  selectedEquipment: any | null = null;
  loading: any;
  clickLoading: any;
  selectedplantId: any;
  isSuperAdmin: boolean = false;

  constructor(
    private breadcrumb: BreadcrumbService,
    private equipmentsService: StandByEquipmentsService,
    private excelService: ExcelService,
    private router: Router,
    private adminPlantSelection: AdminPlantSelectionService
  ) {}
  ngOnInit() {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.breadcrumb.setBreadcrumbUrl(this.router.url);
          this.fetchEquipments();
        });
    } else {
      this.getAdminDetail();
      this.breadcrumb.setBreadcrumbUrl(this.router.url);
      this.fetchEquipments();
    }
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    // this.currentUserPlant = currentUser.plant[0];
  }

  fetchEquipments() {
    this.loading = true;
    const data = {
      filter: [
        `plantId||eq||${this.plantId}`,
        `status||eq||1`,
        `isEquipmentStandBy||eq||1`,
      ],
    };
    const param = createAxiosConfig(data);
    this.equipmentsService.getStandByEquipments(param).then((response) => {
      this.loading = false;
      this.equipmentList = response;
    });
  }

  downloadAllChecklist() {
    this.excelService.exportToExcel(this.equipmentList, 'EquipmentData');
  }
  openModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'block';
      modal?.classList.add('show');
      modal?.setAttribute('aria-hidden', 'false');
      modal?.setAttribute('aria-modal', 'true');
      modal?.setAttribute('role', 'dialog');
    }
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    document.body.appendChild(backdrop);

    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = '0px';
  }
  closeModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'none';
      modal?.classList.remove('show');
      modal?.setAttribute('aria-hidden', 'true');
      modal?.removeAttribute('aria-modal');
      modal?.removeAttribute('role');
    }

    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.parentNode?.removeChild(backdrop);
    }

    const backdrop0 = document.querySelector('.modal-backdrop');
    if (backdrop0) {
      backdrop0.parentNode?.removeChild(backdrop0);
    }

    document.body.className = '';
    document.body.removeAttribute('style');
    document.body.removeAttribute('data-bs-overflow');
    document.body.removeAttribute('data-bs-padding-right');
  }

  openEquipmentModal(equipment: any) {
    this.selectedEquipment = equipment;
    const modalElement = document.getElementById('equipmentModal');
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
  }

  removeStandBy(selectedEquipment: any) {
    this.clickLoading = true;
    console.log(selectedEquipment);
    var body = {
      adminId: this.adminId,
      plantId: this.plantId,
      sectionId: this.selectedEquipment.sectionId,
      equipmentId: this.selectedEquipment.equipmentId,
    };
    console.log(body);
    this.equipmentsService.removeEquipmentFromStandBy(body).then((response) => {
      this.clickLoading = false;
      // Close the modal if it is open
      const modalElement = document.getElementById('equipmentModal');
      const modalInstance = bootstrap.Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.hide();
      }
      if (response.status == 200) {
        this.toast.showSuccessToast(
          'Equipment successfully removed on standby.'
        );
        this.fetchEquipments();
      } else {
        this.toast.showErrorToast(response.message);
      }
    });
  }
}
