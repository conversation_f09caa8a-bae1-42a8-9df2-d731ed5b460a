.outer-container {
  height: 430px;
  max-height: 450px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  padding: 10px;
  background-color: #f9f9f9;

  .user-list {
    max-height: 100%;
  }

  .user-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: white;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    &:hover {
      box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    }

    .user-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 15px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .user-info p {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
    }

    .arrow {
      margin-left: auto;
      img {
        width: 50px !important; /* Adjust size of the arrow */
        height: auto;
      }
    }

    &.selected {
      background-color: #1976d2;
      color: white;
    }
  }
}

.profile-detail-container {
  background-color: #ffffff;
  box-shadow: 0px 0px 24px rgba(146, 171, 186, 0.2);
  height: 430px;
  max-height: 450px;
  overflow-y: auto;
  border-radius: 16px;
}

.profile-header {
  display: flex;
  // border-bottom: 2px solid black;
  height: 24%;
  gap: 14px;
  align-items: center;
  margin-bottom: 8px;
  > .prof-img {
    height: 60px;
    width: 60px;
    border-radius: 50%;
    margin-left: 14px;
    // border: 1px solid rgb(174, 174, 174);
    display: flex;
    justify-content: center;
    align-items: center;
    > img {
      height: 100%;
      width: 100%;
      object-fit: fill;
    }
  }
  > .prof-name {
    font-size: 20px;
    font-weight: 600;
  }
}

.profile-details {
  > .row {
    > .col-3 {
      // border: 1px solid black;
      margin-bottom: 14px;
      > div {
        // display: flex;
        gap: 10px;
        font-size: 15px;
        > .icon {
          margin-right: 4px;
          height: 44px;
          width: 44px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
          background-color: rgb(243, 248, 251);
          > img {
            height: 70%;
            width: 70%;
          }
        }
        > .detail {
          > .detail-title {
            margin-bottom: 3px;
            font-size: 17px;
            font-weight: 600;
          }
          > .detail-value {
            font-size: 14px;
            word-break: break-word; // Ensures long text wraps
            overflow-wrap: anywhere;
            white-space: pre-wrap;
            max-width: 100%;
          }
        }
      }
    }
  }
}


.row2 {
  // border-bottom: 1px solid black;
  > .col-4 {
    margin-bottom: 10px;
    > div {
      display: flex;
      padding-left: 15px;
      // justify-content: center;
      align-items: center;
      gap: 10px;
      font-size: 15px;
      > .icon {
        // border: 1px solid aqua;
        margin-right: 4px;
        height: 44px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 44px;
        border-radius: 50%;
        background-color: rgb(243, 248, 251);
        > .email-icon {
          height: 88%;
          width: 88%;
        }
        > img {
          height: 70%;
          width: 70%;
        }
      }
      > .detail {
        > .detail-title {
          margin-bottom: 3px;
          font-size: 17px;
          font-weight: 600;
        }
        > .detail-value {
          font-size: 14px;  
          word-break: break-word; // Ensures long text wraps
          overflow-wrap: anywhere;
          white-space: pre-wrap;
          max-width: 100%;
        }
      }
    }
  }
}

.plant-values {
  max-height: 80px;
  overflow: hidden; /* Prevent icon shift */
  position: relative;
}
.plant-scroll {
  max-height: 80px;
  overflow-y: auto;
  padding-right: 4px;
  word-break: break-word;
  white-space: pre-wrap;
}

/* Optional: Smooth scrollbar style */
.plant-scroll::-webkit-scrollbar {
  width: 6px;
}
.plant-scroll::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.button-left {
  display: flex;
  // justify-content: center;
  align-items: center;
  > span {
    //  justify-self: center;
    margin: 0 auto;
  }

  > .left-arrow {
    width: 61px;
    height: 61px;
    position: relative;
    // left: 15px;
  }
}
.actions {
  display: flex;
  margin-top: 20px;
  > button {
    width: 200px;
  }
}

.confirmModalHeader {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  color: #ffffff;
}
.confirmModalBody {
  // text-align: center;
  > p {
    color: #555555;
    font-weight: 500;
  }
  > .d-flex {
    justify-content: center;
    margin-top: 25px;
  }
}
.confirmModalCloseBtn {
  color: #fff !important;
}

.button-right-icon{
  position: absolute;
  margin-left: 110px;
  width: 50px;
  height: 50px;
}

.btn-section{
  >button{
  width: 140px;
  }
}
.tab-container {
  display: flex;
  justify-content: center; /* Centers the .tab-headers horizontally */
  align-items: center;     /* Centers the .tab-headers vertically, if needed */
  width: 100%;             /* Ensures the container spans full width */
  margin-top: -10px;
  margin-bottom: 10px;
}

.tab-headers {
  display: flex;
  justify-content: center; /* Centers the individual .tab-header elements horizontally */
  border: 1px solid #0B74B0;
  border-radius: 20px;
  background-color: white;
  color: #0B74B0;
}

.tab-header {
  // width: 24%;
  padding: 10px 34px;
  cursor: pointer;
  transition: transform 0.3s,
}

.tab-header.active {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white;
  border-radius: 20px;
  font-weight: bold;
}
.tab-container2 {
  display: flex;
  justify-content: center; /* Centers the .tab-headers horizontally */
  align-items: center;     /* Centers the .tab-headers vertically, if needed */
  width: 100%;             /* Ensures the container spans full width */
  margin-bottom: 20px;
}

.tab-headers2 {
  display: flex;
  justify-content: center; /* Centers the individual .tab-header elements horizontally */
  border: 1px solid #0B74B0;
  border-radius: 12px;
  background-color: white;
  color: #0B74B0;
}

.tab-header2 {
  padding: 10px 32px;
  cursor: pointer;
  transition: transform 0.3s,
}

.tab-header2.active {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white;
  border-radius: 12px;
  font-weight: bold;
}
.enhanced-table {
  width: 100%;
  margin: 20px auto;
  border-collapse: collapse;
  font-family: Arial, sans-serif;
}

.enhanced-table th, .enhanced-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
  font-weight: bold; /* Makes the text inside the table cells bold */
}

.enhanced-table th {
  background-color: #0B74B0;
  color: white;
  font-weight: bold;
  font-size: 16px;
  text-transform: uppercase;
}

.action-column {
  text-align: center;
  vertical-align: top;
}

.profile-pic {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.profile-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid #ddd;
}

.action-buttons .row {
  display: flex;
  justify-content: center;
  margin: 5px 0;
}

.btn-icon {
  width: 36px;
  height: 36px;
  margin: 0 5px;
  padding: 0;
  border-radius: 50%;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon i {
  pointer-events: none;
}

.btn-success {
  background-color: #28a745;
  color: #fff;
}

.btn-danger {
  background-color: #dc3545;
  color: #fff;
}

.btn-primary {
  background-color: #007bff;
  color: #fff;
}

.btn-warning {
  background-color: #ffc107;
  color: #fff;
}

.enhanced-table td p {
  margin: 5px 0;
}

strong{
  font-weight: 900;
}

.enhanced-table td {
  vertical-align: middle;
  font-weight: bold; /* Makes text inside table data bold */
  font-size: 14px;
}
.profile-avatar{
  width: 100px;
  height: 100px;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%; /* Full viewport width */
}

.profile-details .combined-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem; /* Space between columns */
  justify-content: flex-start; /* Align all items to the start */
  padding-left: 20px;
}
.combined-row{
  >.col-3{
    width: 29% !important;
  }
}

.profile-details .col-4 {
  flex: 1 1 30%; /* Adjust width for 3-4 columns in a single row */
  display: flex;
  flex-direction: row; /* Arrange icon and details side by side */
  align-items: flex-start; /* Align items to the top */
  text-align: left; /* Ensure text is left-aligned */
}

.profile-details .icon {
  margin-right: 0.5rem; /* Add spacing to the right of icons */
  margin-bottom: 0; /* Remove bottom margin */
}

.profile-details .detail {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Align text to the start */
}

.actions {
  margin-top: 1.5rem; /* Add spacing above actions */
  display: flex;
  justify-content: flex-start; /* Align actions to the start */
  gap: 1rem; /* Add spacing between buttons */
}
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.adani-gradient {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
  border: none;
  color: white;
  border-radius: 20px;
  font-size: 12px;
}
.gradient-header {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white; /* Optional: Make text white for better contrast */
  padding: 1rem; /* Optional: Adjust padding for aesthetics */
  border-top-left-radius: 0.3rem; /* Match modal border radius */
  border-top-right-radius: 0.3rem;
}

::ng-deep .ng-select.custom-ng-select .ng-select-container {
  font-size: 1rem; /* Matches Bootstrap inputs */
  border: 1px solid #ced4da;
  border-radius: 8px;
  height: 100px;
  padding: 0 0.75rem;
  background-color: #fff;
  box-shadow: none;
  display: flex;
  align-items: center;
}

::ng-deep .ng-select.custom-ng-select .ng-value-container {
  max-height: 64px;
  overflow-y: auto;
  padding-top: 0;
  padding-bottom: 0;
}

::ng-deep .ng-select.custom-ng-select .ng-value {
  font-size: 0.85rem; /* Matches input text */
  margin: 0 4px 0 0;
}

::ng-deep .ng-select.custom-ng-select .ng-value-label {
  line-height: 0.5;
  font-weight: bold;
}

::ng-deep .ng-select.custom-ng-select .ng-input input {
  font-size: 0.85rem;
  margin: 0;
  padding: 0;
}

::ng-deep .ng-select.custom-ng-select .ng-dropdown-panel .ng-option-label {
  font-size: 1rem;
  line-height: 1.5;
}
::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{
  top: 38px;
  padding-left: 0px;
  font-size: 13px;
}

::ng-deep .ng-select .ng-select-container .ng-value-container .ng-placeholder {
  color: #595C5F;
}
::ng-deep .ng-select.custom-ng-select .ng-dropdown-panel .ng-option-label{
  font-size: 12px;
  font-weight: 600;
}

::ng-deep .ng-select.custom-ng-select .ng-value-label{
  font-size: 12px;
}

@media (max-width: 600px) {
  .profile-details .col-3 {
    flex: 1 1 100%; // Stack on smaller screens
    max-width: 100%;
  }

  .combined-row > .col-3 {
    width: 100% !important;
  }

  .actions {
    flex-direction: column;
    align-items: flex-start;

    > button {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}

/* Make sure image container never shrinks too small */
.prof-img img {
  object-fit: cover;
  min-width: 60px;
  min-height: 60px;
}

/* Avoid shrinking profile name */
.prof-name {
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Allow horizontal scrolling on really tight screens */
.profile-details {
  overflow-x: auto;
}

.profile-details .combined-row {
  width: 100%;
}
