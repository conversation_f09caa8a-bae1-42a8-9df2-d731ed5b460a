import {
  Component,
  ElementRef,
  HostListener,
  OnInit,
  ViewChild,
} from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { CommonModule } from '@angular/common';
import {
  Validators,
  FormGroup,
  FormBuilder,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { AdminService } from '../../services/admin/admin.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { PlantService } from '../../services/plant/plant.service';
import { UploadService } from '../../services/shared/upload.service';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { Router } from '@angular/router';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';
declare var bootstrap: any;

@Component({
  selector: 'app-manage-user',
  templateUrl: './manage-user.component.html',
  styleUrl: './manage-user.component.scss',
  animations: [
    trigger('tabSwitch', [
      state('active', style({ opacity: 1, transform: 'translateX(0)' })),
      state('inactive', style({ opacity: 1, transform: 'translateX(0)' })),
      transition('inactive => active', [animate('300ms ease-in-out')]),
      transition('active => inactive', [animate('300ms ease-in-out')]),
    ]),
  ],
})
export class ManageUserComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;
  selectedFile: any;
  applications = [
    { name: 'WBI', value: 2 },
    { name: 'WBI & BOG', value: 3 },
    //   { name: 'BOG', value: 1 },
  ];
  profileImageUrl: any;
  migrateForm!: FormGroup;
  loading: any;
  departments: any;
  showCreatedDate: any;
  adminrole: any;
  list: any;
  clickLoading: any;
  selectTab(tabIndex: number) {
    this.selectedTabIndex = tabIndex;
  }
  selectTab2(tabIndex: number) {
    this.selectedTabIndex2 = tabIndex;
    // console.log(this.selectedTabIndex2);
    
  }

  dummyEmpData: any[] = [];
  users: any = [];
  superUsers: any = [];
  superAdmins: any = [];
  inactiveUsers: any = [];
  currentUserPlant: any;
  plantTransferList: any = [];
  plants: any = [];
  isUserSelected: boolean = false;
  selectedUserIndex: number | null = null;
  selectedUser: any | null = null;
  isEditUserModalOpen: boolean = false;
  editUserForm!: FormGroup;
  submitted = false;
  currentPage: number = 1;
  pageSize: number = 10;
  inactiveUserPageSize: number = 1000;
  transferRequestPageSize: number = 1000;
  isLoading: boolean = false; // Prevent multiple simultaneous calls
  totalRecords: number = 0; // Total records from the backend
  activeMenu: any;
  adminId: any;
  plantId: any;
  selectedplantId: any;
  tabs: any = [];
  tabs2: any = [{ title: 'Users' }, { title: 'Admin' }];
  selectedTabIndex = 0;
  selectedTabIndex2 = 0;
  private debounceFetch: any = null;
  totalActiveUsersRecords: number = 0;
  isSuperAdmin: boolean = false;
  selectedRole: any;
  plantsForUser: any = [];
  plantsForAdmin: any = [];
  selectedPlantForAdmin: any;
  selectedPlantForUser: any;
  userPlant: any;
  adminPlant: any;
  showErr1: any;
  showErr2: any;

  buildMyForm() {
    this.editUserForm = this.fb.group({
      wbiRoleId: ['', Validators.required],
      firstName: [
        '',
        [Validators.required, Validators.pattern('^[a-zA-Z]{1,15}$')],
      ],
      lastName: [
        '',
        [Validators.required, Validators.pattern('^[a-zA-Z]{1,15}$')],
      ],
      gender: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      wbiDepartmentId: ['', Validators.required],
      applicationId: ['', Validators.required],
      adminPlantForm: [''],
      userPlantForm: [''],
    });
    this.migrateForm = this.fb.group({
      plantId: ['', Validators.required], // Add validation for the 'transfer' field
    });
  }

  constructor(
    private breadcrumbService: BreadcrumbService,
    private fb: FormBuilder,
    private adminService: AdminService,
    private plantService: PlantService,
    private uploadService: UploadService,
    private router: Router,
    private adminPlantSelection: AdminPlantSelectionService
  ) {
    this.breadcrumbService.setBreadcrumbUrl(this.router.url);
  }

  ngOnInit() {
    const role = localStorage.getItem('userRole');
    if (role == 'superadmin') this.isSuperAdmin = true;

    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.getUsers(this.currentPage);
          this.getInactiveUserList();
          this.tabs = [{ title: 'Active' }, { title: 'Inactive' }];
          this.isUserSelected = false;
          this.selectedUserIndex = null;
        });
    } else {
      this.getAdminDetail();
      this.getUsers(this.currentPage);
      this.tabs = [
        { title: 'Active Users' },
        { title: 'Inactive Users' },
        { title: 'Transfer Request' },
      ];
      this.getInactiveUserList();
    }

    this.getDepartment();
    this.buildMyForm();
    this.editUserForm.get('wbiRoleId')?.valueChanges.subscribe((roleId) => {
      this.selectedRole = roleId;
    });
    this.getPlantTransferRequest();
    this.getAllPlant();
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    this.currentUserPlant = currentUser.plant[0];
  }

  private getUsers(page: number) {
    if (this.isLoading) return; // Prevent overlapping calls
    let data;
    if (this.isSuperAdmin) {
      data = {
        plantId: this.plantId,
        page: page,
        limit: this.pageSize,
        wbiStatus: 1,
        wbiRoleId: [2, 3],
      };
    } else
      data = {
        plantId: this.plantId,
        page: page,
        limit: this.pageSize,
        wbiStatus: 1,
        wbiRoleId: [3],
      };

    this.isLoading = true;
    // const data = {
    //   page: page,
    //   limit: this.pageSize,
    //   filter: [
    //     `plantIds||in||{${this.plantId}}`,
    //     'enabled||eq||true',
    //     'wbiStatus||eq||1',
    //   ],
    //   or: orArr,
    // };
    // const data={
    //   plantIds:[
    //     this.plantId
    //   ],
    //   adminsRoleIds:["2","3"]
    // }
    // console.log('get user param', data,this.selectedUser.profilePicture );

    // const param = createAxiosConfig(data);

    this.adminService
      .getAdminAndUsers(data)
      .then((response) => {
        if (response.data) {
          const newUsers = response.data.map((user: { name: any }) => user);
          if (this.isSuperAdmin) {
            this.users = [...newUsers];
            this.superAdmins = [];
            this.superUsers = [];
            response.data.forEach((item: any) => {
              if (item.wbiRoleId === 2) {
                this.superAdmins.push(item);
              } else {
                this.superUsers.push(item);
              }
            });
          } else this.users = [...this.users, ...newUsers]; // Append new data
          this.totalActiveUsersRecords = response.total; // Update total records
          console.log(this.superAdmins, this.superUsers, 'getuserdata');
          // console.log('total records get from api:', this.totalRecords);
        } else {
          console.warn('Empty response or missing data field');
        }
      })
      .catch((error) => {
        console.error('Error fetching users:', error);
      })
      .finally(() => {
        this.isLoading = false; // Reset loading flag
      });

    console.log('Fetching page:', page); // Debug pagination
  }

  emailsShouldNotBeSame(control: AbstractControl): ValidationErrors | null {
    console.log('check');

    const emailPattern =
      /^[a-zA-Z][a-zA-Z0-9]*(?:[._-][a-zA-Z0-9]+)*@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,3}$/;

    const email = control.value['email'];

    if (email && !emailPattern.test(email)) {
      return { validEmail: true };
    }

    return null;
  }
  showUserDetails(item: any, id: any) {
    this.isUserSelected = true;
    this.selectedUserIndex = id;
    this.selectedUser = item;
    console.log(this.selectedUser, 'selectedUSer');
  }

  getSelectValue(event: any) {
    //  console.log(this.editUserForm.value);

    if (this.selectedRole == 2) {
      this.selectedPlantForAdmin = event;
      console.log(this.selectedPlantForAdmin, 'selected Admin');
    } else {
      this.selectedPlantForUser = event.target.value;
      console.log(this.selectedPlantForUser, 'selected User');
    }
  }

  @HostListener('scroll', ['$event'])
  onScroll(event: any): void {
    if (this.debounceFetch) {
      clearTimeout(this.debounceFetch);
    }

    this.debounceFetch = setTimeout(() => {
      const target = event.target;

      const scrolledPercentage =
        (target.scrollTop + target.clientHeight) / target.scrollHeight;

      // console.log('Scrolled percentage:', scrolledPercentage);

      if (scrolledPercentage > 0.9 && !this.isLoading) {
        const nextPage = Math.ceil(this.users.length / this.pageSize) + 1;
        console.log(
          'total records:',
          this.totalActiveUsersRecords,
          this.users.length,
          nextPage
        );
        if (this.users.length < this.totalActiveUsersRecords) {
          // console.log('Fetching more users...');
          this.getUsers(nextPage);
        } else {
          // console.log('All users loaded.');
        }
      }
    }, 150); // Debounce delay
  }

  resetEditForm() {
    //  this.isEditUserModalOpen=false;
    this.resetForm();
    //this.buildMyForm();
  }
  editUser(user?: any) {
    if (this.selectedUser || this.selectedUser != null) {
      // console.log(this.selectedUser, 'if check');
      this.profileImageUrl = this.selectedUser.profilePicture;
      // this.selectedRole=this.selectedUser.wbiRoleId;
      let bindPlantAdmin: any;
      let bindPlantUser: any;
      if (this.selectedUser.wbiRoleId == 2 && this.isSuperAdmin) {
        bindPlantAdmin = this.selectedUser.plantIds;
        this.selectedPlantForAdmin = this.selectedUser.plant;
        bindPlantUser = '';
        console.log(bindPlantAdmin, 'bindAdmin', this.selectedUser);
      } else if (this.selectedUser.wbiRoleId == 3 && this.isSuperAdmin) {
        bindPlantUser = this.selectedUser.plantIds[0];
        this.selectedPlantForUser = bindPlantUser;
        bindPlantAdmin = '';
        console.log(bindPlantUser, 'bindUser');
      }
      // this.editUserForm.setValue({userPlant:})
      let a = {
        firstName: this.selectedUser.firstName,
        lastName: this.selectedUser.lastName,
        email: this.selectedUser.email,
        contactNumber: this.selectedUser.contactNumber,
        wbiDepartmentId: this.selectedUser.wbiDepartmentId,
        wbiRoleId: this.selectedUser.wbiRoleId,
        gender: this.selectedUser.gender,
        applicationId: this.selectedUser.applicationId,
        userPlantForm: bindPlantUser || '',
        adminPlantForm: bindPlantAdmin || '',
      };
      // console.log(a, 'patch value obj');
      // return;
      setTimeout(() => {
        this.editUserForm.patchValue(a);
      }, 0);
    } else {
      console.log(JSON.stringify(user), 'else check');
      this.editUserForm.patchValue({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        contactNumber: user.contactNumber,
        wbiDepartmentId: user.wbiDepartmentId,
        wbiRoleId: user.wbiRoleId,
        gender: user.gender,
        applicationId: this.selectedUser.applicationId,
      });
    }
    this.isEditUserModalOpen = true;
  };


  updateUser(data:any){
    this.adminService.updateUser(data).then(
      (response) => {
        this.clickLoading = false;
        if (response.responseCode == 200) {
          this.toast.showSuccessToast('Record Updated Successfully');
          this.isEditUserModalOpen = false;
          this.getUsers(1);
          this.isUserSelected = false;
          this.selectedUser = null;
          this.selectedUserIndex = null;
        } else {
          this.toast.showSuccessToast(response.message);
        }
      },
      (err) => {
        this.toast.showErrorToast(err.message);
      }
    );
  }

  async onEditSubmit() {
    this.submitted = true;
    if (this.editUserForm.invalid) {
      return;
    }

    let plant: any = [];
    let plantIds: any = [];

    if (this.isSuperAdmin) {
      if (this.selectedRole == 2) {
        if (
          !this.selectedPlantForAdmin ||
          this.selectedPlantForAdmin.length === 0
        ) {
          this.showErr1 = true;
          setTimeout(() => {
            this.showErr1 = false;
          }, 5000);
          return;
        } else {
          this.selectedPlantForAdmin.forEach((item: any) => {
            plantIds.push(item.id);
            let a = {
              id: item.id,
            };
            plant.push(a);
          });
        }
      } else {
        if (!this.selectedPlantForUser) {
          this.showErr2 = true;
          setTimeout(() => {
            this.showErr2 = false;
          }, 5000);
          return;
        } else {
          plantIds = [this.selectedPlantForUser];
          plant = [{ id: this.selectedPlantForUser }];
        }
      }
    }

    this.clickLoading = true;
    var formValue = this.editUserForm.value;
    formValue['plantIds'] = plantIds.length > 0 ? plantIds : [this.plantId];
    formValue['plant'] = plant.length > 0 ? plant : [{ id: this.plantId }];
    formValue['wbiStatus'] = 1;
    formValue['wbiRoleId'] = parseInt(formValue['wbiRoleId']);
    formValue['wbiDepartmentId'] = parseInt(formValue['wbiDepartmentId']);
    formValue['applicationId'] = parseInt(formValue['applicationId']);
    if (this.selectedFile) {
      formValue['profilePicture'] = await this.uploadProfilePic();
    }
    delete formValue.plant;
    delete formValue.adminPlantForm;
    delete formValue.userPlantForm;
    var data = {
      tableName: 'admins',
      id: this.selectedUser.id,
      data: formValue,
    };

    // console.log(formValue);

    if (this.selectedUser.contactNumber != formValue.contactNumber) {
      this.adminService
        .checkContact({ contactNumber: formValue.contactNumber })
        .then((response) => {
          if (response.responseCode == 300) {
            this.toast.showErrorToast('Contact Number Already Exists.');
            this.clickLoading = false;
            return;
          }
          else{
            this.updateUser(data);
          }
        });
    }
    else {
      this.updateUser(data);
    }
    
    // return;

    
  };
  openMigrateModal() {
    this.openModal2('migrateModal');
  }
  openModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'block';
      modal?.classList.add('show');
      modal?.setAttribute('aria-hidden', 'false');
      modal?.setAttribute('aria-modal', 'true');
      modal?.setAttribute('role', 'dialog');
    }
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    document.body.appendChild(backdrop);

    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = '0px';
  }
  closeModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'none';
      modal?.classList.remove('show');
      modal?.setAttribute('aria-hidden', 'true');
      modal?.removeAttribute('aria-modal');
      modal?.removeAttribute('role');
      this.migrateForm.reset();
    }

    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.parentNode?.removeChild(backdrop);
    }

    const backdrop0 = document.querySelector('.modal-backdrop');
    if (backdrop0) {
      backdrop0.parentNode?.removeChild(backdrop0);
    }

    document.body.className = '';
    document.body.removeAttribute('style');
    document.body.removeAttribute('data-bs-overflow');
    document.body.removeAttribute('data-bs-padding-right');
  }
  migratePlantOfUser() {
    this.clickLoading = true;
    const formValues = this.migrateForm.value;
    formValues['adminId'] = this.selectedUser.id;
    formValues['transferedByAdminId'] = this.adminId;
    this.plantService.requestPlantTransfer(formValues).then((response) => {
      this.clickLoading = false;
      this.toast.showSuccessToast('Transfer request has been sent');
      this.closeModal2('migrateModal');
      this.getUsers(1);
      this.isUserSelected = false;
      this.selectedUser = null;
      this.selectedUserIndex = null;
      // console.log(response);
    });
  }
  closeEditUserModal() {
    this.isEditUserModalOpen = false;
    this.resetForm();
  }
  closeEditModal() {
    this.isEditUserModalOpen = false;
    this.resetForm();
  }
  clearFormErrors(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsUntouched();
      control?.markAsPristine();
    });
  }
  resetForm() {
    this.submitted = false;
    this.profileImageUrl = this.selectedUser.profilePicture;
    this.editUserForm.patchValue({
      firstName: this.selectedUser.firstName,
      lastName: this.selectedUser.lastName,
      email: this.selectedUser.email,
      contactNumber: this.selectedUser.contactNumber,
      wbiDepartmentId: this.selectedUser.wbiDepartmentId,
      wbiRoleId: this.selectedUser.wbiRoleId,
      gender: this.selectedUser.gender,
    });
    this.fileInput.nativeElement.value = ''; // Reset the file input
  }

  getInactiveUserList() {
    // const data = {
    //   page: 1,
    //   limit: this.inactiveUserPageSize,
    //   or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
    //   filter: [
    //     'wbiRoleId||eq||3',
    //     'enabled||eq||true',
    //     `plantIds||in||{${this.plantId}}`,
    //     'wbiStatus||eq||0',
    //   ],
    // };
    let data;
    data = {
      plantId: this.plantId,
      page: 1,
      limit: this.inactiveUserPageSize,
      wbiStatus: 0,
      wbiRoleId: [3],
    };
    // console.log('inactive params', data);

    // const param = createAxiosConfig(data);
    this.adminService.getAdminAndUsers(data).then((response) => {
      if (this.isSuperAdmin) {
        this.inactiveUsers = [
          ...response.data.map((user: { name: any }) => user),
        ];
      } else
        this.inactiveUsers = [
          ...this.inactiveUsers,
          ...response.data.map((user: { name: any }) => user),
        ]; // Append new data
      this.totalRecords = response.total;
    });
  }

  getPlantTransferRequest() {
    const data = {
      page: 1,
      limit: this.inactiveUserPageSize,
      status: 0,
      filter: [
        'status||eq||0',
        `plantId||eq||${this.plantId}`,
        'admin.applicationId||eq||2',
      ],
    };
    const param = createAxiosConfig(data);
    this.plantService.getPlantTransferRequest(param).then((response: any) => {
      this.plantTransferList = [
        ...this.plantTransferList,
        ...response.data.map((user: { name: any }) => user),
      ]; // Append new data
      this.totalRecords = response.total;
    });
  }
  getDepartment() {
    const data = {
      page: 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.adminService.getDepartment(param).then((response: any) => {
      this.departments = response.data;
    });
  }

  getAllPlant() {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: ['enabled||eq||true'],
    };
    const param = createAxiosConfig(data);
    this.plantService.getAllPlants(param).then((response) => {
      this.plantsForAdmin = response.data;
      this.plantsForUser = response.data;
      this.plants = response.data;
      this.plants = this.plants.filter(
        (element: { id: any }) => element.id !== this.plantId
      );
    });
  }
  approveUser(id: any) {
    this.clickLoading = true;
    console.log(id);
    const data = {
      tableName: 'admins',
      id: id,
      data: {
        wbiStatus: 1,
      },
    };
    this.adminService.approveUser(data).then((response) => {
      window.location.reload();
      this.inactiveUsers = [];
      this.toast.showSuccessToast('User approved');
      this.getInactiveUserList();
      this.clickLoading = false;
    });
  }

  rejectUser(id: any) {
    this.clickLoading = true;
    const data = {
      tableName: 'admins',
      id: id,
      data: {
        wbiStatus: 0,
      },
    };
    this.adminService.approveUser(data).then((response) => {
      this.inactiveUsers = [];
      this.toast.showSuccessToast('User rejected');
      this.getInactiveUserList();
      this.clickLoading = false;
    });
  }

  rejectTransfer(id: any) {
    this.clickLoading = true;
    const data = {
      tableName: 'plant-transfer-request',
      id: id,
      data: {
        status: 2,
      },
    };
    this.plantService.rejectTransferRequest(data).then((response) => {
      this.clickLoading = false;
      this.toast.showSuccessToast('Plant transfer request rejected');
      this.plantTransferList = this.plantTransferList.filter(
        (item: any) => item.id !== id
      );
    });
  }

  acceptPlantRequest(data: any) {
    this.clickLoading = true;
    const data1 = {
      id: data.id,
      approvedByAdminId: data.transferedByAdmin.id,
    };
    console.log(data1);
    this.plantService.approveTransferRequest(data1).then((response) => {
      this.clickLoading = false;
      this.toast.showSuccessToast('Plant transfer request accepted');
      this.plantTransferList = this.plantTransferList.filter(
        (item: any) => item.id !== data.id
      );
    });
  }

  onFileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (e.target?.result) {
          this.profileImageUrl = e.target.result as string; // Set the result as the preview URL
        }
      };
      reader.readAsDataURL(this.selectedFile); // Read the file as a data URL
      //this.uploadService.uploadImage(formData)
    } else {
      console.error('No file selected.');
    }
  }

  async uploadProfilePic(): Promise<string> {
    let url = '';
    if (this.selectedFile) {
      const formData = new FormData();
      // Append the selected file to form data
      formData.append('file', this.selectedFile);

      try {
        // Await the service call to upload the image
        url = await this.uploadService.uploadImage(formData);
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }
    return url;
  }

  deleteUser() {
    this.clickLoading = true;
    let data = {
      tableName: 'admins',
      id: this.selectedUser.id,
      data: {
        wbiStatus: 3,
      },
    };
    this.adminService.deleteUser(data).then((response) => {
      this.toast.showSuccessToast('User deleted');
      window.location.reload();
      this.clickLoading = false;
      this.closeDeleteModal();
      this.users = [];
      this.getUsers(1);
    });
  }
  openDeleteConfirmationModal() {
    const modalElement = document.getElementById('deleteModal');
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
  }

  giveAccessToBOG() {
    this.clickLoading = true;
    let data = {
      tableName: 'admins',
      id: this.selectedUser.id,
      data: {
        applicationId: 3,
      },
    };
    this.adminService.updateUser(data).then((response) => {
      this.toast.showSuccessToast('Access given to BOG');
      window.location.reload();
      this.clickLoading = false;
    });
  }

  closeDeleteModal() {
    const modalElement = document.getElementById('deleteModal');
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
      modalInstance.hide();
    }
  }
}
