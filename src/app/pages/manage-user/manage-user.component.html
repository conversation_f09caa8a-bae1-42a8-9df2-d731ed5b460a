<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>
<div class="header-container">
  <p class="fw-bold" style="margin-top: -8px">{{isSuperAdmin? 'Manage Roles': 'Manage User'}}</p>
  <div class="tab-container">
    <div class="tab-header">
      <div class="tab-headers">
        <div
          *ngFor="let tab of tabs; let i = index"
          [@tabSwitch]="i === selectedTabIndex ? 'active' : 'inactive'"
          [class.active]="i === selectedTabIndex"
          (click)="selectTab(i)"
          class="tab-header"
        >
          {{ tab.title }}
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="selectedTabIndex == 0" class="active-user">
  <div class="row">
    <div class="col-4">
      <div class="outer-container" (scroll)="onScroll($event)">
        <div class="tab-container2" *ngIf="isSuperAdmin">
          <div class="tab-header2">
            <div class="tab-headers2">
              <div
                *ngFor="let tab of tabs2; let i = index"
                [@tabSwitch]="i === selectedTabIndex2 ? 'active' : 'inactive'"
                [class.active]="i === selectedTabIndex2"
                (click)="selectTab2(i)"
                class="tab-header2"
              >
                {{ tab.title }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="no-data d-flex align-items-center justify-content-center fs-5 fw-bold"
          style="height: 100%"
          *ngIf="users.length == 0 && !this.isSuperAdmin"
        >
          No Data Found
        </div>
        <div
          class="no-data d-flex align-items-center justify-content-center fs-5 fw-bold"
          style="height: 100%"
          *ngIf="superUsers.length == 0 && this.isSuperAdmin && selectedTabIndex2==0"
        >
          No Data Found
        </div>
        <div
          class="no-data d-flex align-items-center justify-content-center fs-5 fw-bold"
          style="height: 100%"
          *ngIf="superAdmins.length == 0 && this.isSuperAdmin && selectedTabIndex2==1"
        >
          No Data Found
        </div>
       
        @if (this.isSuperAdmin && selectedTabIndex2==0) {
          <div class="user-list" *ngFor="let user of superUsers; let i = index">
            <div
              class="user-item"
              [ngClass]="{ selected: selectedUserIndex === i }"
              (click)="showUserDetails(user, i)"
            >
              <div class="user-avatar">
                <img
                  [src]="
                    user.profilePicture == null || user.profilePicture == ''
                      ? '../../../assets/svg/Avatar.svg'
                      : user.profilePicture
                  "
                  alt=""
                />
              </div>
              <div class="user-info">
                <p>{{ user.firstName }} {{ user.lastName }}</p>
              </div>
              <div class="arrow">
                <img
                  [src]="
                    selectedUserIndex === i
                      ? '../../../assets/svg/whitearrow.svg'
                      : '../../../assets/svg/bluearrow.svg'
                  "
                  alt="Arrow"
                />
              </div>
            </div>
          </div>
        }
        @else if (this.isSuperAdmin && selectedTabIndex2==1) {
          <div class="user-list" *ngFor="let user of superAdmins; let i = index">
            <div
              class="user-item"
              [ngClass]="{ selected: selectedUserIndex === i }"
              (click)="showUserDetails(user, i)"
            >
              <div class="user-avatar">
                <img
                  [src]="
                    user.profilePicture == null || user.profilePicture == ''
                      ? '../../../assets/svg/Avatar.svg'
                      : user.profilePicture
                  "
                  alt=""
                />
              </div>
              <div class="user-info">
                <p>{{ user.firstName }} {{ user.lastName }}</p>
              </div>
              <div class="arrow">
                <img
                  [src]="
                    selectedUserIndex === i
                      ? '../../../assets/svg/whitearrow.svg'
                      : '../../../assets/svg/bluearrow.svg'
                  "
                  alt="Arrow"
                />
              </div>
            </div>
          </div>
        }
        @else {
          <div class="user-list" *ngFor="let user of users; let i = index">
            <div
              class="user-item"
              [ngClass]="{ selected: selectedUserIndex === i }"
              (click)="showUserDetails(user, i)"
            >
              <div class="user-avatar">
                <img
                  [src]="
                    user.profilePicture == null || user.profilePicture == ''
                      ? '../../../assets/svg/Avatar.svg'
                      : user.profilePicture
                  "
                  alt=""
                />
              </div>
              <div class="user-info">
                <p>{{ user.firstName }} {{ user.lastName }}</p>
              </div>
              <div class="arrow">
                <img
                  [src]="
                    selectedUserIndex === i
                      ? '../../../assets/svg/whitearrow.svg'
                      : '../../../assets/svg/bluearrow.svg'
                  "
                  alt="Arrow"
                />
              </div>
            </div>
          </div>
        }
        <div class="loading-container" *ngIf="isLoading">
          <div
            class="spinner-border text-primary"
            style="width: 2rem; height: 2rem"
            role="status"
          >
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    </div>
    <!-- <img src="../../../assets/svg/whitearrow.svg" alt=""> -->
    <div class="col-8">
      <div
        class="profile-detail-container d-flex justify-content-center align-items-center"
        *ngIf="!isUserSelected"
      >
        <div class="fs-4 fw-bold">Select a User</div>
      </div>
      <div class="profile-detail-container" *ngIf="isUserSelected">
        <div class="profile-header">
          <div class="prof-img">
            <img
              [src]="
                selectedUser.profilePicture
                  || '../../../assets/svg/Avatar.svg'
              "
              alt="Profile Image"
            />
          </div>
          <div class="prof-name">
            {{ selectedUser.firstName }} {{ selectedUser.lastName }}
          </div>
        </div>
        <!-- Combined rows -->
        <div class="profile-details">
          <div class="row combined-row">
            <div class="col-3">
              <div>
                <div class="icon">
                  <img src="../../../assets/svg/plan-userDetail.svg" alt="" />
                </div>
                <div class="detail">
                  <div class="detail-title">Plant</div>
                  <div class="detail-value plant-values">
                    <div class="plant-scroll">
                      <span *ngFor="let plant of selectedUser.plant;let last=last">{{ plant.name }} <span *ngIf="!last">,</span> </span> 
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-3">
              <div>
                <div class="icon">
                  <img
                    src="../../../assets/svg/Profile-planDetail.svg"
                    alt=""
                  />
                </div>
                <div class="detail">
                  <div class="detail-title">Role</div>
                  <div class="detail-value">
                    {{
                      selectedUser.wbiRoleId === 3
                        ? "User"
                        : selectedUser.wbiRoleId === 2
                        ? "Plant Admin"
                        : "Super Admin"
                    }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-3">
              <div>
                <div class="icon">
                  <img
                    src="../../../assets/svg/email-userDetail.svg"
                    alt="Email"
                    class="email-icon"
                  />
                </div>
                <div class="detail">
                  <div class="detail-title">Email</div>
                  <div class="detail-value">{{ selectedUser.email }}</div>
                </div>
              </div>
            </div>
            <div class="col-3">
              <div>
                <div class="icon">
                  <img src="../../../assets/svg/call-userDetail.svg" alt="" />
                </div>
                <div class="detail">
                  <div class="detail-title">Contact Number</div>
                  <div class="detail-value">
                    {{ selectedUser.contactNumber }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-3">
              <div>
                <div class="icon">
                  <img src="../../../assets/svg/plan-userDetail.svg" alt="" />
                </div>
                <div class="detail">
                  <div class="detail-title">Application Access</div>
                  <div class="detail-value">
                    {{
                      selectedUser.applicationId == 1
                        ? "BOG"
                        : selectedUser.applicationId == 2
                        ? "WBI"
                        : "Both (WBI / BOG)"
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="actions ms-3">
          <app-ng-btn
            [buttonText]="'Migrate'"
            [btnType]="'Submit'"
            [isImg]="true"
            *ngIf="!isSuperAdmin"
            [imgHref]="'../../../assets/svg/right-arrow.svg'"
            [imgAlign]="'right'"
            [imgHeight]="60"
            [imgWidth]="60"
            [btnWidth]="200"
            (buttonClicked)="openMigrateModal()"
          ></app-ng-btn>
          <button class="button-back button-left" (click)="editUser()">
            <span class="ps-4">Edit</span>
            <img
              alt=""
              src="../../../assets/svg/Edit-User.svg"
              class="left-arrow"
            />
          </button>
          <!-- Added Delete Button with Bootstrap Icon (Trash) -->
          <button
            class="btn adani-gradient"
            (click)="openDeleteConfirmationModal()"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="selectedTabIndex == 1" class="inactive-user">
  <table class="table table-bordered enhanced-table">
    <thead>
      <tr>
        <th>Action</th>
        <th>User Details</th>
        <th>Assign Plant</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of inactiveUsers">
        <td class="action-column">
          <div class="profile-pic">
            <img
              [src]="
                user.profilePicture == null || user.profilePicture == ''
                  ? '../../../assets/svg/Avatar.svg'
                  : user.profilePicture
              "
              alt="User Profile"
              class="profile-image"
            />
          </div>
          <div class="action-buttons">
            <div class="row">
              <button
                class="btn btn-success btn-icon"
                (click)="approveUser(user.id)"
                title="Accept"
              >
                <i class="bi bi-check-lg"></i>
              </button>
              <button
                class="btn btn-danger btn-icon"
                (click)="rejectUser(user.id)"
                title="Reject"
              >
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
          </div>
        </td>
        <td>
          <p><strong>Name:</strong> {{ user.firstName }} {{ user.lastName }}</p>
          <p>
            <strong>Date of Birth:</strong>
            {{ user.dob | date : "dd-MMM-yyyy" }}
          </p>
          <p>
            <strong>Gender:</strong>
            {{
              user.gender == 2 ? "Other" : user.gender == 1 ? "Male" : "Female"
            }}
          </p>
          <p>
            <strong>Role:</strong>
            {{ user.adminsRoleId == 3 ? "User" : "Admin" }}
          </p>
          <p><strong>Email:</strong> {{ user.email }}</p>
          <p><strong>Contact Number:</strong> {{ user.contactNumber }}</p>
        </td>
        <td>
          <p>{{ user.plant[0].name }}</p>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div *ngIf="selectedTabIndex == 2" class="plant-transfer">
  <table class="table table-bordered enhanced-table">
    <thead>
      <tr>
        <th>Action</th>
        <th>User Details</th>
        <th>Plant Transfer</th>
        <th>Transfer Requested By</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let transferList of plantTransferList">
        <td class="action-column">
          <div class="profile-pic">
            <img
              [src]="
                transferList.admin.profilePicture == null ||
                transferList.admin.profilePicture == ''
                  ? '../../../assets/svg/Avatar.svg'
                  : transferList.admin.profilePicture
              "
              alt="User Profile"
              class="profile-image"
            />
          </div>
          <div class="action-buttons">
            <div class="row">
              <button
                class="btn btn-success btn-icon"
                (click)="acceptPlantRequest(transferList)"
                title="Accept"
              >
                <i class="bi bi-check-lg"></i>
              </button>
              <button
                class="btn btn-danger btn-icon"
                (click)="rejectTransfer(transferList.id)"
                title="Reject"
              >
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
          </div>
        </td>
        <td class="user-details">
          <p>
            <strong class="user-detail-label">Name:</strong>
            {{ transferList.admin.firstName }} {{ transferList.admin.lastName }}
          </p>
          <p>
            <strong class="user-detail-label">Date of Birth:</strong>
            {{ transferList.admin.dob }}
          </p>
          <p>
            <strong class="user-detail-label">Gender:</strong>
            {{ transferList.admin.gender }}
          </p>
          <p>
            <strong class="user-detail-label">Role:</strong>
            {{ transferList.admin.adminsRoleId == 1 ? "User" : "Admin" }}
          </p>
          <p>
            <strong class="user-detail-label">Email:</strong>
            {{ transferList.admin.email }}
          </p>
          <p>
            <strong class="user-detail-label">Contact Number:</strong>
            {{ transferList.admin.contactNumber }}
          </p>
        </td>
        <td>
          <p>{{ transferList.plant.name }}</p>
        </td>
        <td>
          <p>
            <strong class="user-detail-label">Plant:</strong>
            {{ transferList.transferedByAdmin.plant }}
          </p>
          <p>
            <strong class="user-detail-label">Name:</strong>
            {{ transferList.transferedByAdmin.firstName }}
            {{ transferList.transferedByAdmin.firstName }}
          </p>
          <p>
            <strong class="user-detail-label">Date of Birth:</strong>
            {{ transferList.transferedByAdmin.dob }}
          </p>
          <p>
            <strong class="user-detail-label">Gender:</strong>
            {{ transferList.transferedByAdmin.gender }}
          </p>
          <p>
            <strong class="user-detail-label">Role:</strong>
            {{
              transferList.transferedByAdmin.adminsRoleId == 1
                ? "User"
                : "Admin"
            }}
          </p>
          <p>
            <strong class="user-detail-label">Email:</strong>
            {{ transferList.transferedByAdmin.email }}
          </p>
          <p>
            <strong class="user-detail-label">Contact Number:</strong>
            {{ transferList.transferedByAdmin.contactNumber }}
          </p>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div style="height: 50px"></div>
<div
  class="modal fade"
  tabindex="-1"
  id="migrateModal"
  aria-labelledby="migrateModalLabel"
  #migrateModal
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header confirmModalHeader justify-content-between">
        <h5 class="modal-title">Migrate</h5>
        <button
          type="button"
          class="btn"
          data-bs-dismiss="modal"
          aria-label="Close"
          (click)="this.closeModal2('migrateModal')"
          style="font-size: 16px; font-weight: 600; color: #fff"
        >
          X
        </button>
      </div>
      <div class="modal-body confirmModalBody">
        <form [formGroup]="migrateForm">
          <label class="fw-bold">Current Plant</label>
          <input
            type="text"
            class="form-control my-2"
            value="{{ currentUserPlant.name || 'Current Plant'}}"
            disabled
          />

          <div>
            <label for="transfer" class="fw-bold">Transfer to :</label>
            <select
              formControlName="plantId"
              class="form-select my-2"
              id="transfer"
            >
              <option value="">Select Plant</option>
              <option *ngFor="let plant of plants" [value]="plant.id">
                {{ plant.name }}
              </option>
            </select>
            <div
              *ngIf="
                migrateForm.get('plantId')?.touched &&
                migrateForm.get('plantId')?.invalid
              "
              class="invalid-feedback d-block"
            >
              <div *ngIf="migrateForm.get('plantId')?.errors?.['required']">
                Please select a plant.
              </div>
            </div>
          </div>

          <div class="d-flex mt-4 justify-content-center" style="gap: 10px">
            <button
              type="button"
              id="bt_approve"
              class="button-submit button-left"
              (click)="migratePlantOfUser()"
              [disabled]="migrateForm.invalid"
            >
              <span class="ps-5">Submit</span>
              <img src="../../../assets/svg/accept.svg" class="left-arrow" />
            </button>
            <button
              type="button"
              id="bt_reject"
              class="button-back button-left"
              (click)="this.closeModal2('migrateModal')"
            >
              <span class="ps-5">Cancel</span>
              <img src="../../../assets/svg/reject.svg" class="left-arrow" />
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<app-custom-modal
  [title]="'Edit User'"
  *ngIf="isEditUserModalOpen && editUserForm"
  (closeModal)="closeEditUserModal()"
  (onClickCross)="closeEditModal()"
>
  <div>
    <form [formGroup]="editUserForm" (ngSubmit)="onEditSubmit()">
      <div class="input-text-group text-center">
        <img
          [src]="profileImageUrl || '../../../assets/svg/Avatar.svg'"
          alt="Profile Picture"
          class="profile-avatar mb-3"
          width="100"
          height="100"
          style="border-radius: 50%; object-fit: cover"
        />
        <input
          type="file"
          class="form-control form-control-lg"
          id="profile"
          #fileInput
          (change)="onFileChange($event)"
          accept="image/*"
        />
      </div>
      <div class="input-text-group">
        <label for="role">Role</label>
        <select class="form-control" formControlName="wbiRoleId" id="role">
          <option value="">Select Role</option>
          <option value="3">User</option>
          <option value="2" *ngIf="isSuperAdmin">Admin</option>
        </select>
        <span
          id="txt_roll_error"
          class="text-danger"
          *ngIf="(editUserForm.get('wbiRoleId')?.touched || submitted) && editUserForm.get('wbiRoleId')?.errors?.['required']"
        >
          Please select a role.
        </span>
      </div>

      <div class="input-text-group">
        <label for="firstName">First Name</label>
        <input
          type="text"
          class="form-control"
          formControlName="firstName"
          placeholder="Edit First Name"
          oninput="event.target.value=event.target.value.trimStart('')"
        />
        <span
          id="txt_firstName_error"
          class="text-danger"
          *ngIf="(editUserForm.get('firstName')?.touched || submitted) && editUserForm.get('firstName')?.errors?.['required']"
        >
          First Name is required.
        </span>
        <span
          id="txt_firstName_pattern_error"
          class="text-danger"
          *ngIf="(editUserForm.get('firstName')?.touched || submitted) && editUserForm.get('firstName')?.errors?.['pattern']"
        >
          First Name must be alphabetic and 1-15 characters long.
        </span>
      </div>
      <div class="input-text-group">
        <label for="lastName">Last Name</label>
        <input
          type="text"
          class="form-control"
          formControlName="lastName"
          placeholder="Edit Last Name"
          oninput="event.target.value=event.target.value.trimStart('')"
        />
        <span
          id="txt_lastName_error"
          class="text-danger"
          *ngIf="(editUserForm.get('lastName')?.touched || submitted) && editUserForm.get('lastName')?.errors?.['required']"
        >
          Last Name is required.
        </span>
        <span
          id="txt_lastName_pattern_error"
          class="text-danger"
          *ngIf="(editUserForm.get('lastName')?.touched || submitted) && editUserForm.get('lastName')?.errors?.['pattern']"
        >
          Last Name must be alphabetic and 1-15 characters long.
        </span>
      </div>
      <div class="input-text-group">
        <label for="gender">Gender</label>
        <select class="form-control" formControlName="gender" id="gender">
          <option value="">Select Gender</option>
          <option value="1">Male</option>
          <option value="0">Female</option>
          <option value="2">Other</option>
        </select>
        <span
          id="txt_gender_error"
          class="text-danger"
          *ngIf="(editUserForm.get('gender')?.touched || submitted) && editUserForm.get('gender')?.errors?.['required']"
        >
          Please select a gender.
        </span>
      </div>
      <div class="input-text-group">
        <label for="email">Email</label>
        <input
          type="text"
          [readOnly]="true"
          class="form-control"
          formControlName="email"
          placeholder="Edit Email"
          style="background-color: #E9ECEF"
          oninput="event.target.value=event.target.value.trimStart('')"
          readonly
        />
        <div
          id="txt_email_error"
          class="text-danger validation"
          *ngIf="
            editUserForm.get('email')?.value &&
            editUserForm.hasError('validEmail')
          "
        >
          Invalid Email ID.
        </div>
        <span
          id="txt_email_error"
          class="text-danger"
          *ngIf="(editUserForm.get('email')?.touched || submitted) && editUserForm.get('email')?.errors?.['required']"
        >
          Email is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="contact">Contact No.</label>
        <input
          type="text"
          [readOnly]="!isSuperAdmin"
          class="form-control"
          formControlName="contactNumber"
          [ngStyle]="isSuperAdmin?{}:{'background-color': '#E9ECEF'}"
          placeholder="Edit Contact"
          onkeypress="return event.charCode >= 48 && event.charCode <= 57"
          maxlength="10"
        />
        <span
          id="txt_contactNumber_error"
          class="text-danger"
          *ngIf="(editUserForm.get('contactNumber')?.touched || submitted) && editUserForm.get('contactNumber')?.errors?.['required'] || editUserForm.get('contactNumber')?.touched && editUserForm.get('contactNumber')?.invalid"
        >
          Please enter valid Mobile Number.
        </span>
      </div>
      <div class="input-text-group">
        <label for="applicationId">Application</label>
        <select class="form-control" formControlName="applicationId">
          <option value="">Select Application</option>
          <option *ngFor="let app of applications" [value]="app.value">
            {{ app.name }}
          </option>
        </select>
        <span
          id="txt_department_error"
          class="text-danger"
          *ngIf="(editUserForm.get('applicationId')?.touched || submitted) && editUserForm.get('applicationId')?.errors?.['required']"
        >
          Application is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="wbiDepartmentId">Department</label>
        <select class="form-control" formControlName="wbiDepartmentId">
          <option value="">Select Department</option>
          <option *ngFor="let dept of departments" [value]="dept.id">
            {{ dept.title }}
          </option>
        </select>
        <span
          id="txt_department_error"
          class="text-danger"
          *ngIf="(editUserForm.get('wbiDepartmentId')?.touched || submitted) && editUserForm.get('wbiDepartmentId')?.errors?.['required']"
        >
          Department is required.
        </span>
      </div>

      <div class="input-text-group" *ngIf="selectedRole == 3 && isSuperAdmin">
        <label for="Plant">Select Plant</label>
        <select
          class="form-control"
          (change)="getSelectValue($event)"
          formControlName="userPlantForm"
        >
          <option value="">Select Plant</option>
          <option
            *ngFor="let plant of plantsForUser"
            [value]="plant.id"
          >
            {{ plant.name }}
          </option>
        </select>
        <span
        id="txt_department_error"
        class="text-danger"
        *ngIf="showErr2"
      >
        Plant is required.
      </span>
      </div>

      <div class="input-text-group" *ngIf="selectedRole == 2 && isSuperAdmin">
        <label for="plantIds" class="form-label">Select Plants</label>
        <ng-select
        style="margin-top: -5px;"
        class="custom-ng-select"
        formControlName="adminPlantForm"
          [items]="plantsForAdmin"
          bindLabel="name"
          bindValue="id"
          [multiple]="true"
          placeholder="Select plants"
          id="plantIds"
          [closeOnSelect]="false"
          [clearable]="true"
          (change)="getSelectValue($event)"
        >
        </ng-select>
        <span
        id="txt_department_error"
        class="text-danger"
        *ngIf="showErr1"
      >
        Please select atleast one plant.
      </span>
      </div>

      <div class="d-flex mt-4 btn-section ms-1">
        <button class="button-submit" type="submit">Submit</button>
        <button type="button" (click)="resetEditForm()" class="button-back">
          Reset
        </button>
      </div>
      <br>
    </form>
  </div>
</app-custom-modal>
<div
  class="modal fade"
  id="deleteModal"
  tabindex="-1"
  aria-labelledby="deleteModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <h5 class="modal-title" id="uploadModalLabel">Delete Confirmation</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <p>Are you sure, you want to delete this user ?</p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button
            type="button"
            id="bt_approve"
            class="button-submit button-left"
            (click)="deleteUser()"
          >
            <span class="ps-5">Yes</span>
            <img
              alt=""
              src="../../../assets/svg/right-arrow.svg"
              class="left-arrow"
            />
          </button>
          <button
            type="button"
            id="bt_reject"
            class="button-back button-left"
            (click)="closeDeleteModal()"
          >
            <span class="ps-5">No</span>
            <img
              alt=""
              src="../../../assets/svg/Edit-User.svg"
              class="left-arrow"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
