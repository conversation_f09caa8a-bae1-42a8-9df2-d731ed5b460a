import { Component, OnInit, ViewChild, ChangeDetectorRef, AfterViewInit } from "@angular/core";
import {
  ApexAxisChartSeries,
  ApexChart,
  ChartComponent,
  ApexDataLabels,
  ApexPlotOptions,
  ApexYAxis,
  ApexXAxis,
  ApexFill,
  ApexTooltip,
  ApexStroke,
  ApexLegend,
  NgApexchartsModule
} from "ng-apexcharts";
import { AdminService } from "../../../services/admin/admin.service";
import { CommonModule } from '@angular/common';
import { AdminPlantSelectionService } from "../../../services/admin/admin-plant-selection.service";
import { filter } from "rxjs";

export type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  dataLabels: ApexDataLabels;
  plotOptions: ApexPlotOptions;
  yaxis: ApexYAxis;
  xaxis: ApexXAxis;
  fill: ApexFill;
  tooltip: ApexTooltip;
  stroke: ApexStroke;
  legend: ApexLegend;
  title: any;
};

@Component({
  selector: 'app-work-summary-graph',
  templateUrl: './work-summary-graph.component.html',
  standalone: true,
  imports: [NgApexchartsModule, CommonModule],
  styleUrls: ['./work-summary-graph.component.scss']
})
export class WorkSummaryGraphComponent implements OnInit, AfterViewInit {
  adminId: any;
  plantId: any;
  isLoading: boolean = true;
  chartInitialized: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  public chartOptions: Partial<ChartOptions>;
  selectedplantId: any;
  isSuperAdmin:boolean=false;

  constructor(private adminService: AdminService, private cdr: ChangeDetectorRef, private adminPlantSelection: AdminPlantSelectionService) {
    this.chartOptions = {
      series: [], // Initialize as empty
      chart: {
        type: "bar",
        height: 350,
        toolbar:{
          show:false,
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%"
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"]
      },
      xaxis: {
        categories: []
      },
      yaxis: {
        title: {
          text: "Count"
        }
      },
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + " items";
          }
        }
      },
      title: {
        text: 'Work Summary (Week Wise)',
        align: 'left'
      }
    };
  }

  ngOnInit(): void {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
   if (this.isSuperAdmin) {
    this.adminPlantSelection.selectedPlant$.pipe(
              filter((plantId) => !!plantId) 
            ).subscribe(adminPlantId => {
        this.selectedplantId = adminPlantId;
        this.getAdminDetail();
        this.getWeeklyWorkSummaryGraph();
    });
   }
   else{
    this.getAdminDetail();
    this.getWeeklyWorkSummaryGraph();
  }
  }

  ngAfterViewInit(): void {
    this.getWeeklyWorkSummaryGraph();
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? '{}');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId ? this.selectedplantId : currentUser.plantIds[0]
  }

  private getWeeklyWorkSummaryGraph() {
    this.isLoading = true;
    const data = { plantId: this.plantId };
    this.adminService.weekWiseReportGraph(data).then((response) => {
      this.isLoading = false;
      if (response.data && response.data.length > 0) {
        this.processWeeklyData(response.data);
        this.chartInitialized = true;
      } else {
        console.error("No data found");
        this.chartInitialized = true; // Important: Set even if no data
      }
    }).catch((error) => {
      this.isLoading = false;
      console.error("Error fetching graph data:", error);
      this.chartInitialized = true; // Important: Set even on error
    });
  }

  private processWeeklyData(data: any) {
    const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    const weeklyData: { [day: string]: { [countType: string]: number } } = {};

    for (const item of data) {
      const date = new Date(item.day); // Parse the date from 'day'
      const weekdayIndex = date.getDay(); // Get day index (0-6)
      const weekday = daysOfWeek[weekdayIndex]; // Convert index to name

      if (!weeklyData[weekday]) {
        weeklyData[weekday] = {
          "Scan Equipment Count": 0,
          "Standby Equipment Count": 0,
          "Total Equipment Count": 0,
        };
      }

      weeklyData[weekday]["Scan Equipment Count"] += parseInt(item.scan_equipment_count, 10) || 0;
      weeklyData[weekday]["Standby Equipment Count"] += parseInt(item.standby_count, 10) || 0;
      weeklyData[weekday]["Total Equipment Count"] += parseInt(item.total_equipment, 10) || 0;
    }

    // **Sort categories based on days of the week order**
    const categories = daysOfWeek.filter(day => weeklyData[day]); // Keep only days that exist in data

    const scanEquipmentData: number[] = categories.map(day => weeklyData[day]["Scan Equipment Count"]);
    const standbyEquipmentData: number[] = categories.map(day => weeklyData[day]["Standby Equipment Count"]);
    const totalEquipmentData: number[] = categories.map(day => weeklyData[day]["Total Equipment Count"]);

    this.chartOptions = {
      ...this.chartOptions,
      series: [
        { name: "Scan Equipment Count", data: scanEquipmentData },
        { name: "Standby Equipment Count", data: standbyEquipmentData },
        { name: "Total Equipment Count", data: totalEquipmentData }
      ],
      xaxis: {
        categories: categories // Sorted day names
      }
    };
  
  

    // console.log(weeklyData);

    this.cdr.detectChanges();
  }


}