<div class="loading-container" *ngIf="!chartInitialized">
  <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
<div *ngIf="chartInitialized && !isLoading">
  <apx-chart
    [series]="chartOptions.series"
    [chart]="chartOptions.chart"
    [dataLabels]="chartOptions.dataLabels"
    [plotOptions]="chartOptions.plotOptions"
    [yaxis]="chartOptions.yaxis"
    [xaxis]="chartOptions.xaxis"
    [fill]="chartOptions.fill"
    [tooltip]="chartOptions.tooltip"
    [stroke]="chartOptions.stroke"
    [title]="chartOptions.title"
    #chart
  ></apx-chart>
</div>
