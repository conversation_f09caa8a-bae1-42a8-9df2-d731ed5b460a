<div>
    <apx-chart *ngIf="chartOptions != null"
      [series]="chartOptions!.series" 
      [chart]="chartOptions!.chart!" 
      [xaxis]="chartOptions!.xaxis!" 
      [dataLabels]="chartOptions!.dataLabels!" 
      [plotOptions]="chartOptions!.plotOptions!" 
      [title]="chartOptions!.title!">
    </apx-chart>
    <div class="loading-container" *ngIf="chartOptions == null">
      <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
