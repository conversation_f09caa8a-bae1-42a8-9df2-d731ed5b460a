import { Component, OnInit } from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTitleSubtitle,
  ApexPlotOptions,
  NgApexchartsModule,
} from 'ng-apexcharts';
import { StandByEquipmentsService } from '../../../services/stand-by-equipments/stand-by-equipments.service';
import { CommonModule } from '@angular/common'; // Import CommonModule for *ngIf
import { AdminPlantSelectionService } from '../../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';

@Component({
  selector: 'app-stand-by-equipment-graph',
  templateUrl: './stand-by-equipment-graph.component.html',
  standalone: true,
  imports: [NgApexchartsModule, CommonModule],
  styleUrls: ['./stand-by-equipment-graph.component.scss'], // Corrected to styleUrls
})
export class StandByEquipmentGraphComponent implements OnInit {
  adminId: any;
  plantId: any;
  currentUserPlant: any;
  selectedplantId: any;
  isSuperAdmin: boolean = false;
  public chartOptions: Partial<{
    series: ApexAxisChartSeries;
    chart: ApexChart;
    xaxis: ApexXAxis;
    dataLabels: ApexDataLabels;
    title: ApexTitleSubtitle;
    plotOptions: ApexPlotOptions;
    colors: string[];
  }> | null = null; // Nullable with Partial<> for optional properties

  constructor(
    private equipmentService: StandByEquipmentsService,
    private adminPlantSelection: AdminPlantSelectionService
  ) {}

  ngOnInit(): void {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.getStandByGraph();
        });
    } else {
      this.getAdminDetail();
      this.getStandByGraph();
    }
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    this.currentUserPlant = currentUser.plant[0];
  }

  private getStandByGraph(): void {
    const data = { plantId: this.plantId };
    this.equipmentService
      .standByEquipmentGraph(data)
      .then((response) => {
        if (response.status === 200 && response.data) {
          this.mapGraphData(response.data);
        }
      })
      .catch((error) => {
        console.error('Error fetching graph data:', error);
      });
  }

  private mapGraphData(data: any[]): void {
    // Extract months and counts from the response
    const months: string[] = [];
    const counts: number[] = [];

    data
      .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime()) // Sort chronologically
      .forEach((item) => {
        const monthName = new Date(item.month).toLocaleString('default', {
          month: 'long',
        });
        months.push(monthName);
        counts.push(Number(item.count));
      });

    // Generate dynamic colors
    const colors = this.generateDynamicColors(counts.length);

    // Update chart options
    this.chartOptions = {
      series: [
        {
          name: 'Standby Equipments',
          data: counts,
        },
      ],
      chart: {
        type: 'bar',
        height: 350,
        toolbar:{
          show:false,
        }
      },
      title: {
        text: 'Standby Equipment (Month Wise)',
        align: 'left',
      },
      xaxis: {
        categories: months,
      },
      dataLabels: {
        enabled: true,
      },
      plotOptions: {
        bar: {
          distributed: true, // Enable distributed colors
          columnWidth: '50%',
        },
      },
      colors, // Assign dynamic colors
    };
  }

  private generateDynamicColors(count: number): string[] {
    return Array.from(
      { length: count },
      () => `#${Math.floor(Math.random() * 16777215).toString(16)}`
    );
  }
}
