import { Component, OnInit } from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTitleSubtitle,
  ApexPlotOptions,
  NgApexchartsModule,
} from 'ng-apexcharts';
import { AdminService } from '../../../services/admin/admin.service';
import { CommonModule } from '@angular/common'; // Import CommonModule for *ngIf
import { AdminPlantSelectionService } from '../../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';

@Component({
  selector: 'app-register-user-graph',
  standalone: true,
  imports: [NgApexchartsModule, CommonModule],
  templateUrl: './register-user-graph.component.html',
  styleUrl: './register-user-graph.component.scss',
})
export class RegisterUserGraphComponent implements OnInit {
  adminId: any;
  plantId: any;
  currentUserPlant: any;
  selectedplantId: any;
  isSuperAdmin: boolean = false;
  public chartOptions: Partial<{
    series: ApexAxisChartSeries;
    chart: ApexChart;
    xaxis: ApexXAxis;
    dataLabels: ApexDataLabels;
    title: ApexTitleSubtitle;
    plotOptions: ApexPlotOptions;
  }> | null = null; // Nullable with Partial<> for optional properties

  constructor(
    private adminService: AdminService,
    private adminPlantSelection: AdminPlantSelectionService
  ) {}

  ngOnInit(): void {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.getRegisterUserGraph();
        });
    } else {
      this.getAdminDetail();
      this.getRegisterUserGraph();
    }
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    this.currentUserPlant = currentUser.plant[0];
  }

  private getRegisterUserGraph(): void {
    const data = { plantId: this.plantId };

    this.adminService
      .getRegisterUserGraph(data)
      .then((response) => {
        if (response.status === 200 && response.data) {
          console.log('response.data', response.data);

          const formattedData = this.formatGraphData(response.data);
          this.updateChartOptions(formattedData.months, formattedData.records);
        }
      })
      .catch((error) => {
        console.error('Error fetching graph data:', error);
      });
  }

  private formatGraphData(
    data: Array<{ month: string; total_records: string }>
  ) {
    const months: string[] = [];
    const records: number[] = [];

    // Sort the data array by the month in chronological order
    data.sort(
      (a, b) => new Date(a.month).getTime() - new Date(b.month).getTime()
    );

    data.forEach((item) => {
      const date = new Date(item.month);
      const monthName = date.toLocaleString('default', { month: 'long' }); // Converts to full month name
      months.push(monthName);
      records.push(Number(item.total_records));
    });

    return { months, records };
  }

  private updateChartOptions(months: string[], records: number[]): void {
    this.chartOptions = {
      series: [
        {
          name: 'Register',
          data: records,
        },
      ],
      chart: {
        type: 'bar',
        height: 350,
        toolbar:{
          show:false
        }
      },
      title: {
        text: 'Register User (Month Wise)',
        align: 'left',
      },
      xaxis: {
        categories: months,
      },
      dataLabels: {
        enabled: true,
      },
      plotOptions: {
        bar: {
          distributed: true,
          columnWidth: '50%',
        },
      },
    };
  }
}
