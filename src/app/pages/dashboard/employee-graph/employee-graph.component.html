<div *ngIf="chartInitialized"> <apx-chart
  [series]="chartOptions.series"
  [chart]="chartOptions.chart"
  [labels]="chartOptions.labels"
  [responsive]="chartOptions.responsive"
  [title]="chartOptions.title"
  #chart
></apx-chart>
</div>
<div class="loading-container" *ngIf="!chartInitialized">
  <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
