import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  ApexNonAxisChartSeries,
  ApexResponsive,
  ApexChart
} from "ng-apexcharts";
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { AdminService } from '../../../services/admin/admin.service';
import { CommonModule } from '@angular/common';
import { AdminPlantSelectionService } from '../../../services/admin/admin-plant-selection.service';

export type ChartOptions = {
  series: ApexNonAxisChartSeries;
  chart: ApexChart;
  responsive: ApexResponsive[];
  labels: any;
  title: any;
};

@Component({
  selector: 'app-employee-graph',
  templateUrl: './employee-graph.component.html',
  standalone: true,
  imports: [NgApexchartsModule, CommonModule],
  styleUrls: ['./employee-graph.component.scss']
})
export class EmployeeGraphComponent implements OnInit, AfterViewInit {
  adminId: any;
  plantId: any;
  isSuperAdmin:boolean=false;
  activeUserCount: number = 0;
  inActiveUserCount: number = 0;
  currentUserPlant: any;
  chartInitialized: boolean = false; // Flag to track chart initialization
  @ViewChild("chart") chart: ChartComponent | undefined;
  public chartOptions!: Partial<ChartOptions>;
  selectedplantId: any;

  constructor(private adminService: AdminService, private adminPlantSelection: AdminPlantSelectionService) {
    this.chartOptions = {
      series: [],
      chart: {
        type: "donut",
        height: 350,
      },
      labels: [],
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
              height: 350,
            },
            legend: {
              position: "bottom"
            }
          }
        }
      ],
      title: {
        text: 'Employee',
        align: 'left'
      },
    };
  }

  ngOnInit(): void {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;

    this.adminPlantSelection.selectedPlant$.subscribe(adminPlantId => {
      this.selectedplantId = adminPlantId;
      this.getAdminDetail();

      // Reload data when plant changes
      if (this.chartInitialized) {
        this.loadUserCounts().then(() => {
          this.updateChart();
        });
      }
    });

    // Initial load
    this.getAdminDetail();
  }

  ngAfterViewInit(): void {
    this.initializeChartData();
  }

  private initializeChartData(): void {
    this.loadUserCounts().then(() => {
      this.updateChart();
      this.chartInitialized = true;
    });
  }


  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId ? this.selectedplantId : currentUser.plantIds[0]
    this.currentUserPlant = currentUser.plant[0];
  }

  private async loadUserCounts(): Promise<void> {
    try {
      this.activeUserCount = await this.getActiveUser();
      this.inActiveUserCount = await this.getInActiveUser();
    } catch (error) {
      console.error("Error loading user counts:", error);
      // Handle error appropriately, e.g., display an error message
    }
  }

  private getActiveUser(): Promise<number> {
    const data = {
      or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
      filter: [
        'wbiRoleId||eq||3',
        'wbiStatus||eq||1',
        `plantIds||in||{${this.plantId}}`
      ]
    };
    const param = createAxiosConfig(data);
    return this.adminService.getUsers(param).then((response) => response.length);
  }

  private getInActiveUser(): Promise<number> {
    const data = {
      or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
      filter: [
        'wbiRoleId||eq||3',
        'wbiStatus||eq||0',
        `plantIds||in||{${this.plantId}}`
      ]
    };
    const param = createAxiosConfig(data);
    return this.adminService.getUsers(param).then((response) => response.length);
  }

  private updateChart(): void {
    this.chartOptions = {
      ...this.chartOptions,
      series: [this.activeUserCount, this.inActiveUserCount],
      labels: ["Active Users", "Inactive Users"],
    };

    // Force chart redraw
    if (this.chart) {
      this.chart.updateOptions(this.chartOptions).catch(error => {
        console.error('Error updating chart:', error);
      });
    }
  }
}