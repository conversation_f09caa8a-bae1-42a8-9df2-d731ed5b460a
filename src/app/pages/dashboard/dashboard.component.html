<div class="row statistic justify-content-center">
    <div class="col-12 col-md-6 col-lg-3 mb-4" *ngFor="let stat of dataArray">
        <app-statistic [title]="stat.title" [count]="stat.count" [primaryColor]="stat.primaryColor"
            [secondaryColor]="stat.secondaryColor"></app-statistic>
    </div>
</div>
<div class="row graphs">
    <div class="col-12 col-md-6">
        <div class="card graph-card">
            <app-register-user-graph></app-register-user-graph>
        </div>
    </div>
    <div class="col-12 col-md-6 ">
        <div class="card graph-card">
            <app-employee-graph></app-employee-graph>
        </div>
    </div>
    <div class="col-12 col-md-6">
        <div class="card graph-card">
            <app-stand-by-equipment-graph></app-stand-by-equipment-graph>
        </div>
    </div>
    <div class="col-12 col-md-6">
        <div class="card graph-card">
            <app-work-summary-graph></app-work-summary-graph>
        </div>
    </div>
</div>
<div style="height: 35px"></div>