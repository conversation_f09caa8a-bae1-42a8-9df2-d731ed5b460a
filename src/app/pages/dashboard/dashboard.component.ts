import { Component, OnInit } from '@angular/core';
import { StatisticComponent } from './statistic/statistic.component';
import { CommonModule } from '@angular/common'; // Import CommonModule
import { RegisterUserGraphComponent } from './register-user-graph/register-user-graph.component';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { EmployeeGraphComponent } from './employee-graph/employee-graph.component';
import { WorkSummaryGraphComponent } from './work-summary-graph/work-summary-graph.component';
import { AdminService } from '../../services/admin/admin.service';
import { SectionsService } from '../../services/sections/sections.service';
import { StandByEquipmentsService } from '../../services/stand-by-equipments/stand-by-equipments.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { StandByEquipmentGraphComponent } from './stand-by-equipment-graph/stand-by-equipment-graph.component';
import { Router } from '@angular/router';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter, take } from 'rxjs/operators';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    StatisticComponent,
    CommonModule,
    RegisterUserGraphComponent,
    EmployeeGraphComponent,
    WorkSummaryGraphComponent,
    StandByEquipmentGraphComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  totalUser: any;
  totalStandByEquipment: any;
  totalSection: any;
  adminId: any;
  plantId: any;
  currentUserPlant: any;
  selectedplantId: any;
  isSuperAdmin: boolean = false;

  dataArray: any = [
    {
      title: 'Total Register User',
      count: 0,
      primaryColor: '#2E6FB4',
      secondaryColor: '#3B80C8',
    },
    {
      title: 'Total Stand By Equipment',
      count: 0,
      primaryColor: '#45497B',
      secondaryColor: '#575B86',
    },
    {
      title: 'Total Provision Section',
      count: 0,
      primaryColor: '#1F5077',
      secondaryColor: '#30628A',
    },
  ];

  constructor(
    private breadcrumb: BreadcrumbService,
    private adminService: AdminService,
    private sectionService: SectionsService,
    private equipmentService: StandByEquipmentsService,
    private adminPlantSelection: AdminPlantSelectionService,
    private router: Router
  ) {}

  ngOnInit(): void {
    const role = localStorage.getItem('userRole');
    if (role == 'superadmin') {
      this.isSuperAdmin = true;
      this.dataArray = [
        {
          title: 'Total Register User',
          count: 0,
          primaryColor: '#2E6FB4',
          secondaryColor: '#3B80C8',
        },
        {
          title: 'Total Stand By Equipment',
          count: 0,
          primaryColor: '#45497B',
          secondaryColor: '#575B86',
        },
        {
          title: 'Total Provision Section',
          count: 0,
          primaryColor: '#1F5077',
          secondaryColor: '#30628A',
        },
        {
          title: 'Total Plant Admins',
          count: 0,
          primaryColor: '#1F5077',
          secondaryColor: '#2E6FB4',
        },
      ];
    }
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(
          filter((plantId) => !!plantId) // wait for a truthy plantId
        )
        .subscribe((adminPlantId) => {
          // console.log(adminPlantId, 'currSelectedID');
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.getTotalUser();
          this.getTotalStandByEquation();
          this.getTotalSection();
          this.breadcrumb.setBreadcrumbUrl(this.router.url);
        });
    } else {
      this.getAdminDetail();
      this.getTotalUser();
      this.getTotalStandByEquation();
      this.getTotalSection();
      this.breadcrumb.setBreadcrumbUrl(this.router.url);
    }
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    this.currentUserPlant = currentUser.plant[0];
  }

  getTotalUser() {
    console.log('plant id in totalUser,' + this.plantId);

    const data = {
      or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
      filter: [
        'wbiRoleId||eq||3',
        'wbiStatus||eq||1',
        'enabled||eq||true',
        `plantIds||in||{${this.plantId}}`,
      ],
    };
    const data2={
          or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
      filter: [
        'wbiRoleId||eq||2',
        'wbiStatus||eq||1',
        'enabled||eq||true',
        `plantIds||in||{${this.plantId}}`,
      ],
    }
    const param = createAxiosConfig(data);
    const param2=createAxiosConfig(data2);
    this.adminService.getUsers(param).then((response) => {
      // console.log(response.length, response, 'users in plant');
        this.dataArray[0] = {
          title: 'Total Register User',
          count: response.length,
          primaryColor: '#2E6FB4',
          secondaryColor: '#3B80C8',
        };
    });
    if (this.isSuperAdmin){
       this.adminService.getUsers(param2).then((response) => {
      console.log(response.length, response, 'users in plant');
        this.dataArray[3] = {
           title: 'Total Plant Admins',
          count: response.length,
          primaryColor: '#1F5077',
          secondaryColor: '#2E6FB4',
        };
    });
    }

  }

  getTotalStandByEquation() {
    const data = {
      filter: [`plantId||eq||${this.plantId}`, `isEquipmentStandBy||eq||1`],
    };
    const param = createAxiosConfig(data);
    this.equipmentService.getStandByEquipments(param).then((response) => {
      this.dataArray[1] = {
        title: 'Total Stand By Equipment',
        count: response.length,
        primaryColor: '#45497B',
        secondaryColor: '#575B86',
      };
    });
  }

  getTotalSection() {
    const data = {
      filter: [`plantId||eq||${this.plantId}`, 'enabled||eq||true'],
    };
    const param = createAxiosConfig(data);
    this.sectionService.getSections(param).then((response) => {
      this.dataArray[2] = {
        title: 'Total Provision Section',
        count: response.length,
        primaryColor: '#1F5077',
        secondaryColor: '#30628A',
      };
    });
  }
}
