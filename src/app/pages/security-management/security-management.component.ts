import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChildren, ViewEncapsulation } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { Router } from '@angular/router';
import { AbstractControl, FormBuilder, FormGroup, NgForm, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import * as XLSX from 'xlsx';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { SiteDetails } from '../../models/site-details';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { PlantService } from '../../services/plant/plant.service';
import { ClusterService } from '../../services/cluster/cluster.service';
import { SecurityService } from '../../services/security-module/security.service';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { HttpResponse } from '@angular/common/http';
import { CdkAccordionItem } from '@angular/cdk/accordion';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { FormService } from '../../common/Services/form-service.service';


@Component({
  selector: 'app-security-management',
  templateUrl: './security-management.component.html',
  styleUrl: './security-management.component.scss',
  animations: [
    trigger('detailExpand', [
      state('collapsed,void', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),

    trigger('tabSwitch', [
      state('active', style({ opacity: 1, transform: 'translateX(0)' })),
      state('inactive', style({ opacity: 1, transform: 'translateX(0)' })),
      transition('inactive => active', [
        animate('300ms ease-in-out')
      ]),
      transition('active => inactive', [
        animate('300ms ease-in-out')
      ]),
    ])
  ]

})




export class SecurityManagementComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChildren(CdkAccordionItem) accordionItems!: QueryList<CdkAccordionItem>;
  private videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'];
  private pdfExtension = 'pdf';
  clickLoading: boolean = false;
  uploadForm!: FormGroup;
  registerPlantForm!: FormGroup
  isRegisterFormOpen: boolean = false;
  submitted: boolean = false;
  accordionStates: boolean[] = [];
  expandedIndex = 0;
  isEdit: boolean = false;
  originalValues: any = {}
  isUpdateEnabled: any = false;
  plants: any[] = [];
  clusters: any[] = [];
  siteDetails: SiteDetails[] = []
  activatedPlants: SiteDetails[] = [];
  deactivatedPlants: SiteDetails[] = [];
  pageIndex: number = 0;
  pageSize: number = 5;
  dataSource = new MatTableDataSource(this.activatedPlants);
  searchResults: any = {};
  emailPattern = /^[a-zA-Z0-9._%+-]+@adani\.com$/;
  contactNumberPattern = /^(0[6789]\d{8}|[6789]\d{9})$/
  namePattern = /^[A-Za-z\s]+$/
  expandedElement: any;
  predefinedHeaders: string[] = [
    "Sr No.",
    "Company",
    "Site Name",
    "Cluster",
    "Address",
    "GEO Coordinates",
    "PSH Name",
    "PSH Contact Number",
    "PSH Email ID",
    "PH Name",
    "PH Contact Number",
    "PH Email ID",
    "CMO Name",
    "CMO Contact Number",
    "CMO Email ID",
    "CSO Name",
    "CSO Contact Number",
    "CSO Email ID"
  ]
  displayedColumns: string[] = [
    'srNo',
    'company',
    'siteName',
    'cluster',
    'address',
    'emergencyContactNumber',
    'moreInfo',
    'geoCoordinates',
    'action'
  ];
  nestedColumns: any[] = [
    'pshName',
    'pshContactNumber',
    'pshEmail',
    'phName',
    'phContactNumber',
    'phEmail',
    'cmoName',
    'cmoContactNumber',
    'cmoEmail',

  ]

  nestedColumns2 = [
    'csoName',
    'csoContactNumber',
    'csoEmail',
    'vshName',
    'vshContactNumber',
    'vshEmail'
  ]
  columnsToDisplayWithExpand = [...this.displayedColumns];
  searchInputs = [
    { key: 'company', type: 'dropdown', label: 'Company', options: [{ id: 1, name: 'ACC' }, { id: 2, name: 'Ambuja' }], valueField: 'id', titleField: 'name' },
    { key: 'siteName', type: 'dropdown', label: 'Site Name', options: this.plants, valueField: 'id', titleField: 'name' },
    {
      key: 'cluster', type: 'dropdown', label: 'Cluster', options: this.clusters, valueField: 'id',
      titleField: 'title'
    },
  ];
  items = [
    { title: 'Plant Security Head (PSH) Details', expanded: false, fields: [{ label: 'Name', field: 'pshName' }, { label: 'Contact Number', field: 'pshContactNumber' }, { label: 'Email', field: 'pshEmail' }] },
    { title: 'Plant Head (PH) Details', expanded: false, fields: [{ label: 'Name', field: 'phName' }, { label: 'Contact Number', field: 'phContactNumber' }, { label: 'Email', field: 'phEmail' }] },
    { title: 'Chief Manufacturing Officer (CMO)  Details', expanded: false, fields: [{ label: 'Name', field: 'cmoName' }, { label: 'Contact Number', field: 'cmoContactNumber' }, { label: 'Email', field: 'cmoEmail' }] },
    { title: 'Vertical Security Head (VSH) Details', expanded: false, fields: [{ label: 'Name', field: 'vshName' }, { label: 'Contact Number', field: 'vshContactNumber' }, { label: 'Email', field: 'vshEmail' }] },
    { title: 'Chief Security Officer (CSO) Details', expanded: false, fields: [{ label: 'Name', field: 'csoName' }, { label: 'Contact Number', field: 'cmoContactNumber' }, { label: 'Email', field: 'csoEmail' }] }]
  editDataId: any;
  hoveredLat: string | null = null;
  hoveredLong: string | null = null;
  hoverX: number = 0;
  hoverY: number = 0;
  sanitizedUrl: any;
  mapUrl: SafeUrl | null = null
  tabs = [
    { title: 'Active Plants' },
    { title: 'Inactive Plants' },
  ];
  selectedTabIndex = 0;

  selectTab(tabIndex: number) {
    this.selectedTabIndex = tabIndex
    if (this.selectedTabIndex == 0) {
      this.dataSource.data = this.activatedPlants;
    }
    else if (this.selectedTabIndex == 1) {
      this.dataSource.data = this.deactivatedPlants;
    }
  }

  constructor(private breadcrumb: BreadcrumbService,
    private router: Router,
    private fb: FormBuilder,
    private plantService: PlantService,
    private clusterService: ClusterService,
    private SecurityService: SecurityService,
    private sanitizer: DomSanitizer,
    private formUtils: FormService
  ) {
  }
  ngOnInit() {
    this.fetchData();
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
    this.accordionStates = this.items.map(() => false);
    const data = {
      page: 1,
      limit: 10000,
      sort: 'id,DESC'
    }
    this.getSecurityData(data);
    this.uploadForm = this.fb.group({
      file: [null, Validators.required]
    });
    this.registerPlantForm = this.fb.group({
      companyName: ['', Validators.required],
      plantId: ['', Validators.required],
      clusterId: ['', Validators.required],
      address: ['', Validators.required],
      lat: ['', [Validators.required, Validators.pattern(/^(-?[1-8]?[0-9](\.\d+)?|90(\.0+)?)$/)]],
      long: ['', [Validators.required, Validators.pattern(/^(-?(1[0-7][0-9]|[1-9]?[0-9])(\.\d+)?|180(\.0+)?)$/)]],
      emergencyContactNumber: ['', [Validators.required, Validators.pattern(/^(5432|0[6789]\d{8}|[6789]\d{9})$/)]],
      pshName: ['', [Validators.required, Validators.pattern(this.namePattern)]],
      pshContactNumber: ['', [Validators.required, Validators.pattern(this.contactNumberPattern)]],
      pshEmail: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phName: ['', [Validators.required, Validators.pattern(this.namePattern)]],
      phEmail: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      phContactNumber: ['', [Validators.pattern(this.contactNumberPattern)]],
      cmoName: ['', [Validators.required, Validators.pattern(this.namePattern)]],
      cmoEmail: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      cmoContactNumber: ['', [Validators.pattern(this.contactNumberPattern)]],
      csoName: ['', [Validators.required, Validators.pattern(this.namePattern)]],
      csoEmail: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      csoContactNumber: ['', [Validators.pattern(this.contactNumberPattern)]],
      vshName: ['', [Validators.required, Validators.pattern(this.namePattern)]],
      vshEmail: ['', [Validators.required, Validators.pattern(this.emailPattern)]],
      vshContactNumber: ['', [Validators.pattern(this.contactNumberPattern)]]
    }, {}
    )
    this.registerPlantForm.valueChanges.subscribe(() => {
      if (this.isEdit) {
        this.isUpdateEnabled = this.checkIfFormChanged();
      }
    });

  }

  toggleRow(element: any, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }
    this.expandedElement = this.expandedElement === element ? null : element;
  }

  getSecurityData(data?: any) {
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: []
      }
    }
    if (this.searchResults) {
      if (this.searchResults.siteName) {
        data.filter.push(`plantId||eq||${this.searchResults?.siteName}`);
      }
      if (this.searchResults.cluster) {
        data.filter.push(`clusterId||eq||${this.searchResults?.cluster}`);
      }
      if (this.searchResults.company) {
        data.filter.push(`companyName||eq||${this.searchResults?.company}`);
      }
    }
    const param = createAxiosConfig(data);
    this.clickLoading = true;
    this.SecurityService.getSecurityData(param)
      .then((res: any) => {
        if (res && res.data) {
          this.siteDetails = res.data;
          this.activatedPlants = this.siteDetails.filter((ele: any) => ele.enabled === true);
          this.deactivatedPlants = this.siteDetails.filter((ele: any) => ele.enabled === false);
          this.dataSource.data = this.selectedTabIndex == 0 ? this.activatedPlants : this.deactivatedPlants;
          this.clickLoading = false;
        } else {
          this.toast.showErrorToast('Unexpected response structure.', 6000);
          this.clickLoading = false;
        }

      })
      .catch((error: any) => {
        this.clickLoading = false;
        if (error.status === 500) {
          this.toast.showErrorToast('Server error occurred. Please try again later.', 6000);
        } else if (error.status === 404) {
          this.toast.showErrorToast('Data not found. Please check your parameters.', 6000);
        } else if (error.status === 0) {
          this.toast.showErrorToast('Failed to connect to the server. Please check your network connection.', 6000);
        } else {
          const errorMessage = error?.message || 'An unknown error occurred while loading data.';
          this.toast.showErrorToast(errorMessage, 6000);
        }
      });

  }

  validateLink(url: string): string {
    try {
      const fileExtension = url.split('.').pop()?.split('?')[0].toLowerCase();

      if (!fileExtension) {
        return 'Invalid URL';
      }

      if (fileExtension === this.pdfExtension) {
        return 'PDF';
      }

      if (this.videoExtensions.includes(fileExtension)) {
        return 'Video';
      }

      return 'Unknown Format';
    } catch (error) {
      return 'Invalid URL';
    }
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex ? event.pageIndex : 0;
    this.pageSize = event.pageSize;
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;

  }
  openModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'block';
      modal?.classList.add('show');
      modal?.setAttribute('aria-hidden', 'false');
      modal?.setAttribute('aria-modal', 'true');
      modal?.setAttribute('role', 'dialog');
    }
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    document.body.appendChild(backdrop);

    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = '0px';
  }
  closeModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'none';
      modal?.classList.remove('show');
      modal?.setAttribute('aria-hidden', 'true');
      modal?.removeAttribute('aria-modal');
      modal?.removeAttribute('role');
      this.uploadForm.reset()
    }

    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.parentNode?.removeChild(backdrop);
    }

    const backdrop0 = document.querySelector('.modal-backdrop');
    if (backdrop0) {
      backdrop0.parentNode?.removeChild(backdrop0);
    }

    document.body.className = '';
    document.body.removeAttribute('style');
    document.body.removeAttribute('data-bs-overflow');
    document.body.removeAttribute('data-bs-padding-right');
  }

  downloadAllChecklist() {

  }

  validateFile(event: any) {
    const fileInput = event.target as HTMLInputElement;
    const file = fileInput.files?.[0];
    if (file) {
      const validFileTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
      const isValidType = validFileTypes.includes(file.type);
      if (!isValidType) {
        this.uploadForm.get('file')?.setErrors({ invalidFileType: true });
      } else {
        // this.uploadForm.get('file')?.setValue(file);
        // this.uploadForm.get('file')?.setErrors(null);
        const reader = new FileReader();

        reader.onload = (e: any) => {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });

          // Assuming headers are in the first sheet and first row
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          let headers;
          let firstNonEmptyRow = this.getFirstNonEmptyRow(worksheet);

          if (firstNonEmptyRow) {
            // Now you can process the data from this row onward
            headers = this.getHeadersFromWorksheet(worksheet, firstNonEmptyRow); // Pass the first non-empty row to your header function
          } else {
            console.log('All rows are empty');
          }
          if (!this.validateHeaders(headers)) {
            this.uploadForm.get('file')?.setErrors({ invalidHeaders: true });
          } else {
            this.uploadForm.get('file')?.setErrors(null);
            this.uploadForm.get('file')?.setValue(file);
          }
        };

        reader.readAsArrayBuffer(file);
      }

    }
    else {
      this.uploadForm.get('file')?.reset();
    }
  }


  getHeadersFromWorksheet(worksheet: any, startRow = 1): string[] {
    const headers = [];
    let row = startRow;
    let col = 1; // Start with the first column (A)

    while (worksheet[`${String.fromCharCode(64 + col)}${row}`]) {
      const cell = worksheet[`${String.fromCharCode(64 + col)}${row}`];
      if (cell && cell.v != null) {
        headers.push(cell.v); // Add the header to the list
      }
      col++;
    }

    return headers;
  }

  validateHeaders(headers: any): boolean {
    const normalizeString = (str: string) => str.trim().replace(/\u200B/g, '');
    const normalizedHeaders = headers.map((header: any) => normalizeString(header));
    const normalizedPredefinedHeaders = this.predefinedHeaders.map(header => normalizeString(header));

    const headersMatch = JSON.stringify(normalizedHeaders) == JSON.stringify(normalizedPredefinedHeaders)
    return headersMatch
  }

  downloadSample(): void {
    const link = document.createElement('a');
    link.href = 'assets/files/sample.xlsx';  // Correct file path
    link.setAttribute('download', 'sample.xlsx');  // Specify the file name for download
    document.body.appendChild(link);  // Append the link to body for compatibility
    link.click();  // Trigger the click event to start download
    document.body.removeChild(link);
  }

  getFirstNonEmptyRow = (worksheet: any) => {
    let row = 1; // Start checking from the first row (Excel rows are 1-indexed)
    while (true) {
      const cell = worksheet[`A${row}`]; // Check the first cell of each row (you can change column if necessary)
      if (cell && cell.v != null && cell.v !== '') {
        return row; // Return the row number if it's not empty
      }
      row++; // Move to the next row
      if (!worksheet[`A${row}`]) {
        break; // Stop if no more rows are present
      }
    }
    return null; // If no non-empty rows are found, return null
  };


  handleSearch(event: any): void {
    this.clickLoading = true
    this.searchResults = event;
    const data = {
      page: 1,
      limit: 10000,
      sort: 'id,DESC',
      filter: [
        'enabled||eq||true'
      ]
      // filter: ['']
    }
    data.filter.pop();
    if (this.searchResults.siteName) {
      data.filter.push(`plantId||eq||${this.searchResults?.siteName}`);
    }
    if (this.searchResults.cluster) {
      data.filter.push(`clusterId||eq||${this.searchResults?.cluster}`);
    }
    if (this.searchResults.company) {
      data.filter.push(`companyName||eq||${this.searchResults?.company}`);
    }
    this.getSecurityData(data)
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  getAllPlant(): Promise<void> {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]
    }
    const param = createAxiosConfig(data);
    return this.plantService.getAllPlants(param).then((response) => {
      this.plants = [...response.data, this.plants]
    })
  }

  getClusters(): Promise<void> {
    return this.clusterService.getClusters().then((response) => {
      this.clusters = response
    })
  }
  async fetchData(): Promise<void> {
    try {

      await Promise.all([this.getAllPlant(), this.getClusters()]);
      this.searchInputs = [
        { key: 'company', type: 'dropdown', label: 'Company', options: [{ id: 1, name: 'ACC' }, { id: 2, name: 'Ambuja' }], valueField: 'name', titleField: 'name' },
        { key: 'siteName', type: 'dropdown', label: 'Site Name', options: this.plants, valueField: 'id', titleField: 'name' },
        {
          key: 'cluster', type: 'dropdown', label: 'Cluster', options: this.clusters, valueField: 'id',
          titleField: 'title'
        },
      ];
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }
  populateSearchInputs(): void {
    this.searchInputs = [
      { key: 'company', type: 'dropdown', label: 'Company', options: [{ id: 1, name: 'ACC' }, { id: 2, name: 'Ambuja' }], valueField: 'id', titleField: 'name' },
      { key: 'siteName', type: 'dropdown', label: 'Site Name', options: this.plants, valueField: 'id', titleField: 'name' },
      {
        key: 'cluster', type: 'dropdown', label: 'Cluster', options: this.clusters, valueField: 'id',
        titleField: 'title'
      },
    ];
  }


  closeFormModal() {
    this.isEdit = false;
    this.registerPlantForm.get('plantId')?.enable()
    this.resetRegisterPlantForm()
  }
  onClickCross() {
    this.isRegisterFormOpen = false
    this.isEdit = false;
    this.registerPlantForm.get('plantId')?.enable()
    this.resetRegisterPlantForm()
  }

  openRegisterForm() {
    this.isRegisterFormOpen = true;
  }

  onKeyPress(event: KeyboardEvent): void {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }

  resetRegisterPlantForm() {
    this.registerPlantForm.reset()
    this.submitted = false;
    this.registerPlantForm.get('plantId')?.setValue('')
    this.registerPlantForm.get('companyName')?.setValue('')
    this.registerPlantForm.get('clusterId')?.setValue('')
  }

  onAccordionToggle(index: number): void {
    this.accordionStates[index] = true; this.accordionStates[index] = true;
  }

  isItemValid(item: any): boolean {
    return item.fields.every((field: any) => {
      const control = this.registerPlantForm.get(field.field);
      return control && control.valid;
    });
  }

  onSubmit() {
    this.submitted = true;
    this.formUtils.trimFormValues(this.registerPlantForm);
    const firstAccordionWithErrorsIndex = this.items.findIndex((accordionItem) =>
      accordionItem.fields.some((field) => {
        const control = this.registerPlantForm.get(field.field);
        return control && control.errors;
      })
    );

    if (firstAccordionWithErrorsIndex !== -1) {
      this.accordionItems.forEach((item) => item.expanded = false);
      const accordionToExpand = this.accordionItems.toArray()[firstAccordionWithErrorsIndex];
      if (accordionToExpand) {
        accordionToExpand.open();
      }
    }

    if (this.registerPlantForm.valid) {
      console.log("Form Submitted", this.registerPlantForm.value);
      if (this.isEdit) {
        this.clickLoading = true
        let payload;
        const updatedFields = this.getUpdatedFields();
        if (this.isEdit && Object.keys(updatedFields).length > 0) {
          payload = {
            tableName: 'sos',
            id: this.editDataId,
            data: updatedFields,
          };
        }
        this.SecurityService.EditPlant(payload).then((response: any) => {
          this.clickLoading = false;

          // if (response.status === 200 || response.status === 201) {
          //   this.toast.showSuccessToast("Plant added successfully");
          //   this.resetRegisterPlantForm();
          // } else {
          //   this.toast.showErrorToast("An error occurred.");
          // }
          if (response.statusCode == 401) {
            this.toast.showErrorToast(response.message ? response.message : "An error occurred.");
            this.clickLoading = false;
            return
          }
          if (response.responseCode == 300) {
            this.toast.showErrorToast(response.message ? response.message : "An error occurred.");
            this.clickLoading = false;
            return
          }
          if (response.responseCode == 200) {
            this.toast.showSuccessToast("Site Details Updated Successfully");
            this.resetRegisterPlantForm();
            this.getSecurityData()
            this.isRegisterFormOpen = false;
            this.isEdit = false
            this.registerPlantForm.get('plantId')?.enable()
            this.accordionStates = this.accordionStates.map(() => false);
          } else {
            this.toast.showErrorToast("An error occurred.");
            this.clickLoading = false;
          }
        },)
          .catch(error => {
            this.clickLoading = false;
            if (error.statusCode === 401) {
              this.toast.showErrorToast("Unauthorized! Please log in again.");
              this.router.navigate(['/login']);
            } else {
              this.toast.showErrorToast("Something went wrong. Please try again.");
            }
          });
      }
      else {
        this.clickLoading = true;
        this.SecurityService.registerNewPlant(this.registerPlantForm.value)
          .then((response: any) => {
            this.clickLoading = false;

            if (response.statusCode === 401) {
              this.toast.showErrorToast(response.message || "Unauthorized! Please log in again.");
              return;
            }
            if (response.responseCode == 300) {
              this.toast.showErrorToast(response.message ? response.message : "An error occurred.");
              this.clickLoading = false;
              return
            }
            if (response.responseCode == 200) {
              this.toast.showSuccessToast("New Site Registered Successfully.");
              this.resetRegisterPlantForm();
              this.isEdit = false;
              this.registerPlantForm.get('plantId')?.enable()
              this.isRegisterFormOpen = false;
              this.accordionStates = this.accordionStates.map(() => false);
              this.getSecurityData();
            } else {
              this.toast.showErrorToast("An error occurred.");
              this.clickLoading = false;
              this.registerPlantForm.get('plantId')?.enable()
              this.isEdit = false;
            }
          })
          .catch(error => {
            console.log('error', error)
            const status = error.response ? error.response.status : null;
            this.clickLoading = false;
            if (status === 401) {
              this.toast.showErrorToast("Unauthorized! Please log in again.");
              // Redirect to login if necessary
              this.router.navigate(['/login']);
            } else {
              this.toast.showErrorToast("Something went wrong. Please try again.");
            }
          });

      }
    }
  }
  isFieldRequired(fieldName: string): boolean {
    const control = this.registerPlantForm.get(fieldName);
    if (control?.validator) {
      const validator = control.validator({} as AbstractControl);
      return !!validator && validator['required'];
    }
    return false;
  }

  getUpdatedFields(): any {
    const currentValues = this.registerPlantForm.value;
    const updatedFields: any = {};

    Object.keys(currentValues).forEach((key) => {
      if (currentValues[key] !== this.originalValues[key]) {
        updatedFields[key] = currentValues[key];
      }
    });

    return updatedFields;
  }
  onEdit(item: any) {
    this.isEdit = true;
    this.registerPlantForm.get('plantId')?.disable()
    this.isRegisterFormOpen = true;
    this.editDataId = item.id
    this.registerPlantForm.patchValue({
      companyName: item.companyName,
      plantId: item.plantId,
      clusterId: item.clusterId,
      address: item.address,
      lat: item.lat,
      long: item.long,
      pshName: item.pshName,
      pshContactNumber: item.pshContactNumber,
      emergencyContactNumber: item.emergencyContactNumber,
      pshEmail: item.pshEmail,
      phName: item.phName,
      phEmail: item.phEmail,
      phContactNumber: item.ContactNumber,
      cmoName: item.cmoName,
      cmoEmail: item.cmoEmail,
      cmoContactNumber: item.cmoContactNumber,
      csoName: item.csoName,
      csoEmail: item.csoEmail,
      csoContactNumber: item.csoContactNumber,
      vshName: item.vshName,
      vshEmail: item.vshEmail,
      vshContactNumber: item.vshContactNumber,
    })
    this.originalValues = this.registerPlantForm.value
    this.isUpdateEnabled = this.checkIfFormChanged();
  }
  checkIfFormChanged(): boolean {
    const currentValues = this.registerPlantForm.value;
    return Object.keys(currentValues).some(
      (key) =>
        typeof currentValues[key] === 'string' &&
        currentValues[key].trim() !== this.originalValues[key]?.trim()
    );
  }

  resetSearch(event: any) {
    this.searchResults = {}
    if (event) {
      const data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        // filter: [
        //   'enabled||eq||true'
        // ]
      }
      this.getSecurityData(data);
    }
  }
  activeDeactivatePlant(enabled: boolean, id: any) {
    const payload = {
      tableName: 'sos',
      id: id,
      data: {
        enabled: !enabled
      },
    };
    this.clickLoading = true
    this.SecurityService.EditPlant(payload).then((response: any) => {
      this.clickLoading = false;
      if (response.responseCode === 200 || response.status === 201) {
        this.toast.showSuccessToast(`Plant ${enabled ? 'deactivated' : 'activated'} successfully`);
        this.getSecurityData()
      } else {
        this.toast.showErrorToast("An error occurred.");
      }
    },)

  }
  showOnMapHover(lat: string, long: string, event: MouseEvent): void {
    this.hoveredLat = lat;
    this.hoveredLong = long;
    this.hoverX = event.clientX + 10; // Adjust offset for better positioning
    this.hoverY = event.clientY + 10;
  }

  hideMapHover(): void {
    this.hoveredLat = null;
    this.hoveredLong = null;
  }
  setMapUrl(lat: string, long: string) {
    // const mapUrl = `https://www.google.com/maps?q=${lat},${long}&output=embed`;
    const mapUrl = `https://www.google.com/maps?q=${lat},${long}&hl=en&z=14&output=embed`
    // return this.sanitizer.bypassSecurityTrustResourceUrl(mapUrl);
    window.open(mapUrl, '_blank');
  }

  getDetails(element: any) {
    let array = []
    array.push(element);
    return array
  }

  getDataSource() {
    this.dataSource.data = this.siteDetails.filter((ele: any) => ele?.enabled)
    return this.dataSource
  }
  duplicateValidator(formGroup: FormGroup) {
    const emails = ['pshEmail', 'phEmail', 'cmoEmail', 'csoEmail'];
    const emailLabels: any = {
      pshEmail: 'PSH Email',
      phEmail: 'PH Email',
      cmoEmail: 'CMO Email',
      csoEmail: 'CSO Email',
    };

    const contacts = ['pshContactNumber', 'phContactNumber', 'cmoContactNumber', 'csoContactNumber'];
    const contactLabels: any = {
      pshContactNumber: 'PSH Contact Number',
      phContactNumber: 'PH Contact Number',
      cmoContactNumber: 'CMO Contact Number',
      csoContactNumber: 'CSO Contact Number',
    };

    const allFields = [...emails, ...contacts];
    const fieldLabels = { ...emailLabels, ...contactLabels };

    const fieldValues = allFields.map(field => ({
      field,
      value: formGroup.get(field)?.value,
    }));

    // Detect duplicates
    const duplicates = fieldValues.filter((item, index, self) =>
      item.value && self.findIndex(i => i.value === item.value && i.field !== item.field) !== -1
    );

    // Apply errors to duplicate fields
    allFields.forEach(field => {
      const control = formGroup.get(field);
      if (control) {
        const duplicate = duplicates.find(item => item.field === field);
        if (duplicate) {
          const conflictingField = duplicates.find(
            item => item.value === control.value && item.field !== field
          );
          if (conflictingField) {
            control.setErrors({
              duplicate: `This value is already used in ${fieldLabels[conflictingField.field]}.`,
            });
          }
        } else {
          // Clear duplicate errors if no conflict
          const errors = { ...control.errors };
          delete errors['duplicate'];
          control.setErrors(Object.keys(errors).length ? errors : null);
        }
      }
    });

    return null; // No form-level error
  }

}
