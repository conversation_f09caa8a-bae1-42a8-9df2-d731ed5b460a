.button-right {
  display: flex;
  align-items: center;

  >span {
    margin: 0 auto;
  }

  >.right-arrow {
    width: 61px;
    height: 40px;
    position: relative;
  }
}

.confirmModalHeader {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  color: #ffffff;
}

.confirmModalBody {
  >p {
    color: #555555;
    font-weight: 500;
  }

  >.d-flex {
    justify-content: center;
  }
}

.confirmModalCloseBtn {
  color: #fff !important;
}

.error {
  color: red;
}

.outer-container {
  height: auto;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  padding: 10px;
  background-color: #f9f9f9;
}

.table {
  height: auto;
  overflow: auto;
}

.accordion-card {
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.example-accordion-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  font-size: 20px;
  // background: linear-gradient(to right, #0b74b0, #75479c, #bd3861);
  // background-color: #f9f9f9;
  // background-clip: text;
  // -webkit-background-clip: text;
  // color: black;
  cursor: pointer;
  font-weight: 900;
}

.destination-img {
  height: 25px !important;
  width: 25px !important;
}


.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-default {
  background-color: gray;
  color: white;
}

.icon-valid {
  background-color: green;
  color: white;
}

.icon-invalid {
  background-color: red;
  color: white;
}

.status-icon i {
  font-size: 12px;
  // color: black;
  // border-radius: 50%;
}


.example-accordion-item-body {
  padding: 10px;
  background-color: #f9f9f9;
}


.example-accordion-item-header>span {
  flex-grow: 1;
  text-align: center;
}

.icon-toggle {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: black;
}


.icon-toggle {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(to right, #0b74b0, #75479c, #bd3861);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.2), 0 -4px 8px rgba(255, 255, 255, 0.5);
  transition: transform 0.2s, box-shadow 0.2s;
}

.icon-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25), 0 -6px 12px rgba(255, 255, 255, 0.6);
}

.icon-toggle i {
  font-size: 12px;
  color: white;
  transition: color 0.2s, background-color 0.2s;
  // padding: 10px;
}

.icon-toggle i.active {
  color: white;
  /* Active icon color */
  // background-color: #0b74b0; 
  border-radius: 50%;
  padding: 10px;
}

.icon-toggle i {
  font-size: 14px;
  // color: #0b74b0; 
  transition: color 0.2s, background-color 0.2s;
}

.icon-toggle.green {
  background: linear-gradient(145deg, #3e8e41, #66bb6a);
  // box-shadow: 5px 5px 10px #2e7030, -5px -5px 10px #7fd982; 
  color: white;
}

.icon-toggle.red {
  background: linear-gradient(145deg, #c44f4f, #f76b6b);
  // box-shadow: 5px 5px 10px #a63c3c, -5px -5px 10px #ff8a8a; 
  color: white;
}

::ng-deep .example-accordion-item-body,
.example-accordion-item-header {
  font-family: "adani", sans-serif !important;
  font-size: 15px !important;
  font-weight: 600 !important;
}

.table {
  position: relative;
  overflow: hidden;
  bottom: 0;
  margin-bottom: 0px;
}

.table table {
  overflow-x: auto;
  display: block;
  width: 100%;
}

.table th {
  position: sticky;
  top: 0;
  z-index: 1;
  // background-color: white; /* Or any background color matching the table design */
}

.table mat-paginator {
  position: sticky;
  bottom: 0;
  top: 0;
  background-color: white;
  /* To prevent overlap issues */
  z-index: 2;
  /* Ensure it appears above the table content */
}

.address-column {
  width: 200px !important;
  max-width: 200px;
  white-space: nowrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-align: left;
}

.mat-header-cell,
.mat-cell {
  text-align: left;
}

::ng-deep .address-column {
  width: 100px !important;
}

::ng-deep .more-info {
  width: 80px !important;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-data-found {
  text-align: center;
  margin-top: 20px;
  font-size: 16px;
  color: #888;
}

::ng-deep .matTooltip {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%) !important;
  color: #fff !important;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.3);
}

.map-hover-container {
  position: relative;
  display: inline-block;
}

.map-tooltip {
  position: absolute;
  top: -10px;
  left: 35px;
  z-index: 100;
  display: block;
  width: 300px;
  height: 200px;
  border: 1px solid #ccc;
  background: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  visibility: visible;
}

.map-tooltip iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.bi-geo-alt-fill {
  // font-size: 1.2rem; /* Adjust icon size */
  transition: color 0.3s ease;
}

.bi-geo-alt-fill:hover {
  color: #0056b3;
}

span {
  color: #333;
}

table {
  width: 100%;
}

::ng-deep tr.example-detail-row {
  height: 0;
}

::ng-deep .example-element-row td {
  border-bottom: none;
}

tr.example-element-row:not(.example-expanded-row):hover {
  background: whitesmoke;
}

tr.example-element-row:not(.example-expanded-row):active {
  background: #efefef;
}

::ng-deep .example-element-detail {
  overflow: hidden;
  display: flex;
}


.arrow {
  margin-left: -10px;

  img {
    width: 45px !important;
    height: 35px !important;
    padding-bottom: 10px !important;
  }
}


.example-element-row.example-expanded-row {
  border-bottom: none !important;
}

.example-detail-row {
  // background-color: #f9f9f9;
  padding: 16px;
}

.example-element-description {
  padding: 10px 0px 10px 0px;
  // border: 1px solid #ddd;
  // border-radius: 8px;
}

.tab-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.tab-headers {
  display: flex;
  justify-content: center;
  border: 1px solid #0B74B0;
  border-radius: 20px;
  background-color: white;
  color: #0B74B0;
}

.tab-header {
  padding: 10px 20px;
  cursor: pointer;
  transition: transform 0.3s,
}

.tab-header.active {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white;
  border-radius: 20px;
  font-weight: bold;
}

.ml-12 {
  margin-left: 12px;
}

.mt-1 {
  margin-top: 5px;
}

.example-accordion-item-header.valid-header {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white !important;
  // border-radius: 20px;
  font-weight: bold;
  >span{
    color: white;
  }
}

.invalid-header {
  // background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  // color: white !important;
  // border-radius: 20px;
  font-weight: bold;
}
.example-element-detail {
  width: 100%;
  display: flex;
  flex-direction: column;
}
