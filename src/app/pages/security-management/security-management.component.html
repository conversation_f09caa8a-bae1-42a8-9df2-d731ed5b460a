<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>

<div class="header-container">
  <p class="fw-bold" style="margin-top: -8px">Security Management</p>
  <div class="tab-container">
    <div class="tab-headers">
      <div *ngFor="let tab of tabs; let i = index" [@tabSwitch]="i === selectedTabIndex ? 'active' : 'inactive'"
        [class.active]="i === selectedTabIndex" (click)="selectTab(i)" class="tab-header">
        {{ tab.title }}
      </div>
    </div>
  </div>
</div>
<div>
  <div class="d-flex align-items-end justify-content-end">

    <div style="display: flex;gap:5px">
      <!-- <button class="button-submit button-right" (click)="downloadSample()" style="width: 200px;">
        <img alt="" src="../../../assets/svg/download-btn-icon.svg" class="right-arrow" />
        <span class="">Download Sample Excel</span>
      </button>
      <button class="button-submit button-left" (click)="openModal2('migrateModal')">
          <span class="">Upload Excel</span>
          <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
          
        </button> -->
      <!-- <button  class="button-submit button-left" (click)="openRegisterForm()">
        <span style="color: white;">Register Plant</span>
      </button> -->

      <button *ngIf="selectedTabIndex == 0" class="button-submit button-right" (click)="openRegisterForm()"
        style="width: 150x;">
        <img alt="" style="margin-left: -11px" src="../../../assets/svg/add.svg" class="right-arrow" />
        <span style="color: white;margin: 0px;">Register Site</span>
      </button>

    </div>
  </div>
  <div class="outer-container mt-3">
    <div class="parent-container">
      <app-search-filter [filters]="searchInputs" (search)="handleSearch($event)"
        (reset)="resetSearch($event)"></app-search-filter>
      <div>
        <table style="width: 100% !important;" mat-table multiTemplateDataRows [dataSource]="dataSource"
          class="mat-elevation-z8 ">
          <ng-container matColumnDef="srNo">
            <th style="width:70px" mat-header-cell *matHeaderCellDef> Sr No. </th>
            <td mat-cell *matCellDef="let element; let i = index">
              {{ dataSource.data.indexOf(element) + 1 }}
            </td>
          </ng-container>

          <ng-container matColumnDef="company">
            <th mat-header-cell *matHeaderCellDef> Company </th>
            <td mat-cell *matCellDef="let element"> {{ element.companyName ? element.companyName : 'NA' }} </td>
          </ng-container>

          <ng-container matColumnDef="siteName">
            <th mat-header-cell *matHeaderCellDef> Site Name </th>
            <td mat-cell *matCellDef="let element"> {{ element?.plant?.name ?element.plant.name :'NA'}} </td>
          </ng-container>

          <ng-container matColumnDef="cluster">
            <th mat-header-cell *matHeaderCellDef> Cluster </th>
            <td mat-cell *matCellDef="let element"> {{ element.cluster.title ? element.cluster.title:'NA' }} </td>
          </ng-container>

          <ng-container matColumnDef="address">
            <th mat-header-cell *matHeaderCellDef> Address </th>
            <td mat-cell *matCellDef="let element" matTooltip="{{element.address}}">
              {{ element.address ? element.address : 'NA' }}
            </td>
          </ng-container>
          <ng-container matColumnDef="emergencyContactNumber">
            <th mat-header-cell *matHeaderCellDef> Emergency Contact Number </th>
            <td mat-cell *matCellDef="let element" matTooltip="{{element.emergencyContactNumber}}">
              {{ element.emergencyContactNumber ? element.emergencyContactNumber : 'NA' }}
            </td>
          </ng-container>


          <ng-container matColumnDef="geoCoordinates">
            <th mat-header-cell *matHeaderCellDef> GEO Coordinates </th>
            <td mat-cell *matCellDef="let element">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span matTooltip="{{element.lat}}">{{ element.lat | number: '1.4-4'  }}</span> <span matTooltip="{{element.long}}">{{ element.long | number: '1.4-4'  }}</span>
                <img style="height: 25px;width: 25px;cursor: pointer;" class="destination-img" matTooltip="View Map"
                [matTooltipPosition]="'above'" (click)="setMapUrl(element.lat, element.long)" alt=""
                src="../../../assets/img/destination.png" class="right-arrow" />
              </div>
            </td>

            <div *ngIf="mapUrl" class="map-preview">
              <iframe width="400" height="300" [src]="mapUrl" frameborder="0" allowfullscreen></iframe>
            </div>
          </ng-container>

          <ng-container matColumnDef="moreInfo">
            <th class="more-info" mat-header-cell *matHeaderCellDef aria-label="row actions">&nbsp;</th>
            <td class="more-info" mat-cell *matCellDef="let element">
              <button matTooltip="View More Info" mat-icon-button aria-label="expand row"
                (click)="(expandedElement = expandedElement === element ? null : element); $event.stopPropagation()">
                <div class="arrow">
                  <img [src]="
                   expandedElement === element
                      ? '../../../assets/svg/dropdown-up-arrow.svg'
                      : '../../../assets/svg/dropdown-down-arrow.svg'
                  " alt="Arrow" />
                </div>
              </button>
            </td>
          </ng-container>
          <ng-container matColumnDef="expandedDetail">
            <td mat-cell *matCellDef="let element" [attr.colspan]="columnsToDisplayWithExpand.length">
              <div class="example-element-detail"
                [@detailExpand]="element == expandedElement ? 'expanded' : 'collapsed'">
                <div class="example-element-description">

                  <table class="example-element-detail" mat-table [dataSource]="getDetails(element)" class="mat-elevation-z1">

                    <ng-container matColumnDef="pshName">
                      <th mat-header-cell *matHeaderCellDef>PSH Name </th>
                      <td mat-cell *matCellDef="let element"> {{ element.pshName ? element.pshName :' NA'}} </td>
                    </ng-container>

                    <ng-container matColumnDef="pshContactNumber">
                      <th mat-header-cell *matHeaderCellDef>PSH Contact Number </th>
                      <td mat-cell *matCellDef="let element"> {{ element.pshContactNumber ?
                        element.pshContactNumber:'NA' }} </td>
                    </ng-container>

                    <ng-container matColumnDef="pshEmail">
                      <th mat-header-cell *matHeaderCellDef>PSH Email ID </th>
                      <td mat-cell *matCellDef="let element" style="border-right: 2px dotted black;"> {{
                        element.pshEmail ? element.pshEmail :'NA' }} </td>
                    </ng-container>

                    <ng-container matColumnDef="phName">
                      <th mat-header-cell *matHeaderCellDef>PH Name </th>
                      <td mat-cell *matCellDef="let element"> {{ element.phName ?element.phName : 'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="phContactNumber">
                      <th mat-header-cell *matHeaderCellDef>PH Contact Number </th>
                      <td mat-cell *matCellDef="let element"> {{ element.phContactNumber ? element.phContactNumber :'NA'
                        }} </td>
                    </ng-container>
                    <ng-container matColumnDef="phEmail">
                      <th mat-header-cell *matHeaderCellDef>PH Email ID </th>
                      <td mat-cell *matCellDef="let element" style="border-right: 2px dotted black;"> {{ element.phEmail
                        ? element.phEmail:'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="cmoName">
                      <th mat-header-cell *matHeaderCellDef>CMO Name </th>
                      <td mat-cell *matCellDef="let element"> {{ element.cmoName ? element.cmoName :'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="cmoContactNumber">
                      <th mat-header-cell *matHeaderCellDef>CMO Contact Number </th>
                      <td mat-cell *matCellDef="let element"> {{ element.cmoContactNumber ?
                        element.cmoContactNumber:'NA' }} </td>
                    </ng-container>

                    <ng-container matColumnDef="cmoEmail">
                      <th mat-header-cell *matHeaderCellDef>CMO Email ID </th>
                      <td mat-cell *matCellDef="let element"> {{
                        element.cmoEmail ?element.cmoEmail:'NA' }} </td>
                    </ng-container>
                   <tr style="width:100% !important" mat-header-row *matHeaderRowDef="nestedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: nestedColumns;"></tr>

                  </table>
                  <table class="example-element-detail" mat-table [dataSource]="getDetails(element)" class="mat-elevation-z1">
                  
                    <ng-container matColumnDef="csoName">
                      <th mat-header-cell *matHeaderCellDef>CSO Name </th>
                      <td mat-cell *matCellDef="let element"> {{ element.csoName ? element.csoName :'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="csoContactNumber">
                      <th mat-header-cell *matHeaderCellDef>CSO Contact Number </th>
                      <td mat-cell *matCellDef="let element"> {{ element.csoContactNumber ? element.csoContactNumber
                        :'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="csoEmail">
                      <th mat-header-cell *matHeaderCellDef>CSO Email ID </th>
                      <td mat-cell *matCellDef="let element" style="border-right: 2px dotted black;"> {{ element.csoEmail
                        ?element.csoEmail :'NA'}} </td>
                    </ng-container>
                    <ng-container matColumnDef="vshName">
                      <th mat-header-cell *matHeaderCellDef>VSH Name </th>
                      <td mat-cell *matCellDef="let element"> {{ element.vshName ? element.vshName :'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="vshContactNumber">
                      <th mat-header-cell *matHeaderCellDef>VSH Contact Number </th>
                      <td mat-cell *matCellDef="let element"> {{ element.vshContactNumber ? element.vshContactNumber
                        :'NA' }} </td>
                    </ng-container>
                    <ng-container matColumnDef="vshEmail">
                      <th mat-header-cell *matHeaderCellDef>VSH Email ID </th>
                      <td mat-cell *matCellDef="let element"> {{ element.vshEmail ?element.vshEmail :'NA'}} </td>
                    </ng-container>
                    <tr style="width:100% !important" mat-header-row *matHeaderRowDef="nestedColumns2"></tr>
                    <tr mat-row *matRowDef="let row; columns: nestedColumns2;"></tr>

                  </table>
                </div>
              </div>
            </td>
          </ng-container>
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef>Action</th>
            <td mat-cell *matCellDef="let element">
              <div style="gap:10px" class="d-flex">
                <i *ngIf="selectedTabIndex == 0" class="bi bi-pencil-fill"
                  style="cursor: pointer; color: #634ad3;font-size: 16px;" (click)="onEdit(element)"
                  matTooltip="Edit Plant"></i>
                <i *ngIf="element.enabled" class="bi bi-x-circle-fill"
                  style="cursor: pointer; color: #e23939;font-size: 18px;"
                  (click)="activeDeactivatePlant(element.enabled,element.id)" matTooltip="Deactivate Plant"></i>
                <i *ngIf="!element.enabled" style="cursor: pointer; color: #198f5e;font-size: 18px;"
                  (click)="activeDeactivatePlant(element.enabled,element.id)" matTooltip="Activate Plant"
                  class="bi bi-check-circle-fill">
                </i>
              </div>
            </td>
          </ng-container>

          <tr style="width:100% !important" mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let element; columns: displayedColumns;" class="example-element-row"
            [class.example-expanded-row]="expandedElement === element">
          </tr>
          <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="example-detail-row"
            [ngStyle]="row === expandedElement ? {'height': 0,'border-bottom':'none'} : {'display': 'none'}"></tr>
        </table>
        <div *ngIf="!dataSource || dataSource?.data?.length === 0" class="no-data-found">
          <p>No data found</p>
        </div>
      </div>
      <mat-paginator [pageSizeOptions]="[5, 10, 20]" [pageSize]="5" [length]="dataSource.data.length"
        showFirstLastButtons aria-label="Select page of periodic elements">
      </mat-paginator>
      <div style="height: 50px"></div>
    </div>
  </div>
</div>



<div class="modal fade" tabindex="-1" id="migrateModal" aria-labelledby="migrateModalLabel" #migrateModal
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header confirmModalHeader justify-content-between">
        <h5 class="modal-title">Excel File Upload</h5>
        <button type="button" class="btn" data-bs-dismiss="modal" aria-label="Close"
          (click)="this.closeModal2('migrateModal')" style="font-size: 16px; font-weight: 600; color: #fff">
          X
        </button>
      </div>
      <div class="modal-body confirmModalBody">
        <form [formGroup]="uploadForm">
          <label class="ml-12 fw-bold">Select Excel File to Upload <span class="error">*</span></label>
          <input type="file" class="form-control my-2" formControlName="file" accept=".xls,.xlsx"
            (change)="validateFile($event)" />
          <div *ngIf="uploadForm.get('file')?.hasError('invalidFileType') && uploadForm.get('file')?.touched"
            class="text-danger">
            Please upload a valid Excel file (.xls or .xlsx).
          </div>
          <div *ngIf="uploadForm.get('file')?.hasError('invalidHeaders') && uploadForm.get('file')?.touched"
            class="text-danger">
            The Excel file headers do not match the required format. Kindly refer sample Excel.
          </div>

          <div class="d-flex mt-4 justify-content-center" style="gap: 10px">
            <button type="button" id="bt_approve" class="button-submit button-left" [disabled]="uploadForm.invalid">
              <span class="ps-5">Submit</span>
              <img src="../../../assets/svg/accept.svg" class="left-arrow" />
            </button>
            <button type="button" id="bt_reject" class="button-back button-left"
              (click)="this.closeModal2('migrateModal')">
              <span class="ps-5">Cancel</span>
              <img src="../../../assets/svg/reject.svg" class="left-arrow" />
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<app-custom-modal [title]="isEdit? 'Edit Site Details':'Register New Site'" [width]="'50%'" *ngIf="isRegisterFormOpen"
  (closeModal)="closeFormModal()" (onClickCross)="onClickCross()">
  <div class="mb-0">
    <form [formGroup]="registerPlantForm" (ngSubmit)="onSubmit()">
      <div class="row d-flex">
        <!-- Company -->
        <div class="col-md-4 input-text-group">
          <label class="ml-12">Company <span class="text-danger"> * </span></label>
          <select class="form-control" formControlName="companyName">
            <option value="">Select Company</option>
            <option value="ACC">ACC</option>
            <option value="Ambuja">Ambuja</option>
          </select>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('companyName')?.touched|| registerPlantForm.get('companyName')?.dirty || submitted) && registerPlantForm.get('companyName')?.errors?.['required']">
            Company is required.
          </span>
        </div>
        <!-- Cluster -->
        <div class="col-md-4 input-text-group">
          <label class="ml-12">Cluster<span class="text-danger"> * </span></label>
          <select class="form-control" formControlName="clusterId">
            <option value="">Select Cluster</option>
            <option *ngFor="let cluster of clusters" [value]="cluster.id">{{ cluster.title }}</option>
          </select>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('clusterId')?.dirty || submitted) && registerPlantForm.get('clusterId')?.errors?.['required']">
            Cluster is required.
          </span>
        </div>


        <!-- Site Name -->
        <div class="col-md-4 input-text-group" >
          <label class="ml-12">Site Name<span class="text-danger"> * </span></label>
          <select class="form-control" formControlName="plantId" >
            <option value="">Select Site Name</option>
            <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
          </select>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('plantId')?.dirty || submitted) && registerPlantForm.get('plantId')?.errors?.['required']">
            Site Name is required.
          </span>
        </div>


        <!-- Latitude -->
        <div class="col-md-4 input-text-group">
          <label class="ml-12">Latitude<span class="text-danger"> * </span></label>
          <input type="text" class="form-control" formControlName="lat" placeholder="Enter latitude" />
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('lat')?.dirty||registerPlantForm.get('lat')?.touched || submitted) && registerPlantForm.get('lat')?.errors?.['required']">
            Latitude is required.
          </span>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('lat')?.dirty ||registerPlantForm.get('lat')?.touched || submitted) && registerPlantForm.get('lat')?.errors?.['pattern']">
            Enter Valid Latitude
          </span>
        </div>

        <!-- Longitude -->
        <div class="col-md-4 input-text-group">
          <label class="ml-12">Longitude<span class="text-danger"> * </span></label>
          <input type="text" class="form-control" formControlName="long" placeholder="Enter longitude" />
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('long')?.dirty||registerPlantForm.get('long')?.touched || submitted) && registerPlantForm.get('long')?.errors?.['required']">
            Longitude is required.
          </span>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('long')?.dirty|| registerPlantForm.get('long')?.touched || submitted) && registerPlantForm.get('long')?.errors?.['pattern']">
            Enter Valid Longitude
          </span>
        </div>

        <div class="col-md-4 input-text-group">
          <label class="ml-12">Emergency Contact Number</label><span class="text-danger"> * </span>
          <input  type="text" class="form-control" maxlength="10"
          formControlName="emergencyContactNumber" [placeholder]="'Enter Contact Number'"
          (keypress)="onKeyPress($event)" />
          <div class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('emergencyContactNumber')?.dirty||registerPlantForm.get('emergencyContactNumber')?.touched || submitted) && registerPlantForm.get('emergencyContactNumber')?.errors?.['required']">
            Emergency Contact Number is required.
          </div>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('emergencyContactNumber')?.dirty ||registerPlantForm.get('emergencyContactNumber')?.touched || submitted) && registerPlantForm.get('emergencyContactNumber')?.errors?.['pattern']">
            Enter Valid Contact Number
          </span>
        </div>

        <!-- Address -->
        <div class="col-md-8 input-text-group">
          <label class="fw-bold mb-1 ml-12">Address<span class="text-danger"> * </span></label>
          <textarea rows="4" placeholder="Enter Address here..." class="form-control"
            formControlName="address"></textarea>
          <span class="text-danger ml-12"
            *ngIf="(registerPlantForm.get('address')?.dirty|| registerPlantForm.get('address')?.touched  || submitted) && registerPlantForm.get('address')?.errors?.['required']">
            Address is required.
          </span>
        </div>

        <!-- Accordion -->
        <div class="input-text-group mt-2">
          <cdk-accordion class="example-accordion">
            <ng-container *ngFor="let item of items; let index = index">
              <cdk-accordion-item #accordionItem="cdkAccordionItem" class="example-accordion-item">
                <div class="accordion-card">
                  <div class="example-accordion-item-header" 
                   [attr.data-text]="item.title" (click)="onAccordionToggle(index); accordionItem.toggle()"
                   [ngClass]="{
                          'valid-header': accordionStates[index] && !accordionItem.expanded && isItemValid(item),
                          'invalid-header': accordionStates[index] && !accordionItem.expanded && !isItemValid(item)}"
                   >
                    <span>{{ item.title }}</span>
                    <button type="button" class="icon-toggle" aria-label="Toggle Accordion">
                      <div class="icon-toggle" [ngClass]="{
                          green: accordionStates[index] && !accordionItem.expanded && isItemValid(item),
                          red: accordionStates[index] && !accordionItem.expanded && !isItemValid(item)
                        }">
                        <i style="margin-top: 3px;margin-left: 3px;" class="bi" [ngClass]="{
                            'bi-chevron-right': !accordionItem.expanded,
                            'bi-chevron-down': accordionItem.expanded
                          }"></i>
                      </div>
                    </button>
                  </div>
                  <div class="row example-accordion-item-body"
                    [style.display]="accordionItem.expanded ? 'flex' : 'none'">
                    <div *ngFor="let field of item.fields" class="col-md-6  input-text-group">
                      <label class="ml-12">{{ field.label }} <span *ngIf="isFieldRequired(field.field)"
                          class="text-danger "> * </span></label>
                      <input *ngIf="field.label === 'Contact Number'" type="text" class="form-control" maxlength="10"
                        [formControlName]="field.field || ''" [placeholder]="'Enter ' + (field.label)"
                        (keypress)="onKeyPress($event)" />
                      <input *ngIf="field.label !== 'Contact Number'" type="text" class="form-control"
                        [formControlName]="field.field || ''" [placeholder]="'Enter ' + (field.label)" />
                      <span class="text-danger ml-12 "
                        *ngIf="(registerPlantForm.get(field.field)?.dirty || registerPlantForm.get(field.field)?.touched || submitted) && registerPlantForm.get(field.field)?.errors?.['required']">
                        {{ field.label }} is required.
                      </span>
                      <span class="text-danger"
                        *ngIf="(registerPlantForm.get(field.field)?.dirty || registerPlantForm.get(field.field)?.touched || submitted) && registerPlantForm.get(field.field)?.errors?.['pattern']">
                        <span class="text-danger ml-12 mt-1" *ngIf="field.label !== 'Email'">Enter Valid {{ field.label
                          }} </span> 
                          <span class="text-danger ml-12 mt-1" *ngIf="field.label === 'Email'">Invalid email.
                          Use an adani.com email.</span>
                      </span>
                      <span class="text-danger  ml-12 mt-1"
                        *ngIf="(registerPlantForm.get(field.field)?.dirty|| registerPlantForm.get(field.field)?.touched || submitted) && registerPlantForm.get(field.field)?.errors?.['email']">
                        Enter Valid {{ field.label }}
                      </span>
                      <span
                        *ngIf="(registerPlantForm.get(field.field)?.dirty ||registerPlantForm.get(field.field)?.touched || submitted) && registerPlantForm.get(field.field)?.errors"
                        class="text-danger ml-12 mt-1">
                        <span class="text-danger mt-1"
                          *ngIf="registerPlantForm.get(field.field)?.errors?.['duplicate']">
                          {{ registerPlantForm.get(field.field)?.errors?.['duplicate'] }}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </cdk-accordion-item>
            </ng-container>
          </cdk-accordion>
        </div>
      </div>

      <div class="row d-flex mt-4 btn-section ms-1">
        <div class="col-md-6">
          <button style="width: 100%;" [disabled]="isEdit && !isUpdateEnabled" class="button-submit"
            type="submit">{{isEdit?"Update":'Submit'}}</button>
        </div>
        <div class="col-md-6">
          <button style="width: 100%; margin-left: 0px;" type="button" (click)="resetRegisterPlantForm()"
            class="button-back">
            Reset
          </button>
        </div>
      </div>
    </form>


  </div>
</app-custom-modal>