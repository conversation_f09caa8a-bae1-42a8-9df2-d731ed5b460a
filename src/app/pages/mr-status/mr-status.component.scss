.apply-height{
  // height: 20%;
  height: 60vh;
  overflow-y: auto;
}
.card-header {
    background-color: #f8f9fa;
  }
  
  .card-header h5 {
    font-size: 1.25rem;
    font-weight: bold;
  }
  
  .card-header button {
    background: none;
    border: none;
  }
  
  .card-body {
    background-color: #fff;
    border-top: 1px solid #dee2e6;
  }
  
  .card-body .row {
    padding: 10px 0;
  }
  
  .card-body strong {
    font-weight: 600;
  }
  
  img {
    transform: rotate(0deg);
    transition: transform 0.3s ease-in-out;
  }
  
  button.collapsed img {
    transform: rotate(180deg);
  }

  .btn-link{
    width: 3.2%;
  }

.dropdown-card{
  background-color: white;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  >.dropdown-content{
     width: 96%;
     margin: 0 auto;
     >.dropdown-header{
      padding: 14px 0;
      >h5{
      font-weight: 600;
      }
     }
  }
}

.col-4{
  padding: 10px;
  font-size: 15px;
  height: 100%;
}

.vl {
  border-left: 1px solid #DADADA;
}
.vr{
  border-right: 1px solid #DADADA

}

.hl {
  border-top: 1px solid #DADADA
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 432px;
  width: 100%; /* Full viewport width */
}