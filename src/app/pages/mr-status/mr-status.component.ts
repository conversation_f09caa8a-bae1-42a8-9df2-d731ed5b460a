import { Component } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { MrStatusService } from '../../services/mr-status/mr-status.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { Router } from '@angular/router';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';

@Component({
  selector: 'app-mr-status',
  templateUrl: './mr-status.component.html',
  styleUrl: './mr-status.component.scss',
})
export class MrStatusComponent {
  isCollapsed1 = false;
  isCollapsed2 = false;
  mrData: any[] = [];
  selectedIndex: number = -1;
  adminId: any;
  plantId: any;
  loading: any;
  selectedplantId: any;
  isSuperAdmin: boolean = false;

  constructor(
    private breadcrumb: BreadcrumbService,
    private mrStatusService: MrStatusService,
    private router: Router,
    private adminPlantSelection: AdminPlantSelectionService
  ) {}
  ngOnInit() {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.breadcrumb.setBreadcrumbUrl(this.router.url);
          this.fetchMRStatus();
        });
    } else {
      this.getAdminDetail();
      this.breadcrumb.setBreadcrumbUrl(this.router.url);
      this.fetchMRStatus();
    }
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    console.log();
  }

  private fetchMRStatus() {
    this.loading = true;
    var data = {
      filter: [`plantId||eq||${this.plantId}`],
    };
    const param = createAxiosConfig(data);
    this.mrStatusService.getMRStatus(param).then((response) => {
      this.loading = false;
      this.mrData = response.reverse();
    });
  }

  toggleCollapse(index: number) {
    if (this.selectedIndex === index) {
      this.selectedIndex = -1; // Close the currently open dropdown
    } else {
      this.selectedIndex = index; // Open the clicked dropdown
    }
  }
    toPascalCase(str: any) {
    return str.replace(/\w+/g, function (w: any) {
      return w[0].toUpperCase() + w.slice(1).toLowerCase();
    });
  }
 getNotificationStatus(item: any): string {
  const status = item.notificationStatus;
  const createdDate = new Date(item.createdTimestamp);
  const today = new Date();
  
  
  // Remove time parts
  createdDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  console.log(createdDate,today);

  // Check condition
  if (status === 'CREATED' && createdDate < today) {
    return 'PENDING';
  }

  else return status.toUpperCase();
}

}
