<p class="fw-bold" style="margin-top: -8px">MR Status</p>

<div class="my-3 apply-height">
  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading">
    <div class="spinner-border text-primary" style="width: 10rem; height: 10rem;" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- No Data State -->
  <div class="no-data-container text-center alert alert-info mt-3" *ngIf="!loading && (!mrData || mrData.length === 0)">
    <h4 class="text-muted">No Data Available</h4>
    <p class="text-secondary">
      It looks like there’s nothing to show right now. Please check back later or adjust your filters.
    </p>
  </div>

  <!-- Data Display -->
  <div *ngIf="!loading && mrData && mrData.length > 0">
    <div class="mb-3 dropdown-card" *ngFor="let item of mrData; let i = index">
      <!-- Card header -->
      <div class="dropdown-content">
        <div class="dropdown-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ item.functionalLocation }}</h5>
          <button class="btn btn-link p-0" (click)="toggleCollapse(i)">
            <!-- Arrow changes depending on collapse state -->
            <img [src]="
              selectedIndex === i
                ? '../../../assets/svg/dropdown-up-arrow.svg'
                : '../../../assets/svg/dropdown-down-arrow.svg'
            " alt="toggle" />
          </button>
        </div>

        <!-- Card body (collapsible section) -->
        <div [ngClass]="{
          'collapse show': selectedIndex === i,
          collapse: selectedIndex !== i
        }">
          <div class="body">
            <div class="row row1 pt-2 hl">
              <div class="col-4 col-box-1">
                <strong>Checklist Name :</strong> {{ toPascalCase(item.checklistName) }}
              </div>
              <div class="col-4">
                <strong>Checkpoint Name :</strong> {{toPascalCase( item.checkpointsName) }}
              </div>
              <div class="col-4 col-box-2">
                <strong>Current Status : {{getNotificationStatus(item)  }} </strong> 
              </div>
            </div>

            <div class="row hl">
              <div class="col-4 col-box-1">
                <strong>SAP MR Number :</strong> {{ item.notificationNumber }}
              </div>
              <div class="col-4 col-box-2">
                <strong>Equipment Name :</strong> {{toPascalCase( item.equipmentName) }}
              </div>
              <div class="col-4">
                <strong>MR Created Date :</strong> {{ item.createdTimestamp | date:'yyyy-MM-dd'}}
              </div>
            </div>

            <div class="row pb-2 hl">
              <div class="col-4 col-box-1">
                <strong>Equipment Number :</strong> {{ item.equipmentNumber }}
              </div>
              <div class="col-4">
                <strong>MR Created By :</strong> {{toPascalCase( item.mrcreatedBy) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
