.list-tile{
    background-color:#F8F8F8;
    border: 1px solid lightgray;
    border-radius: 10px;
    margin-top: 15px;
    padding: 10px;
}
.name {
    font-size: 14px;
    font-weight: 900;
    margin-bottom: 2px;

}
.u-id{
    font-size: 12px;
    color: #0B74B0;
    margin-top: 0px;
}
.leading {
    background-color: #909090;
    padding: 10px;
    border-radius: 50%;
    height: 40px;
    width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white; /* Ensures text is visible on the background */
    font-size: 14px; /* Adjust font size to fit within the circle */
    font-weight: 700; /* Make initials bold if needed */
    overflow: hidden; /* Prevents text from spilling out */
    white-space: nowrap; /* Prevents text from wrapping */
    text-overflow: ellipsis; /* Ensures text is properly truncated if necessary */
}
  .name-title{
    margin-left: 10px;
  }
img{
    height: 50px;
    width: 50px;
}
