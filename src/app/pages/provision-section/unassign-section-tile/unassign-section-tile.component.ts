import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-unassign-section-tile',
  templateUrl: './unassign-section-tile.component.html',
  styleUrl: './unassign-section-tile.component.scss'
})
export class UnassignSectionTileComponent {
  @Input() section: any = [];

  // Method to compute initials
  get initials(): string {
    if (this.section && this.section.name) {
      return this.section.name
        .split(' ') // Split the name into parts
        .map((part: string) => part.charAt(0).toUpperCase()) // Get the first character of each part and capitalize it
        .join(''); // Combine all initials
    }
    return ''; // Return empty if section or name is missing
  }
    toPascalCase(str: any) {
    return str.replace(/\w+/g, function (w: any) {
      return w[0].toUpperCase() + w.slice(1).toLowerCase();
    });
  }
}
