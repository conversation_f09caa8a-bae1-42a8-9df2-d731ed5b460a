import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-select-tile',
  templateUrl: './select-tile.component.html',
  styleUrl: './select-tile.component.scss'
})
export class SelectTileComponent {
  @Input() user: any = [];
  // Method to compute initials
  get initials(): string {
    if (this.user && this.user.firstName && this.user.lastName) {
      return (
        this.user.firstName.charAt(0).toUpperCase() +
        this.user.lastName.charAt(0).toUpperCase()
      );
    }
    return ''; // Return empty if user data is missing
  }
    toPascalCase(str: any) {
    return str.replace(/\w+/g, function (w: any) {
      return w[0].toUpperCase() + w.slice(1).toLowerCase();
    });
  }
}
