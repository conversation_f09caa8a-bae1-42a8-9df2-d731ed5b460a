.card{
    height: 600px;
}
.card-title{
    font-weight: 900;
    padding: 20px 0px 0px 20px;
}
.list{
    height: 430px;
    overflow: scroll;
}
.assigned-section-list{
    height: 480px;
    overflow: scroll;
}
.search-bar{
    height: 45px;
    border: 1px solid lightgray;
    border-radius: 10px;
    padding: 10px;
}
.search-input{
    border: none;
    font-size: 17px;
}
.search-icon{
    text-align: end;
}
.search-input:focus {
    outline: none;
  }
  .buttons-for-action {
    button {
      width: 140px;
      height: 34px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #076fb61a;
      border: 0;
      border-radius: 10px;
      font-weight: 600;
      font-size: 13px;
      > img {
        height: 100%;
        width: 20%;
      }
      > span {
        padding: 0 3px;
      }
    }
  }
  .button-left {
    display: flex;
    // justify-content: center;
    align-items: center;
    > span {
      //  justify-self: center;
      margin: 0 auto;
    }
  
    > .left-arrow {
      width: 61px;
      height: 61px;
      position: relative;
      // left: 15px;
    }
  }
  /* Ensure the row spans full width */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%; /* Full viewport width */
  }
  .gradient-header {
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
    color: white; /* Optional: Make text white for better contrast */
    padding: 1rem; /* Optional: Adjust padding for aesthetics */
    border-top-left-radius: 0.3rem; /* Match modal border radius */
    border-top-right-radius: 0.3rem;
  }
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  