import {
  ChangeDetectorRef,
  Component,
  HostListener,
  input,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AdminService } from '../../services/admin/admin.service';
import { SectionsService } from '../../services/sections/sections.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { Router } from '@angular/router';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';
declare var bootstrap: any;

@Component({
  selector: 'app-provision-section',
  templateUrl: './provision-section.component.html',
  styleUrl: './provision-section.component.scss',
})
export class ProvisionSectionComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  users: any = []; // This will hold your list of names
  id: number[] = [];
  currentPage: number = 1;
  pageSize: number = 10;
  isLoading: boolean = false; // Prevent multiple simultaneous calls
  totalRecords: number = 0; // Total records from the backend
  selectedUser: any = {};
  sections: any = [];
  assignedSections: any = [];
  unAssignedSections: any = [];
  currentUser: any = {};
  clickLoading: any;
  selectedplantId: any;
  plantId: any;
  isSuperAdmin: boolean = false;

  constructor(
    private adminService: AdminService,
    private sectionService: SectionsService,
    private cdr: ChangeDetectorRef,
    private breadcrumbservice: BreadcrumbService,
    private router: Router,
    private adminPlantSelection: AdminPlantSelectionService
  ) {}
  ngOnInit(): void {
    this.currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(
          filter((plantId) => !!plantId) // wait for a truthy plantId
        )
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.currentPage=1;
          this.fetchUsers(this.currentPage);
          this.fetchSections();
          this.breadcrumbservice.setBreadcrumbUrl(this.router.url);
        });
    } else {
      this.fetchUsers(this.currentPage);
      this.fetchSections();
      this.breadcrumbservice.setBreadcrumbUrl(this.router.url);
    }
  }

  handleCallback(data: number): void {
    this.updateUser(data);
  }

  handleUnassign(data: number): void {
    this.assignedSections.push(this.unAssignedSections[data]);
    this.unAssignedSections.splice(data, 1);
  }

  handleAssign(data: number): void {
    this.unAssignedSections.push(this.assignedSections[data]);
    this.assignedSections.splice(data, 1);
  }

  assignSection() {
    this.clickLoading = true;
    const data = {
      adminId: this.currentUser.id,
      plantId: this.selectedUser.plantIds[0],
      userId: this.selectedUser.id,
      wbiSection:
        this.assignedSections.length > 0
          ? this.assignedSections.map((section: { id: any }) => section.id)
          : null,
    };
    this.sectionService.assignSection(data).then((response) => {
      this.clickLoading = false;
      if (response.responseCode == 200) {
        if (this.assignedSections.length > 0) {
          this.closeAssignSectionModal();
          this.toast.showSuccessToast('Section assign successfully');
        } else {
          this.closeRemoveSectionModal();
          this.toast.showSuccessToast('Section remove successfully');
        }
        this.resetData();
      } else {
        this.toast.showErrorToast(response.message);
      }
    });
  }

  private updateUser(id: number): void {
    console.log('updateUser called, users length:', this.users.length);
    // Ensure that the users array has been populated before calling this
    if (this.users && this.users.length > 0) {
      // Reset the 'selected' status for all users to false
      this.users.forEach((user: { selected: boolean }) => {
        user.selected = false;
      });

      // Set the selected user to true
      if (this.users[id]) {
        this.users[id].selected = true;
        this.selectedUser = this.users[id];
        this.getUserAssignedAndUnassignedSection(this.selectedUser);
      }
    } else {
      console.log('No users available or array is empty');
    }
  }

  getUserAssignedAndUnassignedSection(selectedUser: any) {
    if (selectedUser.wbiSection == null) {
      this.assignedSections = [];
      this.unAssignedSections = this.sections;
    } else {
      this.assignedSections = this.sections.filter((item: { id: any }) =>
        selectedUser.wbiSection.includes(item.id)
      );
      this.unAssignedSections = this.sections.filter(
        (item: { id: any }) => !selectedUser.wbiSection.includes(item.id)
      );
    }
  }

  fetchUsers(page: number) {
    if (this.isLoading) return; // Prevent overlapping calls
    console.log('fetch user ......');

    this.isLoading = true;
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : this.currentUser.plantIds[0]; 
    const data = {
      page: page,
      limit: this.pageSize,
      or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
      filter: [
        'wbiRoleId||eq||3',
        'enabled||eq||true',
        `plantIds||in||{${this.plantId}}`,
        'wbiStatus||eq||1',
      ],
    };
    console.log('fetch user param ', data);

    const param = createAxiosConfig(data);

    this.adminService.getUsers(param).then((response) => {
      console.log(response);

      this.users = [...response.data.map((user: { name: any }) => user)]; // Append new data
      this.totalRecords = response.total; // Adjust based on API response
      this.isLoading = false;
      this.currentPage++;
    });
  }

  fetchSections() {
    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : this.currentUser.plantIds[0];
    const data = {
      filter: [
        `plantId||eq||${this.plantId}`,
        'enabled||eq||true',
        'isSectionStandBy||eq||0',
      ],
    };
    const param = createAxiosConfig(data);
    this.sectionService.getSections(param).then((response: any) => {
      this.sections = response;
    });
  }

  @HostListener('scroll', ['$event'])
  onScroll(event: any): void {
    const target = event.target;
    const scrollReachedBottom =
      target.scrollHeight - target.scrollTop <= target.clientHeight + 100;

    if (scrollReachedBottom && this.users.length < this.totalRecords) {
      const totalPages = Math.ceil(this.totalRecords / this.pageSize);
      console.log(totalPages, this.currentPage); 
      if (this.currentPage <= totalPages) {
        this.fetchUsers(this.currentPage);
      }
    }
  }

  resetForm() {
    throw new Error('Method not implemented.');
  }

  onSearchChange($event: Event) {
    const inputValue = ($event.target as HTMLInputElement).value;
    const trimmedInput = inputValue.trim();
    if (inputValue != '') {
      if (this.isLoading) return; // Prevent overlapping calls
      this.isLoading = true;

      const data = {
        page: 1,
        limit: this.pageSize,
        or: ['applicationId||$eq||2', 'applicationId||$eq||3'],
        filter: ['wbiRoleId||eq||3', 'enabled||eq||true', 'wbiStatus||eq||1'],
      };
      if (trimmedInput.includes(' ')) {
        // Split the input by spaces and handle extra spaces
        const [firstName, ...lastNameParts] = trimmedInput.split(/\s+/); // Split by one or more spaces
        const lastName = lastNameParts.join(' '); // Join remaining parts to form lastName

        // Add filters for both firstName and lastName
        if (firstName) {
          data.filter.push(`firstName||$contL||${firstName}`);
        }
        if (lastName) {
          data.filter.push(`lastName||$contL||${lastName}`);
        }
      } else {
        // If no space, add a filter for firstName only
        data.filter.push(`firstName||$contL||${trimmedInput}`);
      }
      const param = createAxiosConfig(data);

      this.adminService.getUsers(param).then((response) => {
        this.users = response.data.map((user: { name: any }) => user); // Append new data
        this.totalRecords = response.total; // Adjust based on API response
        this.isLoading = false;
      });
    } else {
      this.users = [];
      this.fetchUsers(1);
    }
  }

  onSearchSectionChange($event: Event) {
    const inputValue = ($event.target as HTMLInputElement).value;
    if (inputValue != '') {
      var searchedUnAssignedSections = this.unAssignedSections.filter(
        (section: any) =>
          section.name.toLowerCase().includes(inputValue.toLowerCase())
      );
      this.unAssignedSections = searchedUnAssignedSections;
    } else {
      this.getUserAssignedAndUnassignedSection(this.selectedUser);
    }
  }

  openConfirmationModel() {
    if (this.assignedSections.length > 0) {
      const modalElement = document.getElementById('assignSectionModal');
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    } else {
      const modalElement = document.getElementById('removeSectionModal');
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    }
  }

  closeAssignSectionModal() {
    const modalElement = document.getElementById('assignSectionModal');
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
      modalInstance.hide();
    }
    this.resetData();
  }

  closeRemoveSectionModal() {
    const modalElement = document.getElementById('removeSectionModal');
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
      modalInstance.hide();
    }
    this.resetData();
  }

  resetData() {
    this.users = [];
    this.assignedSections = [];
    this.unAssignedSections = [];
    this.selectedUser = {};
    this.sections = [];
    this.currentPage = 0;
    this.fetchUsers(this.currentPage);
    this.fetchSections();
  }
}
