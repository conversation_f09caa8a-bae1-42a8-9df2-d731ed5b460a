<app-toast-message></app-toast-message>

<!-- Loading Overlay -->
<div *ngIf="clickLoading" class="loading-overlay">
    <div class="spinner-container">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-2">Please wait...</div>
    </div>
</div>

<div class="container-fluid">
    <p class="fw-bold">Provision Section</p>

    <!-- Row for the content sections -->
    <div class="row">
        <div class="col-12 col-md-4 mb-4">
            <div class="card shadow">
                <h6 class="card-title">Employees</h6>
                <hr />
                <div class="card-body">
                    <div class="search-bar shadow-sm">
                        <div class="row">
                            <div class="col-10">
                                <input (input)="onSearchChange($event)" class="search-input w-100" type="text" placeholder="Search for employees">
                            </div>
                            <div class="col-2 search-icon">
                                <i class="bi bi-search"></i>
                            </div>
                        </div>
                    </div>
                    <div class="list" (scroll)="onScroll($event)">
                        <div *ngFor="let user of users; let i = index">
                            <app-select-tile [user]="user" (click)="handleCallback(i)"></app-select-tile>
                        </div>
                        <div class="loading-container" *ngIf="isLoading">
                            <div class="spinner-border text-primary" style="width: 2rem; height: 2rem;" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-4 mb-4">
            <div class="card shadow">
                <h6 class="card-title">Unassigned Sections</h6>
                <hr />
                <div class="card-body">
                    <div class="search-bar shadow-sm">
                        <div class="row">
                            <div class="col-10">
                                <input (input)="onSearchSectionChange($event)" class="search-input w-100" type="text" placeholder="Search Here...">
                            </div>
                            <div class="col-2 search-icon">
                                <i class="bi bi-search"></i>
                            </div>
                        </div>
                    </div>
                    <div class="list">
                        <div *ngFor="let unassignedSection of unAssignedSections; let i = index">
                            <app-unassign-section-tile [section]="unassignedSection" (click)="handleUnassign(i)"></app-unassign-section-tile>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-4">
            <div class="card shadow">
                <h6 class="card-title">Assigned Sections</h6>
                <hr />
                <div class="card-body">
                    <div class="assigned-section-list">
                        <div *ngFor="let assignSection of assignedSections; let i = index">
                            <app-assign-section-tile [section]="assignSection" (click)="handleAssign(i)"></app-assign-section-tile>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submit Button Row -->
    <div class="row mt-3 ms-2">
        <div class="d-flex gap-3 justify-content-start">
            <button type="button" id="bt_approve" class="button-submit button-left" (click)="openConfirmationModel()">
                <span class="ps-5">Submit</span>
                <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
            </button>
        </div>
    </div>

    <!-- Spacer -->
    <div style="height: 50px"></div>
</div>

<!-- Modal for Assign Section -->
<div class="modal fade" id="assignSectionModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header gradient-header">
                <h5 class="modal-title" id="uploadModalLabel">Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to assign these sections?</p>
            </div>
            <div class="modal-footer">
                <div class="d-flex gap-3 justify-content-center w-100">
                    <button type="button" id="bt_approve" class="button-submit button-left" (click)="assignSection()">
                        <span class="ps-5">Yes</span>
                        <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
                    </button>
                    <button type="button" id="bt_reject" class="button-back button-left" (click)="closeAssignSectionModal()">
                        <span class="ps-5">No</span>
                        <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Remove Section -->
<div class="modal fade" id="removeSectionModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header gradient-header">
                <h5 class="modal-title" id="uploadModalLabel">Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove these sections?</p>
            </div>
            <div class="modal-footer">
                <div class="d-flex gap-3 justify-content-center w-100">
                    <button type="button" id="bt_approve" class="button-submit button-left" (click)="assignSection()">
                        <span class="ps-5">Yes</span>
                        <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
                    </button>
                    <button type="button" id="bt_reject" class="button-back button-left" (click)="closeRemoveSectionModal()">
                        <span class="ps-5">No</span>
                        <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
