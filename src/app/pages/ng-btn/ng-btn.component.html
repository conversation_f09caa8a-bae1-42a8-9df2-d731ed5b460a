<button
  [ngClass]="{
    'button-submit': btnType === 'Submit' || !btnType,
    'button-back': btnType === 'Cancel',
    'button-right': imgHref && (imgAlign === 'right' || !imgAlign),
    'button-left': imgHref && imgAlign === 'left'
  }"
  [style.width]="btnWidth + 'px'"
  (click)="onClick()"
>
  <ng-container *ngIf="isImg && imgHref; else buttonLabel">
    <ng-container *ngIf="imgAlign === 'left'; else rightAlignedImg">
        <img 
          [src]="imgHref"
          [ngClass]="'left-arrow'"
          [style.height]="imgHeight + 'px'"
          [style.width]="imgWidth + 'px'"
        />
        <span>{{ buttonText }}</span>
      </ng-container>
      
      <!-- If imgAlign is 'right', place image after the span -->
      <ng-template #rightAlignedImg>
        <span class="ps-4">{{ buttonText }}</span>
        <img 
          [src]="imgHref"
          [ngClass]="'right-arrow'"
          [style.height]="imgHeight + 'px'"
          [style.width]="imgWidth + 'px'"
        />
      </ng-template>
  
  </ng-container>
  <ng-template #buttonLabel>
    {{ buttonText }}
  </ng-template>
</button>



