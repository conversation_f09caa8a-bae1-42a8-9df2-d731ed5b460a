import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-ng-btn',
  templateUrl: './ng-btn.component.html',
  styleUrl: './ng-btn.component.scss'
})
export class NgBtnComponent {
  constructor(){};

  @Output() buttonClicked = new EventEmitter<void>();
  @Input() buttonText: any = '';
  @Input() isImg: boolean = false;
  @Input() imgHref: any = '';
  @Input() btnType: any = '';
  @Input() imgAlign: any = '';
  @Input() imgHeight: number = 49; 
  @Input() imgWidth: number = 49; 
  @Input() btnWidth: number = 49; 



  onClick() {
    this.buttonClicked.emit();
  }
}
