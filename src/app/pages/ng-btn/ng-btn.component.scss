button{
  display: flex;
  justify-content: center;  
  align-items: center;      
  text-align: center;       
  padding: 0; 
    &:hover{
        cursor: pointer;
    }
}
.button-submit {
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
    border: 0;
    border-radius: 30px;
    height: 39px;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    width: 156px;
}
  
  .button-back {
    border: 1px solid;
    border-radius: 30px;
    height: 39px;
    color: #bd3861 !important;
    font-size: 12px;
    font-weight: 600;
    width: 183px;
    margin: 0 20px;
    width: 156px;
    background: #fff;
}
  
  .button-browser {
    padding: 6px 6px !important;
    font-size: 12px !important;
    font-weight: 600;
    background: #fcfdfd !important;
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
    border: solid 1px #d5d5d5 !important;
}

.button-right {
    display: flex;
    align-items: center;
    span {
      margin-left: 50px;
    }
    .right-arrow {      
      margin-left: 40px;
    }
  }
  .button-left {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      margin-right: 50px;
    }
    .left-arrow {
      padding-left: 5px;
      margin-left: -22px;
    }
  }
  
  button[disabled] {
    opacity: 0.5;
    cursor: no-drop !important;
  }
  