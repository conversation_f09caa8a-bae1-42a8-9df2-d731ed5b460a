.outer-container {
  height: 370px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  padding: 10px;
  background-color: #f9f9f9;

  .section-item {
    display: flex;
    justify-content: space-between;
    align-items: center; /* Aligns text and arrow vertically */
    padding: 5px;
    border: 1px solid;
    margin-top: 10px;
    border-radius: 20px;
    box-sizing: border-box; /* Ensures padding doesn't affect layout */

    &.selected {
      background-color: #1976d2;
      color: white;
    }
  }
  
  .section-info p {
    margin: 0; /* Removes default paragraph margins */
    font-size: 16px; /* Adjust text size as needed */
  }
  
  .arrow img {
    height: 50px; /* Standardize arrow icon size */
    width: auto;
  }
}
.section-list {
  background-color: white;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  height: 370px;
  overflow-y: auto;
}
.checkbox-grid {
  // gap: 10px;
  margin: 6px 8px;
}

.checkbox-container {
  margin-bottom: 12px;
  > div {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid #ccc;
    padding: 8px;
    border-radius: 5px;
    font-size: 14.4px;
    cursor: pointer;
    > label {
      &:hover {
        cursor: pointer;
      }
    }
  }
}
.checkbox-container > div.disabled {
  background-color: #f0f0f0; /* Grey background for disabled items */
  border-color: #d3d3d3; /* Lighter border */
  color: #9e9e9e; /* Greyed-out text */
  cursor: pointer; /* Change cursor to indicate non-interaction */
}
.checkbox-container > div.disabled label {
  color: #8a8a8a;
}

.checkbox-container > div.disabled input[type="checkbox"] {
  pointer-events: none; /* Prevent clicks on disabled checkboxes */
}

.checkbox-container > div.selected.disabled {
  background-color: #d0d0d0; /* Slightly darker grey for selected disabled */
  border-color: #a0a0a0;
  font-weight: bold;
}

.checkbox-container > div.selected {
  background-color: #e0f7fa;
  border-color: #00796b;
  font-weight: bold;
}

input[type="checkbox"] {
  margin-right: 10px;
  display: none; /* Hide default checkbox */
}

.checkbox-container.selected label {
  color: #00796b;
}

.reason {
  background-color: white;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  height: 65vh;
  margin-bottom: 12px;
  margin-top: 20px;
  overflow-y: auto;
  > .reason-form {
    padding: 14px;
  }
}
.buttons-for-action {
  button {
    width: 140px;
    height: 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #076fb61a;
    border: 0;
    border-radius: 10px;
    font-weight: 600;
    font-size: 13px;
    > img {
      height: 100%;
      width: 20%;
    }
    > span {
      padding: 0 3px;
    }
  }
}
.buttons-for-action button.selected {
    background-color: #baf7ff;
    // color: white;
    border: 1px solid black;
}
.buttons-for-action button > img {
    height: 100%;
    width: 20%;
}
.buttons-for-action button > span {
    padding: 0 3px;
}
.from-date {
  > input {
    height: 36px;
  }
}
.to-date {
  > input {
    height: 36px;
  }
}
.button-left {
  display: flex;
  // justify-content: center;
  align-items: center;
  > span {
    //  justify-self: center;
    margin: 0 auto;
  }

  > .left-arrow {
    width: 61px;
    height: 61px;
    position: relative;
    left: 15px;
  }
}
.gradient-header {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white; /* Optional: Make text white for better contrast */
  padding: 1rem; /* Optional: Adjust padding for aesthetics */
  border-top-left-radius: 0.3rem; /* Match modal border radius */
  border-top-right-radius: 0.3rem;
}

.adani-gradient {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
  border: none;
  color: white;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%; /* Full viewport width */
  background-color: #f8f9fa; /* Optional: light background */
}
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
