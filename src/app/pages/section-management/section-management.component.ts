import { Component, OnInit, ViewChild } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SectionsService } from '../../services/sections/sections.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { StandByEquipmentsService } from '../../services/stand-by-equipments/stand-by-equipments.service';
import { DatePipe } from '@angular/common';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { Router } from '@angular/router';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';
declare var bootstrap: any;

@Component({
  selector: 'app-section-management',
  templateUrl: './section-management.component.html',
  styleUrl: './section-management.component.scss',
})
export class SectionManagementComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  users: any;
  todayDate: string = '';
  selectedSectionIndex: any = 0;
  sectionLoading: any;
  equipmentLoading: any;
  clickLoading: any;
  onScroll($event: Event) {
    throw new Error('Method not implemented.');
  }
  sections: any = [];
  equipments: any = [];
  equipmentId: number = -1;
  sectionId: number = -1;
  selectedSection: any;
  adminId: number = -1;
  plantId: number = -1;
  selectedItems: any[] = [];
  selectionType: 'disabled' | 'enabled' | null = null;
  selectedCondition: string = '';
  reasonForm!: FormGroup;
  selectedplantId: any;
  selectedDisableEquipment: any = {};
  isSuperAdmin: boolean = false;
  constructor(
    private breadcrumb: BreadcrumbService,
    private fb: FormBuilder,
    private sectionService: SectionsService,
    private equipmentsService: StandByEquipmentsService,
    private router: Router,
    private adminPlantSelection: AdminPlantSelectionService,
    private datePipe: DatePipe
  ) {}

  ngOnInit() {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.selectedplantId = adminPlantId;
          this.getAdminDetail();
          this.equipments=[];
          this.getData();
        });
    } else this.getAdminDetail();
    const today = new Date();
    this.todayDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    // this.getAdminDetail();
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
    this.buildMyForm();
    this.getData();
    this.reasonForm = this.fb.group({
      fromDate: [this.todayDate, Validators.required],
      toDate: [this.todayDate, Validators.required],
      reason: [
        '',
        [
          Validators.required,
          Validators.minLength(15),
          Validators.pattern(/^(?=.*[a-zA-Z])[a-zA-Z0-9\s\W]+$/), // Ensures at least one alphabet is present
        ],
      ],
      selectedCondition: ['', Validators.required],
    });
  }

  validateDates(): boolean {
    const fromDate = this.reasonForm.get('fromDate')?.value;
    const toDate = this.reasonForm.get('toDate')?.value;
    return fromDate && toDate && new Date(toDate) >= new Date(fromDate);
  }

  getData() {
    this.getSections().then((firstSection) => {
      if (firstSection != undefined) {
        this.sectionId = firstSection.id;
        this.selectedSection = firstSection;
        this.getEquipments(firstSection.id);
      }
    });
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;

    this.plantId = this.selectedplantId
      ? this.selectedplantId
      : currentUser.plantIds[0];
    console.log('plantid', this.plantId);
  }

  async getSections() {
    this.sectionLoading = true;
    const data = {
      filter: [`plantId||eq||${this.plantId}`, 'enabled||eq||true'],
    };
    const param = createAxiosConfig(data);
    await this.sectionService.getSections(param).then((response) => {
      console.log(response,'response');
      
      this.sectionLoading = false;
      this.sections = response;
    });
    console.log(this.sections[0]);
    return this.sections[0];
  }

  private getEquipments(sectionId: number) {
    console.log('get equipment called');
    this.equipmentLoading = true;
    const data = {
      filter: [`sectionId||eq||${sectionId}`, 'enabled||eq||true'],
    };
    const param = createAxiosConfig(data);
    this.equipmentsService.getEquipmentsBySection(param).then((response) => {
      this.equipmentLoading = false;
      this.equipments = response;
    });
  }

  buildMyForm() {
    this.reasonForm = this.fb.group({
      fromDate: [this.todayDate, Validators.required],
      toDate: [this.todayDate, Validators.required],
      reason: ['', Validators.required],
    });
  }
  toggleSelection(item: any) {
    const isDisabled = item.isEquipmentStandBy == 1;
    if (isDisabled) {
      this.selectedDisableEquipment = item;
      console.log(this.selectedDisableEquipment);
      this.openModal();
      return;
    }
    this.equipmentId = item.id;
    const index = this.selectedItems.indexOf(item);
    if (index === -1) {
      this.selectedItems = [];
      this.selectedItems.push(item);
    } else {
      this.selectedItems.splice(index, 1);
    }

    // If all items are deselected, reset the selection type
    if (this.selectedItems.length === 0) {
      this.selectionType = null;
    }
  }

  showEquipments(section: any, index: number) {
    this.selectedSection = section;
    this.selectedSectionIndex = index;
    this.getEquipments(section.id);
    this.sectionId = section.id;
  }
    toPascalCase(str: any) {
    return str.replace(/\w+/g, function (w: any) {
      return w[0].toUpperCase() + w.slice(1).toLowerCase();
    });
  }

  selectCondition(condition: string) {
    this.selectedCondition = condition; // Update the selected condition
    this.reasonForm.patchValue({ selectedCondition: condition });
  }

  openDisableEquipmentModal() {
    if (this.reasonForm.valid && this.validateDates()) {
      const modalElement = document.getElementById('disableEquipmentModal');
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    } else {
      if (this.selectedCondition == '') {
        this.reasonForm.markAllAsTouched();
      }
    }
  }

  closeDisableEquipmentModal() {
    const modalElement = document.getElementById('disableEquipmentModal');
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
      modalInstance.hide();
    }
  }

  disableEquipment() {
    const formValues = this.reasonForm.value;
    formValues['addByAdminId'] = this.adminId;
    formValues['plantId'] = this.plantId;
    formValues['sectionId'] = this.sectionId;
    formValues['equipmentId'] = this.equipmentId;
    formValues['status'] = 1;
    formValues['condition'] = this.selectedCondition;
    formValues['fromDate'] = formValues['fromDate'] = new Date(
      formValues['fromDate']
    ).toISOString();
    formValues['toDate'] = formValues['toDate'] = new Date(
      formValues['toDate']
    ).toISOString();
    this.equipmentsService
      .addEquipmentToStandBy(formValues)
      .then((response) => {
        if (response.status == 200) {
          this.closeDisableEquipmentModal();
          this.toast.showSuccessToast(
            'Equipment successfully placed on standby.'
          );
          this.equipments = [];
          this.sections = [];
          this.getData();
          this.resetForm();
        } else {
          this.toast.showErrorToast(
            'Error!, Failed to place equipment on standby.'
          );
        }
      });
  }
  resetForm() {
    this.selectedCondition = '';
    this.buildMyForm();
  }

  openModal() {
    const modalElement = document.getElementById('enableEquipmentModal');
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
  }
  closeModal() {
    // Close the modal
    const modalElement = document.getElementById('enableEquipmentModal');
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
      modalInstance.hide();
    }
  }
  enableEquipments() {
    var body = {
      adminId: this.adminId,
      plantId: this.plantId,
      sectionId: this.selectedDisableEquipment.sectionId,
      equipmentId: this.selectedDisableEquipment.id,
    };
    this.equipmentsService.removeEquipmentFromStandBy(body).then((response) => {
      this.closeModal();
      if (response.status == 200) {
        this.toast.showSuccessToast(
          'Equipment successfully removed on standby.'
        );
        this.equipments = [];
        this.sections = [];
        this.getData();
      } else {
        this.toast.showErrorToast(
          'Error!, Failed to remove equipment on standby.'
        );
      }
    });
  }

  openConfirmationModel() {
    if (this.selectedSection.isSectionStandBy == 1) {
      const modalElement = document.getElementById('enableSectionModal');
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    } else if (this.selectedSection.isSectionStandBy == 0) {
      const modalElement = document.getElementById('disableSectionModal');
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    }
  }

  enableSection() {
    this.clickLoading = true;
    var body = {
      adminId: this.adminId,
      plantId: this.plantId,
      sectionId: this.selectedSection.id,
    };
    this.sectionService.removeSectionToStandBy(body).then((response) => {
      this.clickLoading = false;
      const modalElement = document.getElementById('enableSectionModal');
      const modalInstance = bootstrap.Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.hide();
      }
      if (response.status == 200) {
        this.toast.showSuccessToast('Section successfully placed on standby.');
        this.equipments = [];
        this.sections = [];
        this.getData();
      } else {
        this.toast.showErrorToast(
          'Error!, Failed to place section on standby.'
        );
      }
    });
  }

  disableSection() {
    this.clickLoading = true;
    var body = {
      adminId: this.adminId,
      plantId: this.plantId,
      sectionId: this.selectedSection.id,
    };
    this.sectionService.addSectionToStandBy(body).then((response) => {
      this.clickLoading = false;
      const modalElement = document.getElementById('disableSectionModal');
      const modalInstance = bootstrap.Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.hide();
      }
      if (response.status == 200) {
        this.toast.showSuccessToast('Section successfully removed on standby.');
        this.toast.showSuccessToast('Section successfully placed on standby.');
        this.equipments = [];
        this.sections = [];
        this.getData();
      } else {
        this.toast.showErrorToast(
          'Error!, Failed to remove section on standby.'
        );
      }
    });
  }
}
