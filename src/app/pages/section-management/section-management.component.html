<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>
<p class="fw-bold">Section Management</p>
<div class="row">
  <div class="col-4">
    <div class="outer-container" (scroll)="onScroll($event)">
      <div *ngIf="!sectionLoading">
        <div *ngIf="sections.length==0" style="height: 58vh;" class="d-flex justify-content-center align-items-center fw-bold fs-5">No Data Found</div>
        <div class="sections-list" *ngFor="let section of sections; let i = index">
          <div class="section-item" [ngClass]="{ selected: selectedSectionIndex === i }"
            (click)="showEquipments(section,i)">
            <div class="section-info">
              <p>{{toPascalCase( section.name) }}</p>
            </div>
            <div class="arrow">
              <img [src]="
                  selectedSectionIndex === i
                    ? '../../../assets/svg/whitearrow.svg'
                    : '../../../assets/svg/bluearrow.svg'
                " alt="Arrow" />
            </div>
          </div>
        </div>
      </div>
      <div class="loading-container" *ngIf="sectionLoading">
        <div class="spinner-border text-primary" style="width: 5rem; height: 5rem;" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>
  </div>
  <div class="col-8">
    <div class="section-list mb-3">
      <div class="checkbox-grid row" *ngIf="!equipmentLoading">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="fw-bold m-0">Equipments List</h6>
          <button class="btn adani-gradient" (click)="openConfirmationModel()">{{ selectedSection?.isSectionStandBy == 1 ? 'Enable Section' : 'Disable Section' }}</button>
        </div>
        
        <div *ngFor="let equipment of equipments; let i = index" 
             class="checkbox-container col-4"
             (click)="toggleSelection(equipment)">
          <div [ngClass]="{
            selected: selectedItems.includes(equipment),
            disabled: equipment.isEquipmentStandBy == 1
          }">
            <input type="checkbox" 
                   [checked]="selectedItems.includes(equipment)"
                   [disabled]="equipment.isEquipmentStandBy == 1" 
                   (change)="toggleSelection(equipment)" />
            <label>{{ equipment.name }}</label>
          </div>
        </div>
      </div>
      <div class="loading-container" *ngIf="equipmentLoading">
        <div class="spinner-border text-primary" style="width: 5rem; height: 5rem;" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="reason" *ngIf="selectedItems.length > 0">
  <form class="reason-form" [formGroup]="reasonForm">
    <div class="row">
      <!-- From Date -->
      <div class="col-4">
        <label for="fromDate" class="mb-1 fw-bold">From</label>
        <div class="input-text-group from-date">
          <input 
            type="date" 
            class="form-control" 
            formControlName="fromDate" 
            [attr.min]="todayDate" 
          />
        </div>
        <div class="text-danger mt-1" *ngIf="reasonForm.get('fromDate')?.invalid && reasonForm.get('fromDate')?.touched">
          From date is required.
        </div>
      </div>

      <!-- To Date -->
      <div class="col-4">
        <label for="toDate" class="mb-1 fw-bold">To</label>
        <div class="input-text-group to-date">
          <input 
            type="date" 
            class="form-control" 
            formControlName="toDate" 
            [attr.min]="todayDate" 
          />
        </div>
        <div class="text-danger mt-1" *ngIf="reasonForm.get('toDate')?.invalid && reasonForm.get('toDate')?.touched">
          To date is required.
        </div>
        <div class="text-danger mt-1" *ngIf="!validateDates()">
          To date cannot be before From date.
        </div>
      </div>
    </div>

    <!-- Reason -->
    <div class="row mt-3">
      <div class="col-8">
        <label for="reason" class="fw-bold mb-1">Reason</label>
        <div class="input-text-group">
          <textarea rows="4" placeholder="Enter Comment here..." class="form-control"
            formControlName="reason"></textarea>
        </div>
        <div class="text-danger mt-1" *ngIf="reasonForm.get('reason')?.invalid && reasonForm.get('reason')?.touched">
          <span *ngIf="reasonForm.get('reason')?.errors?.['required']">Reason is required.</span>
          <span *ngIf="reasonForm.get('reason')?.errors?.['minlength']">
            Reason must be at least 15 characters long.
          </span>
          <span *ngIf="reasonForm.get('reason')?.errors?.['pattern']">Reason cannot contain only numbers or symbols.</span>
        </div>
      </div>
    </div>

    <!-- Condition Selection -->
    <div class="row mt-3">
      <label for="condition" class="fw-bold mb-1">Choose Condition</label>
      <div class="d-flex gap-3 buttons-for-action">
        <button type="button" (click)="selectCondition('PM')" 
          [ngClass]="{ selected: selectedCondition === 'PM' }">
          <img src="../../../assets/svg/project-plan 1.svg" alt="" />
          <span>PM</span>
        </button>

        <button type="button" (click)="selectCondition('Breakdown')" 
          [ngClass]="{ selected: selectedCondition === 'Breakdown' }">
          <img src="../../../assets/svg/wrench 1.svg" alt="" />
          <span>Breakdown</span>
        </button>

        <button type="button" (click)="selectCondition('IDLE')" 
          [ngClass]="{ selected: selectedCondition === 'IDLE' }">
          <img src="../../../assets/svg/Vector.svg" alt="" />
          <span>IDLE</span>
        </button>
      </div>
      <div class="text-danger mt-1" *ngIf="reasonForm.get('selectedCondition')?.invalid && reasonForm.get('selectedCondition')?.touched">
        Please select a condition.
      </div>
    </div>

    <!-- Submit & Reset Buttons -->
    <div class="row mt-3 ms-2">
      <div class="d-flex gap-3">
        <button type="button" id="bt_approve" class="button-submit button-left" (click)="openDisableEquipmentModal()">
          <span class="ps-5">Submit</span>
          <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
        </button>
        <button type="button" id="bt_reject" class="button-back button-left" (click)="resetForm()">
          <span class="ps-5">Reset</span>
          <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
        </button>
      </div>
    </div>
  </form>
</div>

<!-- Enable equipment Modal -->
<div class="modal fade" id="enableEquipmentModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <h5 class="modal-title" id="uploadModalLabel">Confirmation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure, you want to enable this equipment ?</p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button type="button" id="bt_approve" class="button-submit button-left" (click)="enableEquipments()">
            <span class="ps-5">Yes</span>
            <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
          </button>
          <button type="button" id="bt_reject" class="button-back button-left" (click)="closeModal()">
            <span class="ps-5">No</span>
            <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Disable equipment Modal -->
<div class="modal fade" id="disableEquipmentModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <h5 class="modal-title" id="uploadModalLabel">Confirmation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to put this equipment on standby?</p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button type="button" id="bt_approve" class="button-submit button-left" (click)="disableEquipment()">
            <span class="ps-5">Yes</span>
            <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
          </button>
          <button type="button" id="bt_reject" class="button-back button-left" (click)="closeDisableEquipmentModal()">
            <span class="ps-5">No</span>
            <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Disable section Modal -->
<div class="modal fade" id="disableSectionModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <h5 class="modal-title" id="uploadModalLabel">Confirmation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p> Do You want to disable the section </p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button type="button" id="bt_approve" class="button-submit button-left" (click)="disableSection()">
            <span class="ps-5">Yes</span>
            <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
          </button>
          <button type="button" id="bt_reject" class="button-back button-left" (click)="closeModal()">
            <span class="ps-5">No</span>
            <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- enable section Modal -->
<div class="modal fade" id="enableSectionModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <h5 class="modal-title" id="uploadModalLabel">Confirmation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p> Do You want to enable the section </p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button type="button" id="bt_approve" class="button-submit button-left" (click)="enableSection()">
            <span class="ps-5">Yes</span>
            <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
          </button>
          <button type="button" id="bt_reject" class="button-back button-left" (click)="closeModal()">
            <span class="ps-5">No</span>
            <img alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<div style="height: 35px"></div>