import { Component } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { Router } from '@angular/router';
import { SecurityService } from '../../services/security-module/security.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';

@Component({
  selector: 'app-security-dashboard',
  templateUrl: './security-dashboard.component.html',
  styleUrl: './security-dashboard.component.scss'
})
export class SecurityDashboardComponent {
  dataArray: any = [
    { title: 'Number Of Registered Sites / Plants', count: 0, primaryColor: "#48459E", secondaryColor: "#5E5BA2" },
    { title: 'Total Count Of Advisories', count: 0, primaryColor: "#2E6FB4", secondaryColor: "#3B80C8" },
    { title: 'Total Active Incident Report', count: 0, primaryColor: "#45497B", secondaryColor: "#575B86" },
  ];

  dataArray_New: any = [
    { title: 'Active Count Of Lost & Found', count: 0, primaryColor: "#5B1979", secondaryColor: "#732F92" },
    { title: 'Total Open Feedback Count', count: 0, primaryColor: "#1F5077", secondaryColor: "#30628A" },
    { title: 'Total SOS Call History', count: 0, primaryColor: "#1F5077", secondaryColor: "#30628A" }
  ];

  dataArray2: any[] = [
    { title: 'Security Management', url: '/home/<USER>/security-management' },
    { title: 'Security Advisories', url: '/home/<USER>/advisory-management' },
    { title: 'Incident Report', url: '/home/<USER>/incident-report' }

  ]

  dataArray2_New: any[] = [
    { title: 'Lost & Found', url: '/home/<USER>/lost-&-found' },
    { title: 'Feedback', url: '/home/<USER>/feedback-management' },
    { title: 'SOS Call History', url: '/home/<USER>/sos-call-history' }

  ]
  constructor(private breadcrumb: BreadcrumbService, private router: Router, private SecurityService: SecurityService) {
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
  }
  ngOnInit() {
    this.getSecurityData();
    this.getAdvisoryData();
    this.getSecurityData()
    this.getLostAndFoundData();
    this.getFeedbackCount()
    this.getSosCallHistory();
  }

  getSecurityData(data?: any) {
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: []
      }
    }

    const param = createAxiosConfig(data);
    this.SecurityService.getSecurityData(param)
      .then((res: any) => {
        if (res && res.data) {
          this.dataArray[0] = { title: 'Number Of Registered Sites / Plants', count: res.total, primaryColor: "#48459E", secondaryColor: "#5E5BA2" }

        } else {
          // this.toast.showErrorToast('Unexpected response structure.', 6000);
          // this.clickLoading = false;
        }

      })
      .catch((error: any) => {
        // this.clickLoading = false;
        const errorMessage = error?.message || 'An unknown error occurred while loading data.';

      });

  }

  getAdvisoryData(page?: number) {
    const data = {
      page: page ? page : 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true',
      ]
    };
    const param = createAxiosConfig(data);
    this.SecurityService.getAdvisoryData(param).then(response => {
      if (response && response.data) {
        this.dataArray[1] = { title: 'Total Count Of Advisories', count: response.total, primaryColor: "#2E6FB4", secondaryColor: "#3B80C8" }
      } else {
        // this.toast.showErrorToast('Unexpected response structure.', 6000);
        // this.clickLoading = false;
      }
    }).catch((error: any) => {
      // this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }

  getLostAndFoundData(page?: number) {
    const data = {
      page: page ? page : 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.SecurityService.getLostAndFoundData(param).then(response => {
      if (response && response.data) {
        this.dataArray_New[0] = { title: 'Active Count Of Lost & Found', count: response.total, primaryColor: "#5B1979", secondaryColor: "#732F92" }
      } else {
        // this.toast.showErrorToast('Unexpected response structure.', 6000);
        // this.clickLoading = false;
      }
    }).catch((error: any) => {
      // this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }

  getFeedbackCount(page?: number) {
    const data = {
      page: page ? page : 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.SecurityService.getFeedBack(param).then(response => {
      if (response && response.data) {
        this.dataArray_New[1] = { title: 'Total Open Feedback Count', count: response.total, primaryColor: "#1F5077", secondaryColor: "#30628A" }
      } else {
        // this.toast.showErrorToast('Unexpected response structure.', 6000);
        // this.clickLoading = false;
      }
    }).catch((error: any) => {
      // this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }

  getSecurityCount(page?: number) {
    const data = {
      page: page ? page : 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.SecurityService.getSecurityCount(param).then(response => {
      if (response && response.data) {
        this.dataArray[2] = { title: 'Total Active Incident Report', count: response.total, primaryColor: "#45497B", secondaryColor: "#575B86" }
      } else {
        // this.toast.showErrorToast('Unexpected response structure.', 6000);
        // this.clickLoading = false;
      }
    }).catch((error: any) => {
      // this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }

  private getSosCallHistory(data?: any) {
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: [
          'enabled||eq||true'
        ]
      }
    }
    const param = createAxiosConfig(data);
    this.SecurityService.getSOSCallHistory(param)
      .then(res => {
        if (res && res.data) {
          this.dataArray_New[2] = { title: 'Total SOS Call History', count: res.total, primaryColor: "#45497B", secondaryColor: "#575B86" }
        } else {
          // this.toast.showErrorToast('Unexpected response structure.', 6000);
          // this.clickLoading = false;
        }
      })
      .catch(error => console.error("Error fetching SOS call history:", error));
  }

  redirectTo(url: string) {
    console.log(url)
    sessionStorage.setItem('activeRoute', url);
    this.router.navigate([url])
  }
}


