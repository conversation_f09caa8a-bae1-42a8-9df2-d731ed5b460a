<div *ngIf="chartInitializedStatus" class="main">
    <div class="titleGraphStyle">Call History</div>
    <select [(ngModel)]="selectedPeriod" (change)="onClusterChange($event)" class="period-select" [@blink]="blinkState">
        <option class="optionStyle" *ngFor="let type of clusters" [value]="type.id">{{ type.title }}</option>
    </select>

</div>
<div id="chart" *ngIf="chartInitializedStatus">
    <apx-chart #lineChart [series]="lineChartOptions.series" [chart]="lineChartOptions.chart"
        [xaxis]="lineChartOptions.xaxis" [colors]="lineChartOptions.colors">
    </apx-chart>
</div>
<div class="loading-container" *ngIf="!chartInitializedStatus">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>