import { Component, ViewChild } from '@angular/core';
import { SecurityService } from '../../../services/security-module/security.service';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexTitleSubtitle,
  ChartComponent,
  ApexMarkers
} from 'ng-apexcharts';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { ClusterService } from '../../../services/cluster/cluster.service';
import { animate, state, style, transition, trigger } from '@angular/animations';

export type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  title?: ApexTitleSubtitle;
  colors?: any;
  markers?: ApexMarkers;
};

@Component({
  selector: 'app-call-history-graph',
  templateUrl: './call-history-graph.component.html',
  styleUrls: ['./call-history-graph.component.scss'],
  animations: [
    trigger('blink', [
      state('active', style({
        borderColor: '#000000',
        border: '1px solid',
        boxShadow: '0 0 10px #000000'
      })),
      state('inactive', style({
        borderColor: 'transparent',
        boxShadow: '0px',
        border: '0.5px solid',
      })),
      transition('active <=> inactive', [
        animate('5s ease-in-out')

      ])
    ])
  ]
})
export class CallHistoryGraphComponent {
  @ViewChild('lineChart') lineChart!: ChartComponent;
  @ViewChild('dotsChart') dotsChart!: ChartComponent;
  chartInitializedStatus: boolean = false;
  public lineChartOptions!: Partial<ChartOptions>;
  public dotsChartOptions!: Partial<ChartOptions>;
  clusters: any[] = [];
  public selectedPeriod: number = 2;
  blinkState: string = 'active';
  selectedClusterId: string = '';
  selectedClusterTitle: string = '';
  selectedClusterID: number = 0;

  constructor(private securityService: SecurityService,
    private clusterService: ClusterService
  ) {

  }

  ngOnInit() {
    setTimeout(() => {
      this.blinkState = 'inactive';
    }, 3000);

    this.initializeData();
  }

  async initializeData(): Promise<void> {
    try {
      await this.getClusters();
      this.getPlantWiseData(this.selectedClusterID || 2);
    } catch (error) {
      console.error('Initialization error:', error);
    }
  }

  async getClusters(): Promise<void> {
    try {
      this.clusters = await this.clusterService.getClusters();
    } catch (error) {
      console.error('Failed to load clusters:', error);
      throw error;
    }
  }

  async getPlantWiseData(id: number): Promise<void> {
    if (!id || id <= 0) {
      console.error('Invalid cluster ID:', id);
      return;
    }

    try {
      const params = createAxiosConfig({ clusterId: id });
      const res = await this.securityService.plantwiseGraph(params);

      if (!res?.callHistoryGraph) {
        throw new Error('Invalid response format: missing callHistoryGraph');
      }

      this.processChartData(res.callHistoryGraph);
      this.chartInitializedStatus = true;
    } catch (error) {
      console.error('Plant-wise data error:', error);
      this.chartInitializedStatus = false;
    }
  }

  private processChartData(callHistoryData: any[]): void {
    const callHistoryCount = callHistoryData.map(item => item.incident_count);
    const callHistoryPlantName = callHistoryData.map(item => item.plant_name);

    this.lineChartOptions = {
      series: [{ name: 'Count', data: callHistoryCount }],
      chart: {
        type: 'line',
        height: 244,
        width: 620,
        toolbar: { show: false },
        events: { mouseMove: (event, chartContext, config) => this.syncCharts(config.dataPointIndex) }
      },
      xaxis: { categories: callHistoryPlantName },
      colors: ['#0000FF']
    };
  }

  onClusterChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const selectedClusterId = selectElement.value;

    const selectedOption = this.clusters.find(cluster => cluster.id == selectedClusterId);
    if (!selectedOption) {
      console.error('Selected cluster not found:', selectedClusterId);
      return;
    }

    this.selectedClusterId = selectedClusterId;
    this.selectedClusterTitle = selectedOption.title;
    this.selectedClusterID = selectedOption.id;

    this.getPlantWiseData(this.selectedClusterID);
  }

  syncCharts(dataPointIndex?: number): void {
    if (dataPointIndex !== undefined) {
      const lineChartEl = this.lineChart?.chart;
      const dotsChartEl = this.dotsChart?.chart;
    }
  }
}
