import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BreadcrumbService } from '../../../common/breadcrumb/breadcrumb.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PlantService } from '../../../services/plant/plant.service';
import { MatTableDataSource } from '@angular/material/table';
import { SecurityService } from '../../../services/security-module/security.service';
import { ToastMessageComponent } from '../../../common/toast-message/toast-message.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UploadService } from '../../../services/shared/upload.service';
import { FormService } from '../../../common/Services/form-service.service';

@Component({
  selector: 'app-incident-report',
  templateUrl: './incident-report.component.html',
  styleUrl: './incident-report.component.scss'
})
export class IncidentReportComponent {
  plants: any[] = []
  clickLoading: boolean = false;
  pageIndex: number = 0;
  pageSize: number = 5;
  clusters: any[] = []
  data: any[] = [];
  incidentReportData: any[] = []
  isFormOpen: boolean = false;
  charCount: number = 0;
  form!: FormGroup
  isDragging = false;
  pdfCount: number = 0;
  imageCount: number = 0;
  files: File[] = [];
  invalidFiles: any[] = [];
  errorMessage: string = '';
  images: any[] = [];
  isViewImageOpen: boolean = false;
  isViewPdfOpen: boolean = false;
  isEdit: boolean = false;
  isIncidentAddItemOpen: boolean = false
  addedItemId: number = 0
  pdfUrl: string = ''
  selectedItem: any;
  allowedFileTypes = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'];
  dataSource = new MatTableDataSource();
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  incidentType = [
    {
      id: 0, name: 'LEVEL1'
    },
    {
      id: 1, name: 'LEVEL2'
    },
    {
      id: 3, name: 'LEVEL3'
    }
  ]

  tableColumns = [
    { key: 'id', header: 'ID', width: '100px' },
    { key: 'date', header: 'Date', width: '200px' },
    { key: 'plant', header: 'Plant' },
    { key: 'incidentType', header: 'Incident Type' },
    { key: 'incidentDetail', header: 'Incident Detail', width: '500px' },
    { key: 'incidentaction', header: 'Action' }];

  IncidentSearchInputs = [
    { key: 'id', type: 'text', label: 'ID' },
    { key: 'plantName', type: 'dropdown', label: 'Plant/Site', options: this.plants, valueField: 'id', titleField: 'name' },
    { key: 'incidenttype', type: 'dropdown', label: 'Incident Type', options: this.incidentType, valueField: 'id', titleField: 'name' },
  ];

  constructor(
    private router: Router,
    private uploadService: UploadService,
    private formService: FormService,
    private breadcrumb: BreadcrumbService,
    private SecurityService: SecurityService,
    private plantService: PlantService,
    private fb: FormBuilder) {

  }

  ngOnInit() {
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
    this.getAllPlant();
    this.getIncidentReport();

    this.form = this.fb.group({
      plantId: ['', Validators.required],
      date: [''],
      incidentType: ['', Validators.required],
      incidentDetail: ['', Validators.required],
      isAttachment: [''],
    });
  }

  getAllPlant(): Promise<void> {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]
    }
    const param = createAxiosConfig(data);
    return this.plantService.getAllPlants(param).then((response: any) => {
      this.plants = [...response.data, this.plants]
      this.IncidentSearchInputs = [
        { key: 'id', type: 'text', label: 'ID' },
        { key: 'plantName', type: 'dropdown', label: 'Plant/Site', options: this.plants, valueField: 'id', titleField: 'name' },
        { key: 'incidenttype', type: 'dropdown', label: 'Incident Type', options: this.incidentType, valueField: 'id', titleField: 'name' },
      ];
    })
  }

  searchFilter(event: any) {
    console.log('event', event);
    const searchFilter = event;
    let data = {
      page: 1,
      limit: 10000,
      sort: 'id,DESC',
      filter: [] as string[]
    };

    if (searchFilter.plantName) {
      data.filter.push(`plantId||eq||${searchFilter.plantName}`);
    }
    if (searchFilter.incidenttype) {
      data.filter.push(`incidentType||eq||${searchFilter.incidenttype}`);
    }
    if (searchFilter.id) {
      data.filter.push(`id||eq||${searchFilter.id}`);
    }
    this.getIncidentReport(data)
  }

  getIncidentReport(data?: any) {
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: [
          'enabled||eq||true'
        ]
      }
    }
    const param = createAxiosConfig(data);
    this.clickLoading = true;
    this.SecurityService.getIncidentReport(param).then(response => {
      if (response && response.data) {
        this.incidentReportData = this.transformData(response.data);
        this.dataSource.data = this.incidentReportData;
        this.clickLoading = false
      }
      else if (response.length > 0) {
        this.incidentReportData = this.transformData(response);
        this.dataSource.data = this.incidentReportData;
        this.clickLoading = false

      } else {
        this.toast.showErrorToast('An unknown error occurred while loading data', 6000);
        this.clickLoading = false;
      }
    }).catch((error: any) => {
      this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }

  transformData(apiResponse: any[]): any[] {
    return apiResponse.map(item => ({
      id: item?.id,
      date: item?.date,
      plant: item?.plant?.name,
      incidentType: item?.incidentType,
      incidentDetail: item?.incidentDetail,
      plantId: item?.plantId
    }));
  }

  async addItem() {
    this.formService.trimFormValues(this.form);
    if (this.form.invalid || this.invalidFiles.length > 0) {
      this.form.markAllAsTouched();
      return
    }
    const imagesURL = [];
    let pdfURL = ''
    if (this.files.length > 0) {
      for (const file of this.files) {
        this.clickLoading = true;
        const uploadedFileURL = await this.uploadProfilePic(file);

        if (file.type === 'application/pdf') {
          pdfURL = uploadedFileURL;
        } else {
          imagesURL.push(uploadedFileURL);
        }
        this.clickLoading = false;
      }
    }

    let formdata = this.form.value;
    let json = {
      'adminId': 4584,
      'plantId': formdata.plantId,
      'date': formdata.date,
      'incidentDetail': formdata.incidentDetail,
      'incidentType': formdata.incidentType,
      'image': imagesURL,
      'pdf': pdfURL
    }
    this.clickLoading = true;
    if (this.isEdit !== true) {
      this.SecurityService.addIncident(json).then((res: any) => {
        this.clickLoading = false;
        if (res.responseCode == 400) {
          this.toast.showErrorToast(res.message)
        }
        if (res.responseCode == 200) {
          console.log("ADDED");
          this.isFormOpen = false;
          this.addedItemId = res.data.id;

          this.files = [];
          this.invalidFiles = [];
          this.isEdit = false;
          this.getIncidentReport();
        }
      })
    }
    else {
      let json = {
        id: this.selectedItem.id,
        tableName: 'security-incident',
        data: {
          'adminId': 4584,
          'plantId': formdata.plantId,
          'date': formdata.date,
          'incidentDetail': formdata.incidentDetail,
          'incidentType': formdata.incidentType,
        }
      };
      this.updateLostAndFound(json);
    }
  }

  updateLostAndFound(json: any) {
    this.clickLoading = true;
    this.SecurityService.updateIncidentReport(json).then((res: any) => {
      this.clickLoading = false;
      if (res.responseCode == 400) {
        this.toast.showErrorToast(res.message)
      }
      if (res.responseCode == 200) {
        this.toast.showSuccessToast('Item Is Mark As Found');
        this.isFormOpen = false;

        this.isEdit = false;
        this.resetForm();
        this.getIncidentReport()
      }
    })
  }

  async uploadProfilePic(file: any): Promise<string> {
    let url = "";
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        url = await this.uploadService.uploadImage(formData);
      } catch (error) {
        console.error("Error uploading image:", error);
        return ''
      }
    }
    return url;
  }


  updateCharCount(input: string) {
    this.charCount = this.form.get(input)?.value.trim().length || 0;
  }

  approve(item: any) {
    this.selectedItem = item;
    this.isFormOpen = true;
    this.isEdit = true;
    this.form.patchValue({
      plantId: item.plantId,
      date: item.date,
      incidentType: item.incidentType,
      incidentDetail: item.incidentDetail,
      desc: item.desc
    });
  }

  delete(item: any) {
    this.selectedItem = item;
    let json = {
      id: this.selectedItem.id,
      tableName: 'security-incident',
      data: {
        enabled: false
      }
    };
    this.SecurityService.deleteIncidentReport(json).then((res: any) => {
      this.clickLoading = false;
      if (res.responseCode == 400) {
        this.toast.showErrorToast(res.message)
      }
      if (res.responseCode == 200) {
        this.toast.showSuccessToast('Incident Item deleted');
        this.isFormOpen = false;
        this.isEdit = false;
        this.resetForm();
        this.getIncidentReport()
      }
    })
  }

  openAdd() {
    this.isFormOpen = true;
    this.isEdit = false;
    this.form.reset();
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;
    this.handleFiles(event.dataTransfer?.files);
  }



  handleFiles(fileList: any) {
    if (!fileList) return;
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const fileType = file.type;

      if (fileType === 'application/pdf') {
        if (this.pdfCount >= 1) {
          this.invalidFiles.push({ name: file.name, error: 'Only 1 PDF is allowed!' });
        } else {
          this.files.push(file);
          this.pdfCount++;
        }
      } else if (this.allowedFileTypes.includes(fileType) && fileType !== 'application/pdf') {
        if (this.imageCount >= 2) {
          this.invalidFiles.push({ name: file.name, error: 'Only 2 images are allowed!' });
        } else {
          this.files.push(file);
          this.imageCount++;
        }
      } else {
        this.invalidFiles.push({ name: file.name, error: 'This document is not supported. Please select another file' });
      }
    }
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    this.handleFiles(input.files);
  }

  removeInvalidFile(index: number) {
    this.invalidFiles.splice(index, 1);
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragging = true;
  }

  onDragLeave() {
    this.isDragging = false;
  }

  removeFile(index: number) {
    const removedFile = this.files[index];
    if (removedFile.type === 'application/pdf') {
      this.pdfCount--;
    } else {
      this.imageCount--;
    }
    this.files.splice(index, 1);
  }


  validateFiles(files: FileList): File[] {
    const validFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      if (this.allowedFileTypes.includes(files[i].type)) {
        validFiles.push(files[i]);
      } else {
        this.errorMessage = 'Invalid file type! Only PNG, JPG, JPEG, and PDF files are allowed.';
      }
    }
    return validFiles;
  }

  addFiles(fileList: any[]) {
    for (let i = 0; i < fileList.length; i++) {
      this.files.push(fileList[i]);
    }
  }

  openViewImageDialog(selectedItem?: any) {
    this.isViewImageOpen = true;
    this.isViewPdfOpen = false;
    this.openLostAndFoundModal()

    this.images = selectedItem?.image;
    this.pdfUrl = selectedItem?.pdf
  }
  openViewPdfDialog() {
    this.isViewPdfOpen = true;
    this.isViewImageOpen = false;
    this.openLostAndFoundModal()
  }

  openLostAndFoundModal() {
    this.openModal2('lostAndFoundModal')
  }
  openModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'block';
      modal?.classList.add('show');
      modal?.setAttribute('aria-hidden', 'false');
      modal?.setAttribute('aria-modal', 'true');
      modal?.setAttribute('role', 'dialog');
    }
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    document.body.appendChild(backdrop);

    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = '0px';
  }

  resetForm() {
    this.charCount = 0
    if (this.isEdit) {
      this.form.get('remark')?.reset()
    }
    else {
      this.form.reset()
      this.form.get('plantId')?.setValue('')
      this.files = [];
      this.invalidFiles = []
    }
  }

  onClickCross() {
    this.isFormOpen = false;
    this.resetForm()
    this.isEdit = false;
  }
}

