<div>
    <app-toast-message></app-toast-message>
    <div *ngIf="clickLoading" class="loading-overlay">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">Please wait...</div>
        </div>
    </div>

    <div class="d-flex align-items-center justify-content-between">
        <div class="col-md-4">
            <p class="fw-bold mb-0">Incident Report</p>
        </div>

        <div class="d-flex">
            <button class="button-submit button-right d-flex align-items-center" style="width: 120px !important;"
                (click)="openAdd()">
                <img src="../../../assets/svg/add.svg" class="right-arrow" alt="" />
                <span style="color: white;">ADD</span>
            </button>
        </div>
    </div>

    <app-ad-table [data]="incidentReportData" [isSearchShow]="true" [filters]="IncidentSearchInputs"
        (search)="searchFilter($event)" [isTableFilter]="true" (approve)="approve($event)" (delete)="delete($event)"
        [isTableTitle]="false" [columns]="tableColumns"></app-ad-table>
    <app-custom-modal [title]="'Add Incident'" [width]="'50%'" *ngIf="isFormOpen" (onClickCross)="isFormOpen = false">
        <div class="mb-0">
            <form [formGroup]="form">
                <div class="row d-flex m-2">

                    <div class="col-md-6 ">
                        <label class="ml-12">Plant / Site<span class="text-danger"> * </span></label>
                        <select class="form-control" formControlName="plantId" id="plantId">
                            <option value="">Select Plant / Site</option>
                            <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
                        </select>
                        <span class="text-danger ml-12"
                            *ngIf="(form.get('plantId')?.touched|| form.get('plantId')?.dirty) && form.get('plantId')?.errors?.['required']">
                            Site / Plant is required.
                        </span>
                    </div>

                    <div class="col-md-6">
                        <label class="fw-bold ml-6">Date<span class="text-danger"> * </span></label>
                        <input class="form-control" [disabled]="true" placeholder="DD-MM-YYYY" type="date"
                            class="form-control" formControlName="date" id="date" />
                        <span class="text-danger ml-12"
                            *ngIf="(form.get('date')?.touched|| form.get('date')?.dirty) && form.get('date')?.errors?.['required']">
                            Date is required.
                        </span>
                    </div>

                    <div class="col-md-12 ">
                        <label class="ml-12">Incident Type<span class="text-danger"> * </span></label>
                        <select class="form-control" formControlName="incidentType" id="incidentType">
                            <option value="">Select Incident Type</option>
                            <option *ngFor="let type of incidentType" [value]="type.name">{{ type.name }}</option>
                        </select>
                        <span class="text-danger ml-12"
                            *ngIf="(form.get('incidentType')?.touched|| form.get('incidentType')?.dirty) && form.get('incidentType')?.errors?.['required']">
                            Incident type is required.
                        </span>
                    </div>

                    <div class="col-md-12">
                        <label class="ml-12">
                            Incident Details<span class="text-danger"> * </span>
                        </label>

                        <textarea rows="4" placeholder="Enter here..." class="form-control" id="incidentDetail"
                            formControlName="incidentDetail" (input)="updateCharCount('incidentDetail')"
                            maxlength="500">
                        </textarea>

                        <!-- Validation Messages -->
                        <span class="text-danger ml-12"
                            *ngIf="form.get('incidentDetail')?.dirty || form.get('incidentDetail')?.touched">
                            <span *ngIf="form.get('incidentDetail')?.errors?.['required']">Details are required.</span>
                            <span class="ps-1" *ngIf="charCount < 30">Minimum 30 characters required.</span>
                            <span class="ps-1" *ngIf="charCount >500">Maximum 500 characters allowed.</span>
                        </span>

                        <!-- Character Count Display -->
                        <p class="text-muted mt-1" style="font-size: small;">
                            {{ charCount }}/500 characters
                        </p>
                    </div>

                    <div class="col-md-6">
                        <label class="fw-bold mb-1 ml-12">Upload</label>
                        <div class="upload-box" [class.dragging]="isDragging" (dragover)="onDragOver($event)"
                            (dragleave)="onDragLeave()" (drop)="onDrop($event)">
                            <div>
                                <img style="width: 15%;" alt="" src="../../../assets/svg/upload-plain.svg" />
                            </div>
                            <p><b>Drag & Drop files here or <a (click)="fileInput.click()">Browse</a></b></p>
                            <p style="font-size:xx-small;">Supported formate : Png, Jpg & PDf</p>
                            <input type="file" (change)="onFileSelected($event)" multiple hidden #fileInput>
                        </div>
                        <!-- <p *ngIf="errorMessage" style="color: red; font-size: small;">{{ errorMessage }}</p> -->
                    </div>
                    <div class="col-md-6" *ngIf="(files.length > 0 ||invalidFiles.length > 0)">
                        <label class="fw-bold mb-1 ml-12">Selected Attachments</label>
                        <ul *ngIf="files.length > 0">
                            <li *ngFor="let file of files; let i = index">
                                <div class="valid">
                                    {{ file.name }}
                                    <button>
                                        <i class="bi bi-x" style="cursor: pointer; color: #0f8669;font-size: 18px;"
                                            (click)="removeFile(i)"></i>
                                    </button>
                                </div>
                            </li>
                        </ul>
                        <ul *ngIf="invalidFiles.length > 0">
                            <li *ngFor="let file of invalidFiles; let i = index">
                                <div class="error">
                                    {{ file.name }}
                                    <button (click)="removeInvalidFile(i)">
                                        <i class="bi bi-x" style="cursor: pointer; color: red; font-size: 18px;"></i>
                                    </button>
                                </div>
                                <p style="font-size: x-small;">
                                    {{ file.error }}
                                </p>
                        </ul>
                    </div>
                    <!-- {{files.length}} -->
                    <!-- <div class="col-md-6" *ngIf="files.length > 0 && isEdit">
                    <label class="fw-bold">Uploaded Image</label>
                    <button type="button" style="margin:10px 0px !important" id="bt_reject"
                        class="button-back button-left" (click)="openViewImageDialog()">
                        <span class="ps-2">View Image</span>
                        <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg"
                            class="left-arrow" />
                    </button>

                    <label class="fw-bold">Uploaded PDF</label>
                    <button type="button" style="margin:10px 0px !important" id="bt_reject"
                        class="button-back button-left" (click)="openViewPdfDialog()">
                        <span class="ps-2">View PDF</span>
                        <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg"
                            class="left-arrow" />
                    </button> -->
                </div>
                <!-- </div> -->

                <div class="d-flex mt-4" style="margin-left: 20px;">
                    <button type="submit" id="bt_approve" class="button-submit button-left" (click)="addItem()">
                        <span class="ps-2">{{'Add Item'}}</span>
                        <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
                    </button>
                    <button type="button" id="bt_reject" class="button-back button-left" (click)="resetForm()">
                        <span class="ps-2">Reset</span>
                        <img style="margin-left: -31px" alt="" src="../../../assets/svg/Edit-User.svg"
                            class="left-arrow" />
                    </button>
                </div>
            </form>


        </div>
    </app-custom-modal>

</div>