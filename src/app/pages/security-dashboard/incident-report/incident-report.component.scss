.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.button-submit {
    width: 160px;
}

.button-right .right-arrow {
    width: 40px;
    height: 40px;
}

.upload-box {
    // width: 100%;
    // max-width: 400px;
    padding: 20px;
    text-align: center;
    border: 1px dashed #4796C3;
    border-radius: 5px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    &.dragging {
        background-color: #e3f2fd;
    }

    p {
        // margin: 0;
        font-size: 14px;
        color: #555;
    }

    button {
        // margin-top: 10px;
        // padding: 12px 16px;
        background-color: #007bff;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
            background-color: #4796C3;
        }
    }
}

ul {
    list-style: none;
    padding: 0;
    // margin-top: 15px;
  
    li {
      div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 3px;
        border-radius: 5px;
        margin-top: 2px;
      }
  
      .valid {
        border: 1px solid #6ACD75;
        color: #6ACD75;
      }
  
      .error {
        border: 1px solid #E95454;
        color: #E95454
      }
  
      font-size: xx-small;
  
      button {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 16px;
      }
  
      p {
        color: #E95454;
        margin-top: 1px;
        margin-bottom: 0px !important;
      }
    }
  }