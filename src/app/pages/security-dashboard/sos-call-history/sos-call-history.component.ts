import { AfterViewInit, Component, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SecurityService } from "../../../services/security-module/security.service";
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import * as XLSX from 'xlsx';
import { formatDate } from '@angular/common';
import { PlantService } from '../../../services/plant/plant.service';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { ToastMessageComponent } from '../../../common/toast-message/toast-message.component';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-sos-call-history',
  templateUrl: './sos-call-history.component.html',
  styleUrls: ['./sos-call-history.component.scss'],
})
export class SosCallHistoryComponent implements AfterViewInit {
  displayedColumns: string[] = ['no', 'username', 'userId', 'profileNumber', 'toMobileNumber', 'plantname', 'dateTime', 'geoCoordinates', 'action'];
  clickLoading: boolean = false;
  tableColumns = [
    { key: 'no', header: 'No.', width: '100px' },
    { key: 'username', header: 'User Name', width: '200px' },
    { key: 'userId', header: 'User ID' },
    { key: 'profileNumber', header: 'Profile Number' },
    { key: 'toMobileNumber', header: 'GSOC/Plant Control Room', width: '500px' },
    { key: 'plantname', header: 'Plant Name' },
    { key: 'dateTime', header: 'Date Time' },
    { key: 'action', header: 'Action' }
  ];

  columnForm!: FormGroup;
  dataSource = new MatTableDataSource();
  private plants: any[] = [];
  selectedItem: any;
  settingModal = false;
  filterModal = false;
  mapUrl: SafeUrl | null = null
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private securityService: SecurityService,
    private fb: FormBuilder,
    private plantService: PlantService
  ) {
    this.initializeForm();
    this.getAllPlant();

  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  private initializeForm() {
    this.columnForm = this.fb.group({
      settingControlArray: this.fb.array(this.tableColumns.map(() => this.fb.control(true)))
    });
  }

  getAllPlant(): Promise<void> {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]
    }
    this.clickLoading = true;
    const param = createAxiosConfig(data);
    return this.plantService.getAllPlants(param).then((response: any) => {
      this.plants = response.data
      this.getSosCallHistory()

      // this.IncidentSearchInputs = [
      //   { key: 'id', type: 'text', label: 'ID' },
      //   { key: 'date', type: 'date', label: 'Date', options: [] },
      //   { key: 'plantName', type: 'dropdown', label: 'Plant/Site', options: this.plants, valueField: 'id', titleField: 'name' },
      //   { key: 'incidenttype', type: 'dropdown', label: 'Incident Type', options: this.incidentType, valueField: 'id', titleField: 'name' },
      // ];
    })
  }

  private getSosCallHistory(data?: any) {
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: [
          'enabled||eq||true'
        ]
      }
    }
    const param = createAxiosConfig(data);

    this.securityService.getSOSCallHistory(param)
      .then(res => {
        if (res) {
          this.dataSource.data = this.mapPlantNames(res?.data);
          this.clickLoading = false;
        }
      })
      .catch(error => console.error("Error fetching SOS call history:", error));
  }

  private mapPlantNames(data: any[]): any[] {
    return data.map(item => ({
      ...item,
      plantName: this.getPlantName(item.plantId),
      dateTime: this.convertDate(item.dateTime)
    }));
  }

  private getPlantName(plantId: number): string {
    const plant = this.plants.find(p => p.id === plantId);
    return plant ? plant.name : 'Unknown';
  }

  private convertDate(dateString: string): string {
    const date = new Date(dateString);
    return formatDate(date, 'dd-MM-yyyy HH:mm:ss', 'en-US');
  }

  handleSearch(event: any) {
    const { fromDate, toDate, plantIds, toPhoneNumber } = event;
    const filteredData = createAxiosConfig({
      filter: [
        `plantId||$in||${plantIds}`,
        `createdTimestamp||$gte||{${this.formatToISO(new Date(fromDate))}}`,
        `createdTimestamp||$lte||{${this.formatToISO(new Date(toDate))}}`,
        `toPhoneNumber||$in||${toPhoneNumber}`
      ]
    });
    this.fetchFilteredSOSCallHistory(filteredData);
  }

  resetTable() {
    this.getSosCallHistory();
  }

  private formatToISO(date: Date): string {
    const now = new Date();
    const utcDate = new Date(Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      now.getHours(),
      now.getMinutes(),
      now.getSeconds(),
      now.getMilliseconds()
    ));
    return utcDate.toISOString();
  }


  private fetchFilteredSOSCallHistory(param: any) {
    this.securityService.getSOSCallHistory(param)
      .then(res => {
        if (res) {
          this.dataSource.data = this.mapPlantNames(res);
        }
      })
      .catch(error => console.error("Error fetching filtered SOS call history:", error));
  }

  exportToExcel() {
    const worksheet = XLSX.utils.json_to_sheet(this.dataSource.data);
    const workbook = XLSX.utils.book_new();

    XLSX.utils.book_append_sheet(workbook, worksheet, 'SOS Call History');

    XLSX.writeFile(workbook, 'SOS_Call_History.xlsx');
  }

  openTableSettingModal() {
    this.settingModal = true;
  }

  getSettingControlArray(): FormArray {
    return this.columnForm.get('settingControlArray') as FormArray;
  }

  resetSetting() {
    this.columnForm.reset();
  }

  closeSettingModal() {
    this.settingModal = false;
  }

  onClickCross() {
    this.settingModal = false;
    this.filterModal = false;
  }

  openTableFilterModal() {
    this.filterModal = true;
  }

  closeFilterModal() {
    this.filterModal = false;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.dataSource.filter = filterValue;
  }

  applySetting() {
    const selectedColumns = this.getCheckedColumns();

    if (selectedColumns.length > 0) {
      this.displayedColumns = selectedColumns.map(col => col.key);
      this.onClickCross();
    }
  }

  softDeleteSosHistory(id: number) {
    this.selectedItem = id;
    let json = {
      id: this.selectedItem,
      tableName: 'callhistory',
      data: {
        enabled: false
      }
    };
    this.clickLoading = true;
    this.securityService.deleteCallHistory(json).then((res: any) => {
      this.clickLoading = false;
      if (res.responseCode == 400) {
        this.toast.showErrorToast(res.message)
      }
      if (res.status == 200) {
        this.toast.showSuccessToast('Call history Item deleted');
        this.getSosCallHistory()
        this.clickLoading = false;
      }
    })
  }

  private getCheckedColumns(): { key: string; header: string }[] {
    return this.getSettingControlArray().value
      .map((checked: boolean, index: number) => (checked ? this.tableColumns[index] : null))
      .filter((column: any): column is { key: string; header: string } => column !== null);
  }

  setMapUrl(lat: string, long: string) {
    // const mapUrl = `https://www.google.com/maps?q=${lat},${long}&output=embed`;
    const mapUrl = `https://www.google.com/maps?q=${lat},${long}&hl=en&z=14&output=embed`
    // return this.sanitizer.bypassSecurityTrustResourceUrl(mapUrl);
    window.open(mapUrl, '_blank');
  }
}
