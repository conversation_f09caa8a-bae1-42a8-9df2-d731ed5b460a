<div>
    <app-toast-message></app-toast-message>
    <div *ngIf="clickLoading" class="loading-overlay">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">Please wait...</div>
        </div>
    </div>
    <p class="fw-bold">SOS Call History</p>
    <div class="outer-container">
        <div class="parent-container">
            <app-sos-search-filter (search)="handleSearch($event)" (reset)="resetTable()"></app-sos-search-filter>
            <div class="mat-elevation-z8">
                <div class="col-sm-12">
                    <div class="user-action no-data">
                        <input placeholder="Search here..." type="text" (input)="applyFilter($event)"
                            class=" input-text-ty1 icon-border d-inline-block search-icon" id="searchinput">

                        <div class="fliter icon-border">
                            <img src="../../../assets/img/icons/filter.png" class="" alt=""
                                (click)="openTableSettingModal()" data-bs-toggle="modal" data-bs-target="#filterModal">
                        </div>

                        <div class="dropdown excel icon-border">

                            <img src=" ../../../assets/img/icons/excel.png">

                            <div class="dropdown-content">
                                <a href="javascript:void(0);" (click)="exportToExcel()">Excel</a>
                            </div>
                        </div>
                    </div>
                </div>
                <table style="width: 100% !important;" class="mat-elevation-z8 " mat-table [dataSource]="dataSource">

                    <ng-container matColumnDef="no">
                        <th mat-header-cell *matHeaderCellDef> No. </th>
                        <td mat-cell *matCellDef="let element; let i = index"> {{ dataSource.data.indexOf(element) + 1
                            }}
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="username">
                        <th mat-header-cell *matHeaderCellDef> User Name </th>
                        <td mat-cell *matCellDef="let element"> {{element.name}} </td>
                    </ng-container>

                    <ng-container matColumnDef="userId">
                        <th mat-header-cell *matHeaderCellDef> User Id </th>
                        <td mat-cell *matCellDef="let element"> {{element?.adminId}} </td>
                    </ng-container>

                    <ng-container matColumnDef="profileNumber">
                        <th mat-header-cell *matHeaderCellDef> Profile Mobile No. </th>
                        <td mat-cell *matCellDef="let element"> {{element?.admin?.contactNumber}} </td>
                    </ng-container>

                    <ng-container matColumnDef="toMobileNumber">
                        <th mat-header-cell *matHeaderCellDef> GSOC/Plant Control Room </th>
                        <td mat-cell *matCellDef="let element"> {{ element.toPhoneNumber === '9099065100' ? '9099065100
                            - GSOC' : element.toPhoneNumber }} </td>
                    </ng-container>

                    <ng-container matColumnDef="plantname">
                        <th mat-header-cell *matHeaderCellDef> Plant Name </th>
                        <td mat-cell *matCellDef="let element"> {{element.plantName}} </td>
                    </ng-container>

                    <ng-container matColumnDef="dateTime">
                        <th mat-header-cell *matHeaderCellDef> Date Time </th>
                        <td mat-cell *matCellDef="let element"> {{element.dateTime}} </td>
                    </ng-container>

                    <ng-container matColumnDef="geoCoordinates">
                        <th mat-header-cell *matHeaderCellDef> GEO Coordinates </th>
                        <td mat-cell *matCellDef="let element">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <!-- Check if lat is available -->
                                <span matTooltip="{{element.lat}}" *ngIf="element.lat; else dashLat">
                                    {{ element.lat | number: '1.4-4' }}
                                </span>
                                <ng-template #dashLat>-</ng-template>

                                <!-- Check if long is available -->
                                <span matTooltip="{{element.long}}" *ngIf="element.long; else dashLong">
                                    {{ element.long | number: '1.4-4' }}
                                </span>
                                <ng-template #dashLong>-</ng-template>

                                <!-- Map icon -->
                                <img style="height: 25px; width: 25px; cursor: pointer;" class="destination-img"
                                    matTooltip="View Map" [matTooltipPosition]="'above'"
                                    (click)="setMapUrl(element.lat, element.long)" alt=""
                                    src="../../../assets/img/destination.png" class="right-arrow" />
                            </div>
                        </td>

                        <!-- Map preview -->
                        <div *ngIf="mapUrl" class="map-preview">
                            <iframe width="400" height="300" [src]="mapUrl" frameborder="0" allowfullscreen></iframe>
                        </div>
                    </ng-container>


                    <ng-container matColumnDef="action">
                        <th mat-header-cell *matHeaderCellDef> Action </th>
                        <td mat-cell *matCellDef="let element">
                            <div style="gap:10px" class="d-flex">
                                <i class="bi bi-x-circle-fill" style="cursor: pointer; color: #e23939;font-size: 18px;"
                                    (click)="softDeleteSosHistory(element.id)" matTooltip="Delete Call history"></i>
                            </div>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
                <div *ngIf="!dataSource || dataSource?.data?.length === 0" class="no-data-found">
                    <p>No data found</p>
                </div>
                <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons
                    aria-label="Select page of periodic elements">
                </mat-paginator>
                <div style="height: 50px"></div>
            </div>
        </div>
    </div>

    <app-custom-modal [title]=" 'Select Column Header'" [width]="'30%'" *ngIf="settingModal"
        (onClickCross)="onClickCross()" (closeModal)="closeSettingModal()">
        <div class="mb-0">
            <form [formGroup]="columnForm" style="text-align: justify">
                <div formArrayName="settingControlArray">
                    <div *ngFor="let control of getSettingControlArray().controls; let i = index" class="form-check">
                        <div [ngClass]="{'border-bottom': i !== getSettingControlArray().controls.length - 1}"
                            class="checkbox-container">
                            <input type="checkbox" [formControlName]="i" class="custom-checkbox"
                                id="checkbox-{{ i }}" />
                            <label class="form-check-label checkbox-label" for="checkbox-{{ i }}">
                                {{ tableColumns[i]?.header }}
                            </label>
                        </div>
                    </div>

                </div>

                <div style="margin-left: 20px;" class="d-flex mt-4">
                    <button type="submit" id="bt_approve" style="width: 130px;" class="button-submit button-left"
                        (click)="applySetting()">
                        <span class="ps-2"> Apply</span>
                        <img alt="" src="../../../assets/svg/right-pink.svg" style="margin-left: -45px"
                            class="left-arrow" />
                    </button>
                    <button type="button" id="bt_reject" class="button-back button-left" style="width: 130px;"
                        (click)="resetSetting()">
                        <span class="ps-2"><b>Reset</b></span>
                        <img style="margin-left: -35px" alt="" src="../../../assets/svg/Edit-User.svg"
                            class="left-arrow" />
                    </button>
                </div>
            </form>
        </div>

    </app-custom-modal>
</div>