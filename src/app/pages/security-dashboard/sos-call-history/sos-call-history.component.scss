$backgroundColor1: #ffffff;
$backgroundColor2: #2B67C3;

.outer-container {
    height: auto;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 10px;
    background-color: #f9f9f9;
}


img {
    height: 28px;
    width: 28px;
}

$fontColor5: #161616;
$fontColor6: #ffffff;
$fontColor10: #464646;


$backgroundColor0: transparent;
$backgroundColor1: #ffffff;
$backgroundColor2: #2B67C3;
$backgroundColor7: #555555;
$backgroundColor8: #CED4DA;

$hoverColor1: #043b8e;

$borderColor2: #CED4DA;
$borderColor3: #6B6B6E;

.table {
    margin-bottom: 0;
    border: 1px solid $backgroundColor8 !important;
}

.bg-white {
    background-color: $backgroundColor1;
}

.mat-elevation-z8 {
    margin: 16px 0;
    padding: 16px;
}

.user-action {
    margin-bottom: 10px;
    float: right;
    display: flex;
    vertical-align: middle;
    flex-direction: row-reverse;

    .excel {
        height: 21px;
        cursor: pointer;
    }

    .user-setting {
        height: 21px;
        cursor: pointer;

    }

    .fliter {
        height: 21px;
        margin-right: 0px !important;

    }

    .excel:hover {
        // border: solid 1px $hoverColor1;
        background: $backgroundColor8;
    }

    .fliter:hover {
        // border: solid 1px $hoverColor1;
        background: $backgroundColor8;
    }

    .user-setting:hover {
        // border: solid 1px $hoverColor1;
        background: $backgroundColor8;
    }

    .icon-border {
        background: $backgroundColor1;
        border: 1px solid $borderColor2;
        border-radius: 2px;
        // padding: 7px;
        margin-right: 13px;
        height: 29px;

        img {
            padding: 7px;
        }
    }

    .search-icon {
        background: url('../../../../assets/img/icons/search.png') no-repeat scroll 10px 7px;
        padding-left: 30px;
        background-size: 10px;
    }
}

.input-text-ty1 {
    border: 1px solid $borderColor3;
    border-radius: 6px !important;
    flex: none;
    order: 2;
    flex-grow: 0;
    z-index: 0;
    box-sizing: border-box;
    width: 250px;
    // height: 29px;
    font-weight: 400;
    font-size: 12px;
    color: $fontColor5
}

.input-text-ty1:focus-visible {
    outline: none;
}

.mat-table {
    border: 1px solid $backgroundColor8;
}

.pagination-content {
    // border: solid 0.5px #CED4DA;
    background-color: $backgroundColor1;
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 0;
    flex: 0.9;
}

.pagination button {
    // background-color: #007bff;
    color: #464646;
    border: none;
    padding: 10px 15px;
    margin: 0 5px;
    background-color: $backgroundColor1;
    cursor: pointer;
}

.pagination button.active {
    // background-color: #0056b3;
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
    padding: 9px;
    border-radius: 9px;
    padding: 1px 9px;
    border-radius: 9px;
    color: #fff;
}

.pagination button[disabled] {
    background-color: transparent;
    cursor: not-allowed;
}

button {
    font-family: 'adani';
    font-size: 14px;
    font-weight: 500;
}

.pg-dp-label {
    font-size: 16px;
    font-weight: 500;
    margin-right: 15px;
    margin-left: 20px;
}

.form-select {
    width: 100px !important;
}

.total {
    margin-right: 15px;
}

.total span {
    font-size: 15px;
    font-weight: 700;
    margin-left: 15px;
}

tbody tr .status {
    color: #11AF22;
}

tbody tr .link {
    cursor: pointer;
}

tbody td {
    text-align: justify;
}

.table-responsive {
    // min-height: 395px;
    height: auto;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 110px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

/* Show the dropdown menu on hover */
.dropdown:hover .dropdown-content {
    display: block;
}

/* Style the dropdown links */
.dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

/* Change color of dropdown links on hover */
.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.no-data {
    img {
        /* input */
        cursor: no-drop;
    }

    img:hover {
        color: white;
    }

    .icon-border:hover {
        background-color: white;
    }
}

thead tr th:first-child,
tbody tr td:first-child {
    padding-left: 30px !important;
}

.d-flex {
    margin-left: 8px;
}

.no-data-found {
    text-align: center;
    margin-top: 20px;
    font-size: 16px;
    color: #888;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    /* Space between checkbox and label */
    padding-bottom: 10px;
    padding-top: 10px;
    width: 100%;
}

.custom-checkbox {
    width: 20px;
    height: 20px;
}

/* Make label bold and medium font size */
.checkbox-label {
    font-weight: bold;
    font-size: 16px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}