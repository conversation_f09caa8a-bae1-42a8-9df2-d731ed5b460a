.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}


.tab-container {
  display: flex;
  justify-content: start;
  align-items: center;
  // width: 100%;
}

.tab-headers {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #0B74B0;
  border-radius: 20px;
  background-color: white;
  color: #0B74B0;
}

.tab-header {
  padding: 10px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 160px;
  transition: transform 0.3s,
}

.tab-header.active {
  background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
  color: white;
  border-radius: 20px;
  font-weight: bold;
}

label {
  font-size: small;
  font-weight: 700;
}

.button-submit {
  width: 160px;
}

.button-right .right-arrow {
  width: 40px;
  height: 40px;
}

.filter-item {
  flex: 1 1 calc(25% - 20px);
  min-width: 180px;
  box-sizing: border-box;
}

.confirmModalHeader {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
  color: #ffffff;
}

.confirmModalBody {

  // text-align: center;
  >p {
    color: #555555;
    font-weight: 500;
  }

  >.d-flex {
    justify-content: center;
    margin-top: 25px;
  }
}

.confirmModalCloseBtn {
  color: #fff !important;
}

.upload-box {
  // width: 100%;
  // max-width: 400px;
  padding: 20px;
  text-align: center;
  border: 1px dashed #4796C3;
  border-radius: 5px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;

  &.dragging {
    background-color: #e3f2fd;
  }

  p {
    // margin: 0;
    font-size: 14px;
    color: #555;
  }

  button {
    // margin-top: 10px;
    // padding: 12px 16px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background-color: #4796C3;
    }
  }
}

ul {
  list-style: none;
  padding: 0;
  // margin-top: 15px;

  li {
    div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 3px;
      border-radius: 5px;
      margin-top: 2px;
    }

    .valid {
      border: 1px solid #6ACD75;
      color: #6ACD75;
    }

    .error {
      border: 1px solid #E95454;
      color: #E95454
    }

    font-size: xx-small;

    button {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 16px;
    }

    p {
      color: #E95454;
      margin-top: 1px;
      margin-bottom: 0px !important;
    }
  }
}

.accordion-card {
  background: #ffffff;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.example-accordion {
  max-width: 700px;
  margin: auto;
  border-radius: 8px;
  overflow: hidden;

  .example-accordion-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #fff;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;

    &:hover {
      transform: scale(1.01);
    }
  }

  .accordion-card {
    padding: 10px;
    border-radius: 8px;
  }

  .example-accordion-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 0px;
    font-size: 16px;
    font-weight: bold;
    color: #333;

    transition: background 0.3s ease-in-out;



    .title {
      flex-grow: 1;
      color: #007bff;
    }

    .icon-toggle {
      background: none;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      // width: 30px;
      // height: 30px;
      border-radius: 50%;
      transition: background 0.3s ease-in-out;

      &:hover {
        // background: #007bff;
        color: #fff;
      }

      img {
        font-size: 14px;
        height: 30px;
      }
    }
  }

  .example-accordion-item-body {
    display: flex;
    flex-direction: column;
    padding: 15px;
    // background: #f8f9fa;
    // border-top: 1px dashed gray;
    border-radius: 0 0 8px 8px;
    transition: all 0.3s ease-in-out;

    .card-body {
      display: flex;
      flex-wrap: wrap;

      .col-md-6 {
        flex: 1;
        // min-width: 250px;

        label {
          font-weight: bold;
          display: block;
          margin-top: 10px;
          font-size: 14px;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #333;
          background: #fff;
          // padding: 5px 10px;
          // border-radius: 4px;
          // border: 1px solid #ddd;
        }

        .status {
          font-weight: bold;
          color: #dc3545; // Red for pending status
        }
      }
    }
  }
}

.btn-status {
  font-weight: 400;
  font-size: small;
  border: 2px solid;
  padding: 5px 30px;
  border-radius: 5px;
  color: white;
  width: 100px;
  text-align: center;
  white-space: nowrap;

  &.sent {
    border-color: #0B74B0 !important;
    background-color: #0B74B0 !important;
  }

  &.reject {
    border-color: #B00005 !important;
    background-color: #B00005 !important;
  }

  &.accept {
    border-color: #219A0D !important;
    background-color: #219A0D !important;
  }

  &.hold {
    border-color: #00A6B0 !important;
    background-color: #00A6B0 !important;
  }

  &.pending {
    border-color: #00A6B0 !important;
    background-color: #00A6B0 !important;
  }

  &.processing {
    border-color: #D79E01 !important;
    background-color: #D79E01 !important;
  }

  &.completed {
    border-color: #219A0D !important;
    background-color: #219A0D !important;
  }

}
.modal-content {
  max-width: 90%;
  height: 500px; /* Fixed modal height */
  border-radius: 10px;
}

.modal-body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.image-slider {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  max-height: 90%; /* Keeps images inside modal */
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 400px; /* Fixed width */
  height: 400px; /* Fixed height */
  overflow: hidden;
  background: #000; /* Optional background for contrast */
  border: 1px solid gray; /* Optional border for visibility */
}

.image-frame {
  width: 100%;
  height: 100%;
  object-fit: contain; /* Ensures the whole image is visible */
}

// Navigation Buttons (positioned at image edges)
.slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  // background: rgba(0, 0, 0, 0.5);
  color: white;

  padding: 10px;
  margin: 10px !important;
  border-radius: 50%;
  cursor: pointer;
  transition: 0.3s;
  z-index: 10;
  height: 55px;
  i {
    font-size: 20px;
  }
}

// Position buttons at the image's left and right edges
.left-btn {
  left: -51%;
}

.right-btn {
  right: -51%;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  border: none;
}
.button-approve {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px; /* Space between text and image */
  border-radius: 10px; /* Rounded corners */
  border: none; /* Remove default border */
  cursor: pointer;
  background-color: white;
  border: 1px solid #B69FCA;
  width: 150px;
  height: 42px;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.2); /* Adds shadow */
}

.btn-text {
  padding-left: 5px; /* Small left padding for spacing */

}

.btn-icon {
  height: 40px; /* Set image height */
  width: auto; /* Maintain aspect ratio */
  padding: 0px !important;
}
.button-approve:hover {
  box-shadow: 4px 6px 10px rgba(0, 0, 0, 0.3); /* Darker shadow on hover */
  transform: translateY(-2px); /* Slight lift effect */
}
.pdf-not-found-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  background-color: #f8f9fa;
  padding: 20px;
}

.pdf-not-found-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}


.btn-back {
  background-color: #0f8669;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-back:hover {
  background-color: #0c6d54;
}
