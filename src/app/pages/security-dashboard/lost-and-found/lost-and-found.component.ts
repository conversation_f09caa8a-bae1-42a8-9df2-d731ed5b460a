import { <PERSON>mpo<PERSON>, Query<PERSON>ist, <PERSON><PERSON>hild, ViewChildren } from '@angular/core';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { ToastMessageComponent } from '../../../common/toast-message/toast-message.component';
import { BreadcrumbService } from '../../../common/breadcrumb/breadcrumb.service';
import { FormService } from '../../../common/Services/form-service.service';
import { Router } from '@angular/router';
import { SecurityService } from '../../../services/security-module/security.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PlantService } from '../../../services/plant/plant.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdminService } from '../../../services/admin/admin.service';
import { filter } from 'rxjs';
import { CdkAccordionItem } from '@angular/cdk/accordion';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { UploadService } from '../../../services/shared/upload.service';

@Component({
  selector: 'app-lost-and-found',
  templateUrl: './lost-and-found.component.html',
  styleUrl: './lost-and-found.component.scss',
  animations: [
    trigger('tabSwitch', [
      state('active', style({ opacity: 1, transform: 'translateX(0)' })),
      state('inactive', style({ opacity: 1, transform: 'translateX(0)' })),
      transition('inactive => active', [
        animate('300ms ease-in-out')
      ]),
      transition('active => inactive', [
        animate('300ms ease-in-out')
      ]),
    ]),
    trigger('detailExpand', [
      state('collapsed,void', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ]
})
export class LostAndFoundComponent {
  clickLoading: boolean = false;
  isFormOpen = false;
  isMarkAsFoundOpen: boolean = false
  form!: FormGroup
  lostAndFoundForm!: FormGroup;
  plants: any[] = []
  adminList: any[] = [];
  lostTickets: any[] = [];
  dropDownlostTickets: any[] = [];
  dropDownfoundTickets: any[] = [];
  foundTickets: any[] = [];
  isAddNew: boolean = false;
  files: File[] = [];
  isDragging = false;
  viewDetails: boolean = false
  accordionItem: boolean = false;
  images: any[] = [];
  currentIndex: number = 0;
  rejectSelected: boolean = false;
  approveSelected: boolean = false;
  addedItemId: number = 0
  lostItemDetails: any = {};
  foundItemDetails: any = {}
  statusFlag: boolean = false;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChildren(CdkAccordionItem) accordionItems!: QueryList<CdkAccordionItem>;
  pdfUrl: string = ''
  tabs = [
    { title: 'Lost', id: 0 },
    { title: 'Found', id: 1 },
  ];
  tableData: any[] = [];
  tableColumns = [
    { key: 'date', header: 'Date', width: '100px' },
    { key: 'employeeName', header: 'Employee Name', width: '200px' },
    { key: 'ticketId', header: 'Ticket Id' },
    { key: 'plantName', header: 'Plant/Site' },
    { key: 'desc', header: 'Details', width: '100px' },
    { key: 'status', header: 'Status' },
    { key: 'action', header: 'Action' }
  ];

  selectedTabIndex = 0;
  statusList: any[] = [
    { id: 0, name: 'Open' },
    { id: 1, name: 'Close' },
    { id: 2, name: 'Rejected' }
  ]
  editData: any;
  isEdit: boolean = false;
  accordionStates: boolean[] = [];
  isViewImageOpen: boolean = false;
  isViewPdfOpen: boolean = false;
  data: any[] = [];
  allowedFileTypes = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'];
  errorMessage: string = '';
  invalidFiles: any[] = [];
  pdfCount: number = 0;
  imageCount: number = 0;
  charCount: number = 0;
  selectedItem: any;
  selectTab(tabIndex: number) {
    this.selectedTabIndex = tabIndex;
    if (this.selectedTabIndex === 1) {
      this.data = this.foundTickets
    }
    else {
      this.data = this.lostTickets
    }
    console.log('selected tab', this.selectedTabIndex);
  }
  searchInputs = [
    { key: 'date', type: 'date', label: 'Date', options: [] },
    { key: 'ticketId', type: 'text', label: 'Ticket ID' },
    { key: 'plantName', type: 'dropdown', label: 'Plant/Site', options: this.plants, valueField: 'id', titleField: 'name' },
    { key: 'status', type: 'dropdown', label: 'Status', options: this.statusList, valueField: 'id', titleField: 'name' },
  ];

  ApproveRejectOption = [
    { key: 'approve', label: 'Approve' },
    { key: 'reject', label: 'Reject' }
  ]

  filteredOptions: any[] = [];
  items = [
    { title: 'Lost Item Details', expanded: true },
    { title: 'Found Details', expanded: false },
  ]
  constructor(private router: Router,
    private breadcrumb: BreadcrumbService,
    private SecurityService: SecurityService,
    private plantService: PlantService,
    private fb: FormBuilder,
    private uploadService: UploadService,
    private sanitizer: DomSanitizer,
    private adminService: AdminService,
    private formService: FormService) {
    this.images = [];
  }

  ngOnInit() {
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
    // this.accordionStates = this.items.map(() => false);
    // this. accordionStates[0] = true;

    this.form = this.fb.group({
      remark: [''],
      date: [''],
      adminId: [null],
      employeeName: [''],
      plantId: [''],
      options: [''],
      desc: ['']
    });
    this.lostAndFoundForm = this.fb.group({
      id: [''], // Not required by default
      approveRejectOpn: ['', Validators.required], // This is your approve/reject option
      remark: ['', Validators.required]
    })


    this.getLostAndFoundData()
    this.getAllPlant();
    this.getUsers()
    this.items[0].expanded = true;

    this.lostAndFoundForm.get('approveRejectOpn')?.valueChanges.subscribe(value => {
      const idControl = this.lostAndFoundForm.get('id');
    //  this.submitted = false; // Reset submission flag on dropdown change

      if (value === 'approve') {
        idControl?.setValidators(Validators.required);
        this.lostAndFoundForm.markAsUntouched()
      } else {
        idControl?.clearValidators();
        idControl?.setValue(''); // Clear ID value
        idControl?.setErrors(null); // Remove existing errors
        this.lostAndFoundForm.markAsUntouched()
      }

      idControl?.updateValueAndValidity();
      this.lostAndFoundForm.updateValueAndValidity(); // Update form validity
    });
  }
  updateCharCount(input: string) {
    this.charCount = this.form.get(input)?.value.trim().length || 0;
  }
  async getLostAndFoundData(data?: any) {
    let type = this.selectedTabIndex === 0 ? 0 : 1
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: [
          // `type||eq||${type}`
        ]

      };
    }
    const param = createAxiosConfig(data);
    this.clickLoading = true;
    await this.SecurityService.getLostAndFoundData(param).then(response => {
      if (response && response.data) {
        this.tableData = response.data
        this.tableData = response.data.map((item: any) => {
          item['employeeName'] = item?.admin?.firstName + ' ' + item?.admin?.lastName;
          item['plantName'] = item?.plant?.name;
          item['status'] == 1 ? item.status = 'Close' : item['status'] == 2 ? item.status = 'Rejected' : item.status = 'Open';
          item['ticketId'] = 'TIK' + item.id
          return item
        }
        )
        this.lostTickets = this.tableData.filter((ele: any) => ele.type == 0);
        this.foundTickets = this.tableData.filter((ele: any) => ele.type == 1)
        if (this.selectedTabIndex === 1) {
          this.data = this.foundTickets
        }
        else {
          this.data = this.lostTickets
        }
        this.dropDownlostTickets = this.lostTickets.filter((item: any) => item.status === 'Open')
        this.dropDownfoundTickets = this.foundTickets.filter((item: any) => item.status === 'Open')

        this.clickLoading = false;
        console.log('this.lostTickets', this.lostTickets);
        console.log('this.foundTickets', this.foundTickets)
      } else {
        this.toast.showErrorToast('Unexpected response structure.', 6000);
        this.clickLoading = false;
      }
    }).catch((error: any) => {
      this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }

  async getLostAndFoundDataById(id?: any) {
    let data = {
      page: 1,
      limit: 10000,
      sort: 'id,DESC',
      filter: []
    };
    const param = createAxiosConfig(data);
    this.clickLoading = true;
    await this.SecurityService.getLostAndFoundDataById(id, param).then(response => {
      if (response) {
        let data = Array.isArray(response) ? response : [response]
        if (this.selectedItem.type === 0) {
          this.foundItemDetails = data.map((item: any) => ({
            ...item, // Retain all original properties
            employeeName: `${item.admin?.firstName || ''} ${item.admin?.lastName || ''}`,
            plantName: item?.plant?.name || 'N/A',
            status: item?.status === 1 ? 'Close' : item?.status === 2 ? 'Rejected' : 'Open',
            ticketId: 'TIK' + item.id
          }));
          this.foundItemDetails = this.foundItemDetails[0]
        } else {
          this.lostItemDetails = data.map((item: any) => ({
            ...item,
            employeeName: `${item.admin?.firstName || ''} ${item.admin?.lastName || ''}`,
            plantName: item?.plant?.name || 'N/A',
            status: item.status === 1 ? 'Close' : item.status === 2 ? 'Rejected' : 'Open',
            ticketId: 'TIK' + item.id
          }));
          this.lostItemDetails = this.lostItemDetails[0]
        }
        this.clickLoading = false;
        console.log('Lost Item:', this.lostItemDetails);
        console.log('Found Item Details:', this.foundItemDetails);
      } else {
        this.toast.showErrorToast('Unexpected response structure.', 6000);
        this.clickLoading = false;
      }
    }).catch((error: any) => {
      this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';
    });
  }
  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging = false;
    this.handleFiles(event.dataTransfer?.files);
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    this.handleFiles(input.files);
  }




  handleFiles(fileList: any) {
    if (!fileList) return;
    // this.invalidFiles = []; // Clear previous invalid files
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const fileType = file.type;

      if (fileType === 'application/pdf') {
        if (this.pdfCount >= 1) {
          this.invalidFiles.push({ name: file.name, error: 'Only 1 PDF is allowed!' });
        } else {
          this.files.push(file);
          this.pdfCount++;
        }
      } else if (this.allowedFileTypes.includes(fileType) && fileType !== 'application/pdf') {
        if (this.imageCount >= 2) {
          this.invalidFiles.push({ name: file.name, error: 'Only 2 images are allowed!' });
        } else {
          this.files.push(file);
          this.imageCount++;
        }
      } else {
        this.invalidFiles.push({ name: file.name, error: 'This document is not supported. Please select another file' });
      }
    }
  }

  // Remove file from list
  removeFile(index: number) {
    const removedFile = this.files[index];
    if (removedFile.type === 'application/pdf') {
      this.pdfCount--;
    } else {
      this.imageCount--;
    }
    this.files.splice(index, 1);
  }

  // Remove invalid file from list
  removeInvalidFile(index: number) {
    this.invalidFiles.splice(index, 1);
  }
  selectedButton(selectedBtn: string) {
    if (selectedBtn == 'approve') {
      this.approveSelected = true;
      this.rejectSelected = false;
    }
    else {
      this.rejectSelected = true;
      this.approveSelected = false;
    }
  }

  statusSelectFn(option: any) {
    const selectedValue = option.target.value;
    if (selectedValue === 'approve') {
      this.approveSelected = true;
      this.rejectSelected = false;
      this.statusFlag = true;
    } else {
      this.statusFlag = false;
      this.rejectSelected = true;
      this.approveSelected = false;
    }
  }


  getAllPlant(): Promise<void> {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]
    }
    const param = createAxiosConfig(data);
    return this.plantService.getAllPlants(param).then((response: any) => {
      this.plants = [...response.data, this.plants]
      this.searchInputs = [
        { key: 'date', type: 'date', label: 'Date', options: [] },
        { key: 'ticketId', type: 'text', label: 'Ticket ID' },
        { key: 'plantName', type: 'dropdown', label: 'Plant/Site', options: this.plants, valueField: 'id', titleField: 'name' },
        { key: 'status', type: 'dropdown', label: 'Status', options: this.statusList, valueField: 'id', titleField: 'name' },
      ];
    })
  }
  searchFilter(event: any) {
    console.log('event', event);
    const searchFilter = event;
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]

    }
    if (searchFilter.date) {
      data.filter.push(`date||eq||${searchFilter?.date}`);
    }
    if (searchFilter.status) {
      data.filter.push(`status||eq||${searchFilter?.status}`);
    }
    if (searchFilter.plantName) {
      data.filter.push(`plantId||eq||${searchFilter?.plantName}`);
    }
    if (searchFilter.ticketId) {
      data.filter.push(`id||eq||${searchFilter?.ticketId}`);
    }

    this.getLostAndFoundData(data)
  }

  getUsers() {
    const data = {
      page: 1,
      limit: 10000,
      // sort: 'name,ASC',
      filter: [
        'adminsRoleId||eq||3',
        'status||eq||1'
      ]
    }
    this.clickLoading = true;
    const param = createAxiosConfig(data);
    return this.adminService.getUsers(param).then((res: any) => {
      this.adminList = res.data.map((item: any) => {
        return { id: item.id, 'name': item.firstName + ' ' + item.lastName }
      });
      this.clickLoading = false;
    }
    )
  }
  onEditClick(item: any) {
    console.log(item)
    this.isFormOpen = true;
    this.isEdit = true;
    this.form.get('date')?.disable();
    this.form.get('employeeName')?.disable();
    this.form.get('ticketId')?.disable();
    this.form.get('desc')?.disable();
    this.form.get('plantId')?.disable()
    this.form.get('remark')?.setValidators([Validators.required, Validators.maxLength(500), Validators.minLength(30)]);
    this.form.updateValueAndValidity()
    this.form.patchValue({
      date: item.date,
      employeeName: item.employeeName,
      adminId: item.adminId,
      ticketId: item.ticketId,
      desc: item.desc,
      plantId: item?.plant?.id
    });
    this.editData = item;
    console.log('this.form value', this.form.value)
  }

  resetForm() {
    this.charCount = 0
    if (this.isEdit) {
      this.form.get('remark')?.reset()
    }
    else {
      this.form.reset()
      this.lostAndFoundForm.reset();
      this.form.get('plantId')?.setValue('')
      this.files = [];
      this.invalidFiles = []
    }
  }

  markAsFound() {
    this.formService.trimFormValues(this.form)
    console.log('this.', this.form);
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return
    }
    let json = {
      id: this.editData.id,
      tableName: 'lost-found',
      data: {
        'remark': this.form.value['remark'],
        'status': 1
      }
    }
    this.updateLostAndFound(json)
    // this.clickLoading = true;
    // this.SecurityService.updateLostAndFound(json).then((res: any) => {
    //   this.clickLoading = false;
    //   if (res.responseCode == 400) {
    //     this.toast.showErrorToast(res.message)
    //   }
    //   if (res.responseCode == 200) {
    //     this.toast.showSuccessToast('Item Is Mark As Found');
    //     this.isFormOpen = false;
    //     this.isEdit = false;
    //     this.resetForm();
    //     this.getLostAndFoundData()
    //   }
    // })
  }

  async markAsApproved() {
    this.formService.trimFormValues(this.form)
    console.log('this.', this.form);
    let json = {}
    if (!this.isAddNew) {
      if (this.lostAndFoundForm.invalid) {
        this.lostAndFoundForm.markAllAsTouched()
        return
      }
      json = {
        id: this.selectedItem.id,
        tableName: 'lost-found',
        data: {
          'status': this.approveSelected == true ? 1 : 2,
          'remark': this.lostAndFoundForm.value.remark,
          ...(this.selectedTabIndex === 1
            ? { foundId: this.lostAndFoundForm.value.id === "" ? null : this.lostAndFoundForm.value.id }
            : { lostId: this.lostAndFoundForm.value.id === "" ? null : this.lostAndFoundForm.value.id })
        }
      };
      this.updateLostAndFound(json)
    }
    else {
      if (this.form.invalid) {
        this.form.markAsTouched();
        return
      }
      if (this.approveSelected == true) {
        await this.addItem();

      }
      else {
        json = {
          id: this.selectedItem.id,
          tableName: 'lost-found',
          data: {
            // 'lostId': this.form.value.id,
            'status': 2,
            'remark': this.form.value.remark,
            ...(this.selectedTabIndex === 1
              ? { foundId: this.addedItemId }
              : { lostId: this.addedItemId })
          }
        }
      }
    }
    console.log('form value', json)

    this.lostAndFoundForm.reset();
    this.statusFlag = false;
    this.approveSelected = false;

  }
  updateLostAndFound(json: any) {
    this.clickLoading = true;
    this.SecurityService.updateLostAndFound(json).then((res: any) => {
      this.clickLoading = false;
      if (res.responseCode == 400) {
        this.toast.showErrorToast(res.message)
      }
      if (res.responseCode == 200) {
        this.toast.showSuccessToast('Item Is Mark As Found');
        // this.isFormOpen = false;
        this.isMarkAsFoundOpen = false;

        this.isEdit = false;
        this.resetForm();
        this.getLostAndFoundData()
      }
    })
  }
  async uploadProfilePic(file: any): Promise<string> {
    let url = "";
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        url = await this.uploadService.uploadImage(formData);
      } catch (error) {
        console.error("Error uploading image:", error);
        return ''
      }
    }
    return url;
  }

  async addItem() {
    this.formService.trimFormValues(this.form);
    console.log('this.form', this.form);

    if (this.form.invalid || this.invalidFiles.length > 0) {
      this.form.markAllAsTouched();
      return
    }
    const imagesURL = [];
    let pdfURL = ''
    if (this.files.length > 0) {
      for (const file of this.files) {
        this.clickLoading = true;
        const uploadedFileURL = await this.uploadProfilePic(file);

        if (file.type === 'application/pdf') {
          pdfURL = uploadedFileURL;
        } else {
          imagesURL.push(uploadedFileURL);
        }
        this.clickLoading = false;
      }
      console.log('Image URLs:', imagesURL);
      console.log('PDF URL:', pdfURL);
    }
    let formData = this.form.value

    let json = {
      'adminId': formData.adminId,
      'type': this.isMarkAsFoundOpen ? this.selectedTabIndex == 1 ? 0 : 1 : this.selectedTabIndex == 1 ? 1 : 0,
      'desc': formData.desc,
      'plantId': formData.plantId,
      'date': this.formatDate(formData.date),
      'image': imagesURL,
      'pdf': pdfURL,
      'remark': formData.remark ? formData.remark : '',
      'status': this.isMarkAsFoundOpen == true ? 1 : 0
    }
    console.log('json', json);
    this.clickLoading = true;
    this.SecurityService.addLostAndFound(json).then((res: any) => {
      this.clickLoading = false;
      if (res.responseCode == 400) {
        this.toast.showErrorToast(res.message)
      }
      if (res.responseCode == 200) {
        this.isFormOpen = false;
        if (this.isMarkAsFoundOpen == true) {
          this.addedItemId = res.data.id;
          let json = {
            id: this.selectedItem.id,
            tableName: 'lost-found',
            data: {
              'status': this.approveSelected == true ? 1 : 2,
              'remark': this.form.value.remark,
              ...(this.selectedTabIndex === 0
                ? { foundId: this.addedItemId }
                : { lostId: this.addedItemId })
            }
          };
          console.log('update json', json)
          this.updateLostAndFound(json);
        }
        else {
          this.toast.showSuccessToast('Item Added Successfully.')
          this.getLostAndFoundData();
        }
        console.log('mark as found', this.isMarkAsFoundOpen, this.addedItemId)
        this.resetForm()
        this.files = [];
        this.invalidFiles = [];

      }
    })
  }

  openAdd() {
    this.isFormOpen = true;
    this.updateForm();

  }

  updateForm() {
    this.form.get('date')?.enable();
    this.form.get('employeeName')?.enable();
    this.form.get('ticketId')?.enable();
    this.form.get('desc')?.enable();
    this.form.get('plantId')?.enable()
    this.form.updateValueAndValidity();
    this.resetForm()
    this.form.get('date')?.setValidators(Validators.required);
    this.form.get('employeeName')?.setValidators(Validators.required);
    this.form.get('ticketId')?.setValidators(Validators.required);
    this.form.get('plantId')?.setValidators(Validators.required);
    this.form.get('desc')?.setValidators([Validators.required, Validators.minLength(30), Validators.maxLength(500)])
  }

  onClickCross() {
    this.isFormOpen = false;
    this.resetForm()
    this.isEdit = false;
    this.updateForm()
  }

  filterOptions(event: any) {
    if (!event || !event.target?.value) {
      this.filteredOptions = [];
      return;
    }

    const filterValue = event.target.value.toLowerCase().trim();
    this.filteredOptions = this.adminList.filter(option =>
      option.name.toLowerCase().startsWith(filterValue)  // ✅ Now filters only from the start
    );
  }


  selectOption(option: any) {
    this.form.get('employeeName')?.setValue(option.name)
    this.form.get('adminId')?.setValue(option.id)
    this.filteredOptions = [];
  }

  formatDate(date: string): string {
    if (!date) return '';
    const [year, month, day] = date.split('-'); // Convert from YYYY-MM-DD
    return `${day}-${month}-${year}`; // Convert to DD-MM-YYYY
  }

  updateDate(event: any) {
    let value = event.target?.value
    const regex = /^\d{2}-\d{2}-\d{4}$/; // Ensure valid format (DD-MM-YYYY)
    if (regex.test(value)) {
      const [day, month, year] = value.split('-');
      this.form.get('date')?.setValue(`${year}-${month}-${day}`); // Convert back to YYYY-MM-DD
    }
  }
  openLostAndFoundModal() {
    this.openModal2('lostAndFoundModal')
  }
  openModal2(modalName: string) {
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'block';
      modal?.classList.add('show');
      modal?.setAttribute('aria-hidden', 'false');
      modal?.setAttribute('aria-modal', 'true');
      modal?.setAttribute('role', 'dialog');
    }
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop fade show';
    document.body.appendChild(backdrop);

    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = '0px';
  }
  closeModal2(modalName: string) {

    this.statusFlag = true;
    const modal = document.getElementById(modalName);
    if (modal) {
      modal.style.display = 'none';
      modal?.classList.remove('show');
      modal?.setAttribute('aria-hidden', 'true');
      modal?.removeAttribute('aria-modal');
      modal?.removeAttribute('role');
      this.lostAndFoundForm.reset()
    }

    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.parentNode?.removeChild(backdrop);
    }

    const backdrop0 = document.querySelector('.modal-backdrop');
    if (backdrop0) {
      backdrop0.parentNode?.removeChild(backdrop0);
    }

    document.body.className = '';
    document.body.removeAttribute('style');
    document.body.removeAttribute('data-bs-overflow');
    document.body.removeAttribute('data-bs-padding-right');
  }
  approve(item: any) {
    // this.form.addControl('remark', Validators.required)
    this.selectedItem = item;
    this.resetForm();
    this.isMarkAsFoundOpen = true
    this.mapID(this.selectedItem);
  }

  mapID(data: any) {
    const getID = this.lostTickets.find((event => data.id === event.id))
    console.log("getID", getID);

  }
  openViewImageDialog(selectedItem?: any) {
    this.isViewImageOpen = true;
    this.isViewPdfOpen = false;
    this.openLostAndFoundModal()

    this.images = selectedItem.image;
    this.pdfUrl = selectedItem.pdf
  }
  openViewPdfDialog() {
    this.isViewPdfOpen = true;
    this.isViewImageOpen = false;
    this.openLostAndFoundModal()
  }
  addApproveForm() {
    this.isAddNew = !this.isAddNew;
    if (!this.isAddNew) {
      this.form.reset();

    }
  }


  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragging = true;
  }

  onDragLeave() {
    this.isDragging = false;
  }


  validateFiles(files: FileList): File[] {
    const validFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      if (this.allowedFileTypes.includes(files[i].type)) {
        validFiles.push(files[i]);
      } else {
        this.errorMessage = 'Invalid file type! Only PNG, JPG, JPEG, and PDF files are allowed.';
      }
    }
    return validFiles;
  }

  addFiles(fileList: any[]) {
    for (let i = 0; i < fileList.length; i++) {
      this.files.push(fileList[i]);
    }
  }


  oncloseViewDetails() {
    this.viewDetails = false;
  }
  onViewClick(item: any) {
    this.viewDetails = true;
    this.selectedItem = item;

    if (this.selectedItem.type == 1) {
      this.foundItemDetails = this.selectedItem
      this.getLostAndFoundDataById(this.selectedItem.lostId)
    }
    else {
      this.lostItemDetails = this.selectedItem;
      this.getLostAndFoundDataById(this.selectedItem.foundId)
    }
  }
  onAccordionToggle(index: number): void {
    this.accordionStates[index] = true;
    // this.items[index].expanded = !this.items[index].expanded;

  }
  nextImage() {
    this.currentIndex = (this.currentIndex + 1) % this.images.length;
  }

  prevImage() {
    this.currentIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
  }



}


