<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>

<div class="d-flex align-items-center justify-content-between">
  <div class="col-md-4">
    <p class="fw-bold mb-0">Lost & Found</p>
  </div>

  <div class="tab-container col-md-6 d-flex justify-content-start">
    <div class="tab-headers d-flex">
      <div *ngFor="let tab of tabs; let i = index" [@tabSwitch]="i === selectedTabIndex ? 'active' : 'inactive'"
        [class.active]="i === selectedTabIndex" (click)="selectTab(i)" class="tab-header px-3">
        {{ tab.title }}
      </div>
    </div>
  </div>

  <div class="d-flex">
    <button class="button-submit button-right d-flex align-items-center" style="width: 120px !important;"
      (click)="openAdd()">
      <img src="../../../assets/svg/add.svg" class="right-arrow" alt="" />
      <span style="color: white;">ADD</span>
    </button>
  </div>
</div>

<div *ngIf="!clickLoading">

  <app-ad-table [data]="data" [isSearchShow]="true" [filters]="searchInputs" [isTableFilter]="true"
    (search)="searchFilter($event)" (edit)="onEditClick($event)" (approve)="approve($event)"
    (view)="onViewClick($event)" [isTableTitle]="false" [columns]="tableColumns"></app-ad-table>
</div>

<app-custom-modal
  [title]="isEdit ?'Item Details' :(selectedTabIndex == 0 && !isEdit ) ? 'Add Lost Item' :'Add Found Item'"
  [width]="'50%'" *ngIf="isFormOpen" (onClickCross)="onClickCross()">
  <div class="mb-0">
    <form [formGroup]="form">
      <div class="row d-flex m-2">

        <div class="col-md-6 " class="autocomplete-container">
          <label class="ml-12">Employee Name<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <input class="form-control" type="text" formControlName="employeeName" placeholder="Search Employee..."
            (input)="filterOptions($event)" />
          <ul *ngIf="filteredOptions.length > 0" class="autocomplete-list">
            <li *ngFor="let option of filteredOptions" (click)="selectOption(option)" style="font-size: smaller;">
              {{ option?.name }}
            </li>
          </ul>
          <span class="text-danger ml-12"
            *ngIf="(form.get('employeeName')?.touched|| form.get('employeeName')?.dirty) && form.get('employeeName')?.errors?.['required']">
            Employee Name is required.
          </span>
        </div>

        <!-- <div class="col-md-12 ">
          <label class="ml-12">Employee Name<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <input type="text" class="form-control" id="employeeName" formControlName="employeeName" placeholder="Enter Employee Name" />
          <span class="text-danger ml-12"
          *ngIf="(form.get('employeeName')?.touched|| form.get('employeeName')?.dirty) && form.get('employeeName')?.errors?.['required']">
          Employee Name is required.
        </span>
        </div> -->
        <div class="col-md-6">
          <label class="fw-bold ml-6">Date<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <input class="form-control" [disabled]="true" placeholder="DD-MM-YYYY" type="date" class="form-control"
            formControlName="date" id="date" />
          <span class="text-danger ml-12"
            *ngIf="(form.get('date')?.touched|| form.get('date')?.dirty) && form.get('date')?.errors?.['required']">
            Date is required.
          </span>
        </div>

        <div class="col-md-6 ">
          <label class="ml-12">Plant / Site<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <select class="form-control" formControlName="plantId" id="plantId">
            <option value="">Select Plant / Site</option>
            <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
          </select>
          <span class="text-danger ml-12"
            *ngIf="(form.get('plantId')?.touched|| form.get('plantId')?.dirty) && form.get('plantId')?.errors?.['required']">
            Site / Plant is required.
          </span>
        </div>
        <div class="col-md-12">
          <label class="fw-bold mb-1 ml-12">
            Details<span *ngIf="!isEdit" class="text-danger"> * </span>
          </label>

          <textarea rows="4" placeholder="Enter here..." class="form-control" id="desc" formControlName="desc"
            (input)="updateCharCount('desc')" maxlength="500">
          </textarea>

          <!-- Validation Messages -->
          <span class="text-danger ml-12" *ngIf="form.get('desc')?.dirty || form.get('desc')?.touched">
            <span *ngIf="form.get('desc')?.errors?.['required']">Details are required.</span>
            <span class="ps-1" *ngIf="charCount < 30 && !isEdit">Minimum 30 characters required.</span>
            <span class="ps-1" *ngIf="charCount >500 && !isEdit">Maximum 500 characters allowed.</span>
          </span>

          <!-- Character Count Display -->
          <p class="text-muted mt-1" style="font-size: small;" *ngIf="!isEdit">
            {{ charCount }}/500 characters
          </p>
        </div>
        <div class="col-md-6" *ngIf="!isEdit">
          <label class="fw-bold mb-1 ml-12">Upload</label>
          <div class="upload-box" [class.dragging]="isDragging" (dragover)="onDragOver($event)"
            (dragleave)="onDragLeave()" (drop)="onDrop($event)">
            <div>
              <img style="width: 15%;" alt="" src="../../../assets/svg/upload-plain.svg" />
            </div>
            <p><b>Drag & Drop files here or <a (click)="fileInput.click()">Browse</a></b></p>
            <p style="font-size:xx-small;">Supported formate : Png, Jpg & PDf</p>
            <input type="file" (change)="onFileSelected($event)" multiple hidden #fileInput>
          </div>
          <!-- <p *ngIf="errorMessage" style="color: red; font-size: small;">{{ errorMessage }}</p> -->
        </div>
        <div class="col-md-6" *ngIf="(files.length > 0 ||invalidFiles.length > 0) && !isEdit">
          <label class="fw-bold mb-1 ml-12">Selected Attachments</label>
          <ul *ngIf="files.length > 0">
            <li *ngFor="let file of files; let i = index">
              <div class="valid">
                {{ file.name }}
                <button>
                  <i class="bi bi-x" style="cursor: pointer; color: #0f8669;font-size: 18px;"
                    (click)="removeFile(i)"></i>
                </button>
              </div>
            </li>
          </ul>
          <ul *ngIf="invalidFiles.length > 0">
            <li *ngFor="let file of invalidFiles; let i = index">
              <div class="error">
                {{ file.name }}
                <button (click)="removeInvalidFile(i)">
                  <i class="bi bi-x" style="cursor: pointer; color: red; font-size: 18px;"></i>
                </button>
              </div>
              <p style="font-size: x-small;">
                {{ file.error }}
              </p>
          </ul>
        </div>
        <div class="col-md-6" *ngIf="files.length > 0 && isEdit">
          <label class="fw-bold">Uploaded Image</label>
          <button type="button" style="margin:10px 0px !important" id="bt_reject" class="button-back button-left"
            (click)="openViewImageDialog()">
            <span class="ps-2">View Image</span>
            <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg" class="left-arrow" />
          </button>
        </div>
        <div class="col-md-6" *ngIf="files.length > 0 && isEdit">
          <label class="fw-bold">Uploaded PDF</label>
          <button type="button" style="margin:10px 0px !important" id="bt_reject" class="button-back button-left"
            (click)="openViewPdfDialog()">
            <span class="ps-2">View PDF</span>
            <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg" class="left-arrow" />
          </button>
        </div>
        <div class="col-md-12 mt-3 " *ngIf="isEdit">
          <div style="margin: 5px;">
            <label class="fw-bold mb-1">Remarks<span class="text-danger"> * </span></label>
            <textarea rows="6" placeholder="Enter Remark here..." class="form-control"
              (input)="updateCharCount('remark')" formControlName="remark"></textarea>
            <!-- <span class="text-danger"
              *ngIf="(form.get('remark')?.dirty|| form.get('remark')?.touched) && form.get('remark')?.errors?.['required']">
              Remarks is required.
            </span> -->
            <span class="text-danger ml-12" *ngIf="form.get('remark')?.dirty || form.get('remark')?.touched">
              <span *ngIf="form.get('desc')?.errors?.['required']">Remark is required.</span>
              <span class="ps-1" *ngIf="charCount < 30">Minimum 30 characters required.</span>
              <span class="ps-1" *ngIf="charCount >500">Maximum 500 characters allowed.</span>
            </span>
          </div>
          <p class="text-muted mt-1" style="font-size: small;">
            {{ charCount }}/500 characters
          </p>

        </div>
      </div>

      <div class="d-flex mt-4" style="margin-left: 20px;">
        <button type="submit" id="bt_approve" class="button-submit button-left"
          (click)="isEdit ? markAsFound(): addItem()">
          <span class="ps-2">{{isEdit ? 'Mark As Found':'Add Item'}}</span>
          <img alt="" src="../../../assets/svg/right-arrow.svg" class="left-arrow" />
        </button>
        <button type="button" id="bt_reject" class="button-back button-left" (click)="resetForm()">
          <span class="ps-2">Reset</span>
          <img style="margin-left: -31px" alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
        </button>
      </div>
    </form>


  </div>
</app-custom-modal>

<div class="modal fade" tabindex="-1" id="lostAndFoundModal" aria-labelledby="lostAndFoundModalLabel" #lostAndFoundModal
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header confirmModalHeader justify-content-between">
        <h5 class="modal-title">{{isViewImageOpen ? 'IMAGE' :'PDF'}}</h5>
        <button type="button" class="btn" data-bs-dismiss="modal" aria-label="Close"
          (click)="closeModal2('lostAndFoundModal')">
          X
        </button>
      </div>

      <div *ngIf="isViewImageOpen" class="modal-body confirmModalBody">
        <div class="image-slider">
          <img (click)="prevImage()" class="slider-btn left-btn" alt="Previous"
            src="../../../assets/svg/left-arrow-gradient.svg" />

          <div class="image-container">
            <img [src]="images[currentIndex]" class="image-frame">
          </div>

          <img (click)="nextImage()" class="slider-btn right-btn" alt="Next"
            src="../../../assets/svg/right-arrow-gradient.svg" />
        </div>
      </div>

      <div *ngIf="isViewPdfOpen" style="height: 100%;">
        <iframe *ngIf="pdfUrl" [src]="pdfUrl | safeURL" class="pdf-viewer"></iframe>
        <div *ngIf="!pdfUrl" class="pdf-not-found-container">
          <h1 class="pdf-not-found-title">PDF Not Found</h1>
          <button class="btn-back" (click)="closeModal2('lostAndFoundModal')">Back</button>
        </div>
      </div>
    </div>
  </div>

</div>

<app-custom-modal [title]="'LIST OF LOST/FOUND'" [width]="'50%'" *ngIf="viewDetails"
  (onClickCross)="oncloseViewDetails()">
  <cdk-accordion class="example-accordion">
    <ng-container *ngFor="let item of items; let index = index">
      <cdk-accordion-item #accordionItem="cdkAccordionItem" [expanded]="item.expanded">
        <div class="accordion-card">
          <div class="example-accordion-item-header" [attr.data-text]="item.title"
            (click)="onAccordionToggle(index); accordionItem.toggle()">
            <span class="title">{{ item.title }}</span>
            <button type="button" class="icon-toggle" aria-label="Toggle Accordion">
              <div class="icon-toggle">
                <img *ngIf="!accordionItem.expanded" src="../../../assets/svg/down-arrow-gradient.svg" />
                <img *ngIf="accordionItem.expanded" src="../../../assets/svg/up-arrow-gradient.svg" />
              </div>
            </button>
          </div>
          <div class="row example-accordion-item-body" [style.display]="accordionItem.expanded ? 'flex' : 'none'">
            <hr style="border-top:2px dashed gray">
            <div class="card-body row " *ngIf="item.title == 'Lost Item Details'">
              <div class="col-md-6">
                <label class="fw-bold">Date</label>
                <p>{{lostItemDetails?.date}}</p>
                <label class="fw-bold">Ticket Id</label>
                <p>{{lostItemDetails?.ticketId}}</p>
                <label class="fw-bold">Details</label>
                <p style="font-size:smaller; text-align: justify;">
                  {{lostItemDetails?.desc}}
                </p>
              </div>

              <div class="col-md-6" style="border-left: 2px dashed rgb(179 176 176 / 45%)">
                <label class="fw-bold">Employee Name</label>
                <p>{{lostItemDetails?.employeeName}}</p>
                <label class="fw-bold">Plant Site</label>
                <p>{{lostItemDetails?.plantName}}</p>
                <label class="fw-bold">Status</label>
                {{lostItemDetails?.status}}
                <span class="btn-status mt-1" style="padding-left: 10px;"
                  [ngClass]="{'reject':lostItemDetails?.status == 'Close' ? 'accept':lostItemDetails?.status == 'Open' ? 'processing':lostItemDetails?.status == 'Rejected'}">{{lostItemDetails?.status}}</span>
              </div>
              <div class="col-md-12">
              </div>
              <div class="col-md-6">
                <label class="fw-bold">Uploaded Image</label>
                <button type="button" style="margin:10px 0px !important" id="bt_reject" class="button-back button-left"
                  (click)="openViewImageDialog(lostItemDetails)">
                  <span class="ps-2">View Image</span>
                  <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg" class="left-arrow" />
                </button>
              </div>
              <div class="col-md-6" style="border-left: 2px dashed rgb(179 176 176 / 45%)">
                <label class="fw-bold">Uploaded PDF</label>
                <button type="button" style="margin:10px 0px !important" id="bt_reject" class="button-back button-left"
                  (click)="openViewPdfDialog()">
                  <span class="ps-2">View PDF</span>
                  <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg" class="left-arrow" />
                </button>
              </div>
            </div>

            <div class="card-body row " *ngIf="item.title == 'Found Details'">
              <div class="col-md-6">
                <label class="fw-bold">Date</label>

                <p>{{foundItemDetails?.date}}</p>

                <label class="fw-bold">Ticket Id</label>
                <p>{{foundItemDetails?.ticketId}}</p>
                <label class="fw-bold">Details</label>
                <p style="font-size:smaller; text-align: justify;">
                  {{foundItemDetails?.desc}}
                </p>
              </div>

              <div class="col-md-6" style="border-left: 2px dashed rgb(179 176 176 / 45%)">
                <label class="fw-bold">Employee Name</label>
                <p>{{foundItemDetails?.employeeName}}</p>

                <label class="fw-bold">Plant Site</label>
                <p>{{foundItemDetails?.plantName}}</p>
                <label class="fw-bold">Status</label>
                <span class="btn-status mt-1"
                  [ngClass]="{'reject':foundItemDetails?.status == 'Close' ? 'accept':foundItemDetails?.status == 'Open' ? 'processing':foundItemDetails?.status == 'Rejected'}">{{foundItemDetails?.status}}</span>


              </div>
              <div class="col-md-12">

              </div>
              <div class="col-md-6">
                <label class="fw-bold">Uploaded Image</label>
                <button type="button" style="margin:10px 0px !important" id="bt_reject" class="button-back button-left"
                  (click)="openViewImageDialog(foundItemDetails)">
                  <span class="ps-2">View Image</span>
                  <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg" class="left-arrow" />
                </button>
              </div>
              <div class="col-md-6" style="border-left: 2px dashed rgb(179 176 176 / 45%)">
                <label class="fw-bold">Uploaded PDF</label>
                <button type="button" style="margin:10px 0px !important" id="bt_reject" class="button-back button-left"
                  (click)="openViewPdfDialog()">
                  <span class="ps-2">View PDF</span>
                  <img style="margin-left: -31px" alt="" src="../../../assets/svg/eye-pink.svg" class="left-arrow" />
                </button>
              </div>
            </div>


          </div>
        </div>
      </cdk-accordion-item>

    </ng-container>
  </cdk-accordion>
</app-custom-modal>

<app-custom-modal [title]="'Item Details'" [width]="'50%'" *ngIf="isMarkAsFoundOpen"
  (onClickCross)="isMarkAsFoundOpen = false">
  <div class="m-3">
    <form [formGroup]="lostAndFoundForm" *ngIf="!isAddNew">
      <div style="margin-bottom: 10px;">
        <label class="ml-12">Select Option<span *ngIf="!isEdit" class="text-danger"> * </span></label>
        <select formControlName="approveRejectOpn" class="form-control" id="ApproveRejectOption"
          (change)="statusSelectFn($event)">
          <option value="">Select Option</option>
          <option *ngFor="let approveReject of ApproveRejectOption" [value]="approveReject.key">{{ approveReject.label
            }}</option>
        </select>
        <span class="text-danger ml-12"
          *ngIf="(lostAndFoundForm.get('approveRejectOpn')?.touched|| lostAndFoundForm.get('approveRejectOpn')?.dirty) && lostAndFoundForm.get('approveRejectOpn')?.errors?.['required']">
          Approve / Reject is required.
        </span>
      </div>
      <div *ngIf="selectedTabIndex === 0 && lostAndFoundForm.get('approveRejectOpn')?.value === 'approve'">
        <label class="ml-12">Select Found Ticket Id<span *ngIf="!isEdit" class="text-danger"> * </span></label>
        <select formControlName="id" class="form-control" id="ticketId" formControlName="id">
          <option value="">Select Found Ticket ID</option>
          <option *ngFor="let ticket of dropDownfoundTickets" [value]="ticket.id">{{ ticket.ticketId }}</option>
        </select>
        <span class="text-danger ml-12"
          *ngIf="(lostAndFoundForm.get('id')?.touched|| lostAndFoundForm.get('id')?.dirty) && lostAndFoundForm.get('id')?.errors?.['required']">
          Found ticket id is required.
        </span>
      </div>
      <div *ngIf="selectedTabIndex === 1 && lostAndFoundForm.get('approveRejectOpn')?.value === 'approve'">
        <label class="ml-12">Select Lost Ticket Id<span *ngIf="!isEdit" class="text-danger"> * </span></label>
        <select formControlName="id" class="form-control" id="ticketId">
          <option value="">Select Lost Ticket ID</option>
          <option *ngFor="let ticket of dropDownlostTickets" [value]="ticket.id">{{ ticket.ticketId }}</option>
        </select>
        <span class="text-danger ml-12"
          *ngIf="(lostAndFoundForm.get('id')?.touched|| lostAndFoundForm.get('id')?.dirty) && lostAndFoundForm.get('id')?.errors?.['required']">
          Lost is required.
        </span>
      </div>
      <div class="col-md-12 mt-3">
        <div>
          <label class="fw-bold mb-1">Remarks<span class="text-danger"> * </span></label>
          <textarea rows="6" placeholder="Enter Remark here..." class="form-control"
            formControlName="remark"></textarea>
          <span class="text-danger"
            *ngIf="(lostAndFoundForm.get('remark')?.dirty|| lostAndFoundForm.get('remark')?.touched) && lostAndFoundForm.get('remark')?.errors?.['required']">
            Remarks is required.
          </span>
        </div>
      </div>
    </form>


    <form [formGroup]="form" *ngIf="isAddNew">
      <div class="row d-flex">

        <div class="col-md-6 mt-2 " class="autocomplete-container">
          <label class="ml-12">Employee Name<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <input class="form-control" type="text" formControlName="employeeName" placeholder="Search Employee..."
            (input)="filterOptions($event)" />
          <ul *ngIf="filteredOptions.length > 0" class="autocomplete-list">
            <li *ngFor="let option of filteredOptions" (click)="selectOption(option)" style="font-size: smaller;">
              {{ option?.name }}
            </li>
          </ul>
          <span class="text-danger ml-12"
            *ngIf="(form.get('employeeName')?.touched|| form.get('employeeName')?.dirty) && form.get('employeeName')?.errors?.['required']">
            Employee Name is required.
          </span>
        </div>

        <div class="col-md-6">
          <label class="fw-bold ml-6">Date<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <input class="form-control" [disabled]="true" placeholder="DD-MM-YYYY" type="date" class="form-control"
            formControlName="date" id="date" />
          <span class="text-danger ml-12"
            *ngIf="(form.get('date')?.touched|| form.get('date')?.dirty) && form.get('date')?.errors?.['required']">
            Date is required.
          </span>
        </div>

        <div class="col-md-6 ">
          <label class="ml-12">Plant / Site<span *ngIf="!isEdit" class="text-danger"> * </span></label>
          <select class="form-control" formControlName="plantId" id="plantId">
            <option value="">Select Plant / Site</option>
            <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
          </select>
          <span class="text-danger ml-12"
            *ngIf="(form.get('plantId')?.touched|| form.get('plantId')?.dirty) && form.get('plantId')?.errors?.['required']">
            Site / Plant is required.
          </span>
        </div>
        <div class="col-md-6">
          <label class="fw-bold mb-1 ml-12">Details of the Item<span *ngIf="!isEdit" class="text-danger"> *
            </span></label>
          <textarea rows="4" placeholder="Enter here..." class="form-control" id="desc"
            formControlName="desc"></textarea>
          <span class="text-danger ml-12"
            *ngIf="(form.get('desc')?.dirty|| form.get('desc')?.touched) && form.get('desc')?.errors?.['required']">
            Details are required.
          </span>
        </div>


        <div class="col-md-6">
          <label class="fw-bold mb-1">Remarks<span class="text-danger"> * </span></label>
          <textarea rows="4" placeholder="Enter Remark here..." class="form-control" (input)="updateCharCount('remark')"
            formControlName="remark"></textarea>
          <span class="text-danger ml-12" *ngIf="form.get('remark')?.dirty || form.get('remark')?.touched">
            <span *ngIf="form.get('desc')?.errors?.['required']">Remark is required.</span>
            <span class="ps-1" *ngIf="charCount < 30">Minimum 30 characters required.</span>
            <span class="ps-1" *ngIf="charCount >500">Maximum 500 characters allowed.</span>
          </span>
          <p class="text-muted" style="font-size: small;">
            {{ charCount }}/500 characters
          </p>
        </div>
        <div class="col-md-6" *ngIf="!isEdit">
          <label class="fw-bold mb-1 ml-12">Upload</label>
          <div class="upload-box" [class.dragging]="isDragging" (dragover)="onDragOver($event)"
            (dragleave)="onDragLeave()" (drop)="onDrop($event)">
            <div>
              <img style="width: 15%;" alt="" src="../../../assets/svg/upload-plain.svg" />
            </div>
            <p><b>Drag & Drop files here or <a (click)="fileInput.click()">Browse</a></b></p>
            <p style="font-size:xx-small;">Supported formate : Png, Jpg & PDf</p>
            <input type="file" (change)="onFileSelected($event)" multiple hidden #fileInput>
          </div>
          <!-- <p *ngIf="errorMessage" style="color: red; font-size: small;">{{ errorMessage }}</p> -->
        </div>
        <div class="col-md-6" *ngIf="(files.length > 0 ||invalidFiles.length > 0) && !isEdit">
          <label class="fw-bold mb-1 ml-12">Selected Attachments</label>
          <ul *ngIf="files.length > 0">
            <li *ngFor="let file of files; let i = index">
              <div class="valid">
                {{ file.name }}
                <button>
                  <i class="bi bi-x" style="cursor: pointer; color: #0f8669;font-size: 18px;"
                    (click)="removeFile(i)"></i>
                </button>
              </div>
            </li>
          </ul>
          <ul *ngIf="invalidFiles.length > 0">
            <li *ngFor="let file of invalidFiles; let i = index">
              <div class="error">
                {{ file.name }}
                <button (click)="removeInvalidFile(i)">
                  <i class="bi bi-x" style="cursor: pointer; color: red; font-size: 18px;"></i>
                </button>
              </div>
              <p style="font-size: x-small;">
                {{ file.error }}
              </p>
          </ul>
        </div>
      </div>

    </form>
    <div class="mt-3">
      <button style="margin-left:0px" type="button" id="bt_reject" class="button-back button-left"
        (click)="addApproveForm()">
        <span>{{!isAddNew ? 'ADD New Ticket':'Select Ticket'}}</span>
      </button>
    </div>
    <!-- <div class="d-flex mt-4 justify-content-start" style="gap: 10px">
      <button type="button" id="bt_approve" class="button-approve" (click)="selectedButton('approve')">
        <span class="btn-text">Approve</span>
        <img *ngIf="approveSelected" src="../../../assets/svg/accept-pink.svg" class="btn-icon" />
      </button>

      <button type="button" id="bt_approve" class="button-approve" (click)="selectedButton('reject')">
        <span class="btn-text">Reject</span>
        <img *ngIf="rejectSelected" src="../../../assets/svg/accept-pink.svg" class="btn-icon" />
      </button>
    </div> -->

    <div class="d-flex mt-4 justify-content-start" style="gap: 10px">
      <button type="submit" id="bt_submit" class="button-submit button-left" (click)="markAsApproved()">
        <span class="ps-5">Submit</span>
        <img src="../../../assets/svg/accept.svg" class="left-arrow" />
      </button>
      <button type="button" id="bt_reset" class="button-back button-left"
        (click)="this.closeModal2('lostAndFoundModal')">
        <span class="ps-5">Reset</span>
        <img src="../../../assets/svg/reject.svg" class="left-arrow" />
      </button>

    </div>

  </div>
</app-custom-modal>