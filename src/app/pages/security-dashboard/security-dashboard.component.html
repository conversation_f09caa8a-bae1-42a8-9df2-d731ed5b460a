<div class="row statistic justify-content-start grid">
    <div *ngFor="let stat of dataArray">
        <app-statistic [title]="stat.title" [count]="stat.count" [primaryColor]="stat.primaryColor"
            [secondaryColor]="stat.secondaryColor"></app-statistic>
    </div>
    <div *ngFor="let data of dataArray2" class="d-flex mt-2 title-card">
        <div class="card" (click)="redirectTo(data.url)">
            <div class="card-content">
                <span class="icon">
                    <img alt="" src="../../../assets/svg/package-svgrepo-com 1.svg" class="left-arrow" />
                </span>
                <span class="title"><b>{{data.title}}</b></span>
                <span class="icon-arrow">
                    <img alt="" src="../../../assets/svg/up (1) 1.svg" class="left-arrow" />
                </span>
            </div>
        </div>
    </div>

    <div *ngFor="let stat of dataArray_New">
        <app-statistic [title]="stat.title" [count]="stat.count" [primaryColor]="stat.primaryColor"
            [secondaryColor]="stat.secondaryColor"></app-statistic>
    </div>
    <div *ngFor="let data of dataArray2_New" class="d-flex mt-2 title-card">
        <div class="card" (click)="redirectTo(data.url)">
            <div class="card-content">
                <span class="icon">
                    <img alt="" src="../../../assets/svg/package-svgrepo-com 1.svg" class="left-arrow" />
                </span>
                <span class="title"><b>{{data.title}}</b></span>
                <span class="icon-arrow">
                    <img alt="" src="../../../assets/svg/up (1) 1.svg" class="left-arrow" />
                </span>
            </div>
        </div>
    </div>

    <!-- <div style="height: 50px"></div> -->
</div>

<div class="row graphs">
    <div class="col-8 col-md-6 ">
        <div class="card graph-card">
            <app-active-application-users-graph></app-active-application-users-graph>
        </div>
    </div>

    <div class="col-8 col-md-6 ">
        <div class="card graph-card">
            <app-incident-report-graph></app-incident-report-graph>
        </div>
    </div>
    <!-- <div style="height: 50px"></div> -->
</div>

<div class="row graphs">
    <div class="col-8 col-md-6 ">
        <div class="card graph-card">
            <app-lost-and-found-graph></app-lost-and-found-graph>
        </div>
    </div>

    <div class="col-8 col-md-6 ">
        <div class="card graph-card">
            <app-call-history-graph></app-call-history-graph>
        </div>
    </div>
    <div style="height: 50px"></div>
</div>