.dashboard-root {
    background-color: #F9F9F9;
}

.statistic {
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 20px;
    // overflow-x: scroll;
}

.grid {
    display: grid;
    grid-template-columns: 33% 33% 33%;
}

/* Card container */
.title-card {
    display: flex;
    justify-content: center;
    /* Center the card horizontally */
    margin-bottom: 10px;
    font-size: small;
}

/* Card Styling */
.card {
    background-color: #fff;
    border: 1px solid #ddd;
    /* Add a subtle border */
    border-radius: 8px;
    /* Rounded corners */
    padding: 5px;
    width: 100%;
    height: auto;
    // max-width: 350px; /* Set a max width for the card */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    /* Light shadow for depth */
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    margin-bottom: 10px;
}

/* Card content: Align images and title */
.card-content {
    display: flex;
    align-items: center;
    /* Vertically align the elements */
    justify-content: space-between;
    /* Space between icon and title */
    width: 100%;
}

/* Icon (left and right images) */
.icon {
    height: 30px;
    width: 30px;
    // display: flex;
    // justify-content: center;
    // align-items: center;
}

.icon-arrow {
    height: 20px;
    width: 20px;
}

/* Title styling */
.title {
    font-size: small;
    font-weight: bold;
    color: #333;
    margin: 0 10px;
    /* Adding margin to separate the title from icons */
}

@media (max-width: 768px) {

    .statistic .col,
    .graphs .col {
        margin-bottom: 20px;
    }
}

@media (min-width: 992px) {
    .graph-card {
        margin-bottom: 30px;
    }
}

.graphs {
    margin-top: 5px;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 10px;
}

.graph-card {
    margin-bottom: 4px;
    padding: 20px;
    padding-top: 5px;
}