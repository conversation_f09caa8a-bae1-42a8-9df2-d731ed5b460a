<div *ngIf="chartInitializedStatus" class="main">
    <div class="titleGraphStyle">Incident Report</div>
    <select [(ngModel)]="selectedPeriod" (change)="onClusterChange($event)" class="period-select" [@blink]="blinkState">
        <option class="optionStyle" *ngFor="let type of clusters" [value]="type.id">{{ type.title }}</option>
    </select>
</div>

<div id="chart" *ngIf="chartInitializedStatus">
    <apx-chart class=".apexcharts-tooltip" [series]="chartOptions!.series" [chart]="chartOptions!.chart!"
        [xaxis]="chartOptions!.xaxis!" [tooltip]="chartOptions.tooltip" [plotOptions]="chartOptions.plotOptions"
        [legend]="chartOptions?.legend" [dataLabels]="chartOptions!.dataLabels!" [colors]="chartOptions.colors"
        [title]="chartOptions!.title!">
    </apx-chart>
</div>

<div class="loading-container" *ngIf="!chartInitializedStatus">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>