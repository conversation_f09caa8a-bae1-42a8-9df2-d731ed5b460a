import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ApexAxisChartSeries, ApexDataLabels, ApexPlotOptions, ApexTitleSubtitle, ApexXAxis, ChartComponent } from "ng-apexcharts";
import {
  ApexChart,
} from "ng-apexcharts";
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { SecurityService } from '../../../services/security-module/security.service';
import { ClusterService } from '../../../services/cluster/cluster.service';
import { animate, state, style, transition, trigger } from '@angular/animations';

export type ChartOptions = {
  series: ApexAxisChartSeries | any;
  chart: ApexChart | any;
  xaxis: ApexXAxis | any;
  dataLabels: ApexDataLabels | any;
  title: ApexTitleSubtitle | any;
  plotOptions: ApexPlotOptions | any;
  colors?: any;
  tooltip: any;
  legend: any;
};

@Component({
  selector: 'app-incident-report-graph',
  templateUrl: './incident-report-graph.component.html',
  styleUrl: './incident-report-graph.component.scss',
  animations: [
    trigger('blink', [
      state('active', style({
        borderColor: '#000000',
        border: '1px solid',
        boxShadow: '0 0 10px #000000'
      })),
      state('inactive', style({
        borderColor: 'transparent',
        boxShadow: '0px',
        border: '0.5px solid',
      })),
      transition('active <=> inactive', [
        animate('5s ease-in-out')

      ])
    ])
  ]
})
export class IncidentReportGraphComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  @ViewChild('clusterSelectionRef', { static: false })
  dropdownElement!: ElementRef;
  public selectedPeriod: number = 3;
  clusters: any[] = [];
  public chartOptions!: Partial<ChartOptions>;
  blinkState: string = 'active';
  selectedClusterId: string = '';
  selectedClusterTitle: string = '';
  selectedClusterID: number = 0;

  constructor(private securityService: SecurityService, private clusterService: ClusterService) {
    this.chartOptions = {
    };
  }

  ngOnInit() {
    setTimeout(() => {
      this.blinkState = 'inactive';
    }, 3000);
    this.loadObservationStatusData(3);
    this.getClusters();
  }


  onClusterChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.selectedClusterId = selectElement.value;
    const selectedOption = this.clusters.find(cluster => cluster.id == this.selectedClusterId);
    this.selectedClusterTitle = selectedOption ? selectedOption.title : '';
    this.selectedClusterID = selectedOption ? selectedOption.id : 0;
    this.loadObservationStatusData(this.selectedClusterID);
  }


  getClusters(): Promise<void> {
    return this.clusterService.getClusters().then((response) => {
      this.clusters = response
    })
  }


  loadObservationStatusData(id: number) {
    const body = {
      clusterId: id
    }
    const params = createAxiosConfig(body);
    this.securityService.plantwiseGraph(params).then((res) => {
      if (res && res.securityIncidentGraph && Array.isArray(res.securityIncidentGraph) && res.securityIncidentGraph.length > 0) {
        const incidentReportData = res.securityIncidentGraph;
        const incidentCount = incidentReportData.map((key: any) => key.incident_count);
        const incidentPlantName = incidentReportData.map((value: any) => value.plant_name);

        this.chartOptions = {
          series: [
            {
              name: 'Count',
              data: incidentCount
            }
          ],
          chart: {
            type: 'bar',
            height: 246,
            width: 600,
            toolbar: {
              show: false
            },
            hover: {
              color: '#FFD700'
            }
          },
          colors: ['#A2C0B4'],
          title: { text: '', align: 'left' },
          xaxis: {
            categories: incidentPlantName,
            labels: {
              rotate: -45
            },
          },
          dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
              fontSize: '12px',
              colors: ["#000000"]
            },
            formatter: function(val: number) {  // Explicitly type as number
              return val !== 0 ? val : '';      // Return empty string for zero
            }
          },

          legend: {
            itemMargin: {
              horizontal: 5,
              vertical: 0
            },
          },
          plotOptions: {
            bar: {
              borderRadius: [10],
              columnWidth: '22%',
            }
          }

        };
      }
      this.chartInitializedStatus = true
    }).catch(error => {
      console.error('Error fetching cluster-wise data', error);
    });
  }

  onPeriodChange() {
    this.chartInitializedStatus = false;
    this.loadObservationStatusData(1);
  }

}
