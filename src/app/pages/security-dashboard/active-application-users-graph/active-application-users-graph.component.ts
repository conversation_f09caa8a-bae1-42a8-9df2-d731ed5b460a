import { Component, OnInit, ViewChild } from '@angular/core';
import { ApexAxisChartSeries, ApexDataLabels, ApexPlotOptions, ApexTitleSubtitle, ApexXAxis, ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  Apex<PERSON>hart
} from "ng-apexcharts";
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { SecurityService } from '../../../services/security-module/security.service';
import { ClusterService } from '../../../services/cluster/cluster.service';
import { animate, state, style, transition, trigger } from '@angular/animations';

export type ChartOptions = {
  series: ApexAxisChartSeries | any;
  chart: ApexChart | any;
  xaxis: ApexXAxis | any;
  dataLabels: ApexDataLabels | any;
  title: ApexTitleSubtitle | any;
  plotOptions: ApexPlotOptions | any;
  colors?: any;
};
@Component({
  selector: 'app-active-application-users-graph',
  templateUrl: './active-application-users-graph.component.html',
  styleUrl: './active-application-users-graph.component.scss',
  animations: [
    trigger('blink', [
      state('active', style({
        borderColor: '#000000',
        border: '1px solid',
        boxShadow: '0 0 10px #000000'
      })),
      state('inactive', style({
        borderColor: 'transparent',
        boxShadow: '0px',
        border: '0.5px solid',
      })),
      transition('active <=> inactive', [
        animate('5s ease-in-out')

      ])
    ])
  ]
})
export class ActiveApplicationUsersGraphComponent implements OnInit {

  private currentYear = new Date().getFullYear();
  private currentMonth = new Date().getMonth() + 1;
  blinkState = 'active';
  chartInitializedStatus = false;
  selectedPeriod: 'monthly' | 'yearly' | 'weekly' = 'weekly';
  chartOptions: ChartOptions = this.getInitialChartOptions();

  @ViewChild("chart") chart!: ChartComponent;

  constructor(
    private datePipe: DatePipe,
    private securityService: SecurityService,
    private clusterService: ClusterService
  ) { }

  ngOnInit(): void {
    setTimeout(() => {
      this.blinkState = 'inactive';
    }, 3000);

    this.loadInitialData();
  }

  private loadInitialData(): void {
    const initialRequestBody = this.getWeeklyRequestBody();
    this.loadChartData(initialRequestBody);
  }

  private getInitialChartOptions(): ChartOptions {
    return {
      series: [{ name: 'Count', data: [] }],
      chart: {
        type: 'bar',
        height: 246,
        width: 600,
        toolbar: { show: false }
      },
      colors: ['#004C78'],
      title: { text: '', align: 'left' },
      xaxis: { categories: [] },
      dataLabels: {
        enabled: true,
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ["#000000"]
        },
        formatter: function(val: number) {  // Explicitly type as number
          return val !== 0 ? val : '';      // Return empty string for zero
        }
      },
      plotOptions: {
        bar: {
          columnWidth: '85%',
          borderRadius: [12],
        }
      }
    };
  }

  onPeriodChange(): void {
    this.chartInitializedStatus = false;

    const requestBody = this.getRequestBodyForPeriod();
    this.loadChartData(requestBody);
  }

  private getRequestBodyForPeriod(): any {
    switch (this.selectedPeriod) {
      case 'monthly':
        return {
          type: 2,
          year: this.currentYear,
          month: this.currentMonth
        };
      case 'yearly':
        return {
          type: 1,
          year: this.currentYear
        };
      case 'weekly':
        return this.getWeeklyRequestBody();
      default:
        throw new Error(`Invalid period: ${this.selectedPeriod}`);
    }
  }

  private getWeeklyRequestBody(): any {
    const today = new Date();
    const beforeSevenDays = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000);

    return {
      type: 3,
      from: this.formatDate(beforeSevenDays),
      to: this.formatDate(today)
    };
  }


  private formatDate(date: Date): string {
    return this.datePipe.transform(date, 'yyyy-MM-dd') || '';
  }

  private getMonthName(monthNumber: number): string {
    return new Date(2000, monthNumber - 1).toLocaleString('default', { month: 'short' });
  }

  loadChartData(requestBody: any): void {
    this.securityService.activeUser(requestBody).then(response => {
      if (!response?.data) {
        throw new Error('No data received from API');
      }

      const { categories, data } = this.processResponseData(response.data);

      this.updateChartOptions(categories, data);
      this.chartInitializedStatus = true;
    }).catch(error => {
      console.error('Error loading chart data:', error);
      this.chartInitializedStatus = true; // Ensure UI doesn't get stuck
    });
  }

  private processResponseData(responseData: any[]): { categories: string[]; data: number[] } {
    const getCategory = (item: any) => {
      switch (this.selectedPeriod) {
        case 'yearly': return this.getMonthName(item.month);
        case 'monthly': return item.day.toString();
        case 'weekly': return item.weekday;
        default: return '';
      }
    };

    return {
      categories: responseData.map(getCategory),
      data: responseData.map(item => item.count)
    };
  }

  private updateChartOptions(categories: string[], data: number[]): void {
    this.chartOptions = {
      ...this.chartOptions,
      series: [{ ...this.chartOptions.series[0], data }],
      xaxis: { ...this.chartOptions.xaxis, categories }
    };
  }
}
