<div *ngIf="chartInitializedStatus" class="main">
    <div class="titleGraphStyle">Active Application Users</div>
    <select [(ngModel)]="selectedPeriod" (change)="onPeriodChange()" class="period-select" [@blink]="blinkState">
        <option value="weekly" class="optionStyle">Weekly</option>
        <option value="monthly" class="optionStyle">Monthly</option>
        <option value="yearly" class="optionStyle">Yearly</option>
    </select>
</div>


<div id="chart" *ngIf="chartInitializedStatus">
    <apx-chart [series]="chartOptions!.series" [chart]="chartOptions!.chart!" [xaxis]="chartOptions!.xaxis!"
        [plotOptions]="chartOptions.plotOptions" [dataLabels]="chartOptions!.dataLabels!" [colors]="chartOptions.colors"
        [title]="chartOptions!.title!">
    </apx-chart>
</div>

<div class="loading-container" *ngIf="!chartInitializedStatus">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>