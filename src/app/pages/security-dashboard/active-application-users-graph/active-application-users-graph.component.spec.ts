import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ActiveApplicationUsersGraphComponent } from './active-application-users-graph.component';

describe('ActiveApplicationUsersGraphComponent', () => {
  let component: ActiveApplicationUsersGraphComponent;
  let fixture: ComponentFixture<ActiveApplicationUsersGraphComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ActiveApplicationUsersGraphComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ActiveApplicationUsersGraphComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
