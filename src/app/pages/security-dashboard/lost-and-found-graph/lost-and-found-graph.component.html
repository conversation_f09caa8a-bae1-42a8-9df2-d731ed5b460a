<div *ngIf="chartInitializedStatus" class="main">
    <div class="titleGraphStyle">Cluster Wise BOG Tour</div>
</div>

<div id="chart" *ngIf="chartInitializedStatus">
    <apx-chart [series]="chartOptions.series" [chart]="chartOptions.chart" [labels]="chartOptions.labels"
        [plotOptions]="chartOptions.plotOptions" [responsive]="chartOptions.responsive"
        [dataLabels]="chartOptions.dataLabels" [legend]="chartOptions?.legend" [colors]="chartOptions?.colors">
    </apx-chart>
</div>
<div class="loading-container" *ngIf="!chartInitializedStatus">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>