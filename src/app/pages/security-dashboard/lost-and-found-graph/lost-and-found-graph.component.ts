import { Component, OnInit, ViewChild } from '@angular/core';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { ApexChart, ApexNonAxisChartSeries, ApexPlotOptions, ApexResponsive, ChartComponent } from 'ng-apexcharts';
import { SecurityService } from '../../../services/security-module/security.service';
import { DatePipe } from '@angular/common';

export type ChartOptions = {
  series: ApexNonAxisChartSeries | any;
  chart: ApexChart | any;
  responsive: ApexResponsive[] | any;
  labels: any;
  title: any;
  dataLabels?: any;
  tooltip?: any;
  colors?: any;
  legend: any;
  plotOptions: ApexPlotOptions | any;
};

@Component({
  selector: 'app-lost-and-found-graph',
  templateUrl: './lost-and-found-graph.component.html',
  styleUrl: './lost-and-found-graph.component.scss'
})
export class LostAndFoundGraphComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  public chartOptions!: Partial<ChartOptions>;

  constructor(private datePipe: DatePipe, private securityService: SecurityService) {
    this.chartOptions = {}
  }

  ngOnInit() {
    this.getDate();
    this.getTourObservationCount();
  }

  getDate() {
    const today = new Date();
    const before30Days = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    return {
      from: before30Days,
      to: today
    };
  }


  getTourObservationCount() {
    const body = this.getDate();
    this.securityService.lostandfoundGraph(body).then((res: any) => {

      if (res && res.data) {
        const clusterData = res.data;

        this.chartOptions.series = [parseInt(clusterData[0].lost, 10), parseInt(clusterData[0].found, 10)]
        this.chartOptions.labels = ['Lost', 'Found'];

        this.chartOptions.dataLabels = {
          enabled: true,
          formatter: (value: any, options: any) => {
            return options.w.config.series[options.seriesIndex];
          },
          style: {
            fontSize: '12px',
            fontWeight: '400',
            position: 'center',
            colors: ['#000000'],
          }
        };

        this.chartOptions.chart = {
          type: 'donut',
          height: '266px',
          width: '275px',
        };

        this.chartOptions.legend = {
          position: 'bottom',
          horizontalAlign: 'center',
          floating: false,
          fontSize: '12px',
          labels: {
            colors: ['#000'],
            useSeriesColors: false
          }
        };

        this.chartOptions.plotOptions = {
          pie: {
            donut: {
              size: "35%",
              columnWidth: '50%',
              borderRadius: 8
            }
          }
        };

        this.chartOptions.responsive = [
          {
            breakpoint: 180,
            options: {
              chart: {
                width: 74,
                height: 74
              },
              legend: {
                position: 'center'
              }
            }
          }
        ];

        this.chartOptions.colors = ['#C2246B', '#1786D3'],

          this.chartInitializedStatus = true;
      } else {
        console.error('Unexpected response format', res);
      }
    }).catch(error => {
      console.error('Error fetching cluster-wise data', error);
    });
  }
}
