import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LostAndFoundGraphComponent } from './lost-and-found-graph.component';

describe('LostAndFoundGraphComponent', () => {
  let component: LostAndFoundGraphComponent;
  let fixture: ComponentFixture<LostAndFoundGraphComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [LostAndFoundGraphComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(LostAndFoundGraphComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
