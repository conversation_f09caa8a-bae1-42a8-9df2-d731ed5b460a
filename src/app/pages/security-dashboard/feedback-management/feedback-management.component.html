<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>

<div class="header-container">
  <p class="fw-bold" style="margin-top: -8px">Feedback Management</p>
</div>

<div class="outer-container mt-3">
  <div class="parent-container">
    <app-search-filter [filters]="searchInputs"  (search)="handleSearch($event)"
      (reset)="resetSearch($event)"></app-search-filter>
    <div>
      <table style="width: 100% !important;" mat-table [dataSource]="dataSource" class="mat-elevation-z8 ">
        <ng-container matColumnDef="srNo">
          <th style="width:70px" mat-header-cell *matHeaderCellDef> Sr No. </th>
          <td mat-cell *matCellDef="let element; let i = index">
            {{ dataSource.data.indexOf(element) + 1 }}
          </td>
        </ng-container>

        <ng-container matColumnDef="User">
          <th mat-header-cell *matHeaderCellDef> User Name </th>
          <td mat-cell *matCellDef="let element"> {{ element.admin ? element.admin.firstName +' '+
            element.admin.lastName : 'NA' }} </td>
        </ng-container>

        <ng-container matColumnDef="siteName">
          <th mat-header-cell *matHeaderCellDef> Site Name </th>
          <td mat-cell *matCellDef="let element"> {{ element?.plant?.name ?element.plant.name :'NA'}} </td>
        </ng-container>
        <ng-container matColumnDef="desc">
          <th mat-header-cell *matHeaderCellDef> Description </th>
          <td mat-cell *matCellDef="let element"> {{ element?.desc ?element.desc :'NA'}} </td>
        </ng-container>
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef> Status </th>
          <td mat-cell *matCellDef="let element"> {{ element?.status ? element.status :'NA'}} </td>
        </ng-container>

        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef> Category </th>
          <td mat-cell *matCellDef="let element"> {{ element?.category ? element.category :'NA'}} </td>
        </ng-container>

        <ng-container matColumnDef="status_reason">
          <th mat-header-cell *matHeaderCellDef> Feedback Reason </th>
          <td mat-cell *matCellDef="let element"> {{ element?.rating ?element.rating :'NA'}} </td>
        </ng-container>

        <tr style="width:100% !important" mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let element; columns: displayedColumns;" class="example-element-row">
        </tr>
      </table>
      <div *ngIf="!dataSource || dataSource?.data?.length === 0" class="no-data-found">
        <p>No data found</p>
      </div>
    </div>
    <mat-paginator [pageSizeOptions]="[5, 10, 20]" [pageSize]="5" [length]="dataSource.data.length" showFirstLastButtons
      aria-label="Select page of periodic elements">
    </mat-paginator>
    <div style="height: 50px"></div>
  </div>
</div>