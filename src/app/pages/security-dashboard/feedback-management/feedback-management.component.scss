.table {
  height: auto;
  overflow: auto;
}

.table th {
  position: sticky;
  top: 0;
  z-index: 1;
  // background-color: white; /* Or any background color matching the table design */
}

.table {
  position: relative;
  overflow: hidden;
  bottom: 0;
  margin-bottom: 0px;
}

.table mat-paginator {
  position: sticky;
  bottom: 0;
  top: 0;
  background-color: white;
  /* To prevent overlap issues */
  z-index: 2;
  /* Ensure it appears above the table content */
}

.mat-header-cell,
.mat-cell {
  text-align: left;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner-container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-data-found {
  text-align: center;
  margin-top: 20px;
  font-size: 16px;
  color: #888;
}

td,
th {
  white-space: normal;
  word-wrap: break-word;
  max-width: 400px;
}