import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BreadcrumbComponent } from '../../../common/breadcrumb/breadcrumb.component';
import { BreadcrumbService } from '../../../common/breadcrumb/breadcrumb.service';
import { MatTableDataSource } from '@angular/material/table';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { SecurityService } from '../../../services/security-module/security.service';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { ToastMessageComponent } from '../../../common/toast-message/toast-message.component';
import { PlantService } from '../../../services/plant/plant.service';
import { FormService } from '../../../common/Services/form-service.service';

@Component({
  selector: 'app-feedback-management',
  templateUrl: './feedback-management.component.html',
  styleUrl: './feedback-management.component.scss'
})
export class FeedbackManagementComponent {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  clickLoading: boolean = false;
  pageIndex: number = 0;
  pageSize: number = 5;
  feedbackData: any[] = []
  plants: any[] = [];
  clusters: any[] = []
  dataSource = new MatTableDataSource(this.feedbackData);
  displayedColumns: string[] = [
    'srNo',
    'User',
    'siteName',
    'status',
    'status_reason',
    'category',
    'desc',
  ];
  statusList = [
    { id: 0, name: 'Active' },
    { id: 1, name: 'Close' },
    { id: 2, name: 'Hold' },
    { id: 3, name: 'Cancel' }
  ];
  categoryList = [{
    id: 0, name: 'SOS'
  },
  {
    id: 1, name: 'MAP'
  },
  {
    id: 2, name: 'INCIDENT REPORT'
  },
  {
    id: 3, name: 'LOST & FOUND'
  },
  {
    id: 4, name: 'OTHERS'
  }

  ]
  feedbackReasonList = [{ id: 0, name: 'Sad' }, { id: 1, name: 'Normal' }, { id: 2, name: 'Happy' }]
  searchInputs = [
    { key: 'status', type: 'dropdown', label: 'Status', options: this.statusList, valueField: 'id', titleField: 'name' },
    { key: 'siteName', type: 'dropdown', label: 'Site Name', options: this.plants, valueField: 'id', titleField: 'name' },
    {
      key: 'date', type: 'date', label: 'Date', options: [], valueField: 'id',
      titleField: 'title'
    },
    { key: 'feedback_reason', type: 'dropdown', label: 'Feedback Reason', options: this.feedbackReasonList, valueField: 'id', titleField: 'name' },
    { key: 'category', type: 'dropdown', label: 'Category', options: this.categoryList, valueField: 'id', titleField: 'name' }
  ];


  searchResults: any = {};
  constructor(private router: Router,
    private breadcrumb: BreadcrumbService,
    private SecurityService: SecurityService,
    private formService: FormService,
    private plantService: PlantService) { }

  ngOnInit() {
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
    this.fetchData()
    this.getFeedbackCount()
  }
  resetSearch(event: any) {
    this.searchResults = {};
    this.getFeedbackCount()
  }
  getFeedbackCount(data?: any) {
    if (!data) {
      data = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: [
          'enabled||eq||true'
        ]
      }
    }
    const param = createAxiosConfig(data);
    this.clickLoading = true;
    this.SecurityService.getFeedBack(param).then(response => {
      if (response && response.data) {
        this.feedbackData = response.data
        this.dataSource.data = this.feedbackData.map((ele: any) => ({ ...ele, status: this.statusList.find((d) => d.id === ele.status)?.name, rating: this.feedbackReasonList.find((d) => d.id === ele.rating)?.name }))
        this.clickLoading = false
      } else {
        this.toast.showErrorToast('An unknown error occurred while loading data', 6000);
        this.clickLoading = false;
      }
    }).catch((error: any) => {
      this.clickLoading = false;
      const errorMessage = error?.message || 'An unknown error occurred while loading data.';

    });
  }
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex ? event.pageIndex : 0;
    this.pageSize = event.pageSize;
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;

  }
  handleSearch(event: any): void {
    this.clickLoading = true
    this.searchResults = event;
    const data = {
      page: 1,
      limit: 10000,
      sort: 'id,DESC',
      filter: [
        'enabled||eq||true'
      ]
    }
    data.filter.pop();
    console.log('event', event);

    if (this.searchResults.siteName) {
      data.filter.push(`plantId||eq||${this.searchResults?.siteName}`);
    }
    // if (this.searchResults.date) {
    //   this.searchResults.date = this.formService.convertDate(this.searchResults.date)
    //   data.filter.push(`date||eq||${this.searchResults?.date}`);
    // }
    if (this.searchResults.feedback_reason) {
      data.filter.push(`rating||eq||${this.searchResults?.feedback_reason}`);
    }
    if (this.searchResults.status) {
      data.filter.push(`status||eq||${this.searchResults?.status}`);
    }
    if (this.searchResults.category) {
      data.filter.push(`category||eq||${this.searchResults?.category}`);
    }

    console.log(this.searchResults)
    this.getFeedbackCount(data)
  }

  getAllPlant(): Promise<void> {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]
    }
    const param = createAxiosConfig(data);
    return this.plantService.getAllPlants(param).then((response: any) => {
      this.plants = [...response.data, this.plants]
    })
  }

  async fetchData(): Promise<void> {
    try {

      await Promise.all([this.getAllPlant()]);
      this.searchInputs = [
        { key: 'status', type: 'dropdown', label: 'Status', options: [{ id: 0, name: 'Active' }, { id: 1, name: 'Close' }, { id: 2, name: 'Hold' }, { id: 3, name: 'Cancel' }], valueField: 'id', titleField: 'name' },
        { key: 'siteName', type: 'dropdown', label: 'Site Name', options: this.plants, valueField: 'id', titleField: 'name' },
        {
          key: 'date', type: 'date', label: 'Date', options: [], valueField: 'id',
          titleField: 'title'
        },
        { key: 'feedback_reason', type: 'dropdown', label: 'Feedback Reason', options: [{ id: 0, name: 'Happy' }, { id: 1, name: 'Normal' }, { id: 2, name: 'Sad' }], valueField: 'id', titleField: 'name' },
        { key: 'category', type: 'dropdown', label: 'Category', options: this.categoryList, valueField: 'id', titleField: 'name' }
      ];
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }

}
