import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { AdminModel } from '../../models/admin.model';
import { StandByEquipmentsService } from '../../services/stand-by-equipments/stand-by-equipments.service';
import { UploadService } from '../../services/shared/upload.service';
import { ExcelExportService } from '../../services/shared/excel-export/excel-export.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { Router } from '@angular/router';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { MatPaginator } from '@angular/material/paginator';
import { ToastMessageComponent } from '../../common/toast-message/toast-message.component';
import { formatDate } from '@angular/common';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import saveAs from 'file-saver';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';

declare var bootstrap: any;

@Component({
  selector: 'app-bulk-upload-excel',
  templateUrl: './bulk-upload-excel.component.html',
  styleUrl: './bulk-upload-excel.component.scss',
  animations: [
    trigger('arrowMove', [
      state('start', style({ transform: 'translateX(0)' })),
      state('moving', style({ transform: 'translateX(120px)' })), // adjust 120px as needed
      transition('start => moving', [animate('400ms cubic-bezier(.4,0,.2,1)')]),
      transition('moving => start', [animate('0ms')]),
    ]),
  ],
})
export class BulkUploadExcelComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  clickLoading: boolean = false;
  selectedFile: File | null = null;
  public admin: AdminModel | null = null;
  isUploading = false;
  input?: HTMLInputElement;
  profileImageUrl: any;
  uploadForm: FormGroup;
  filteredBulkUploadData: any[] = [];
  getbulkUploadData: any[] = [];
  dataSource = new MatTableDataSource(this.filteredBulkUploadData);
  setSelectedVersion: number = 0;
  highlightVersion: number | null = null;
  userData: any;
  isDragging = false;
  invalidFiles: any[] = [];
  pdfCount: number = 0;
  imageCount: number = 0;
  files: File[] = [];
  confirmBackupData: any;
  invalidFile: { name: string; error: string } | null = null;
  arrowState = 'start';
  isSuperAdmin: boolean = false;
  plantId: any;
  allowedFileTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
  ]; // Excel

  displayedColumns: string[] = [
    'srNo',
    'fileName',
    'version',
    'backup',
    'uploadByAdmin',
    'uploadTime',
    'downloadfile',
    'backupicon',
  ];

  searchInputs = [
    {
      key: 'version',
      type: 'text',
      label: 'Version',
      valueField: 'versionNo',
      titleField: 'versionNo',
    },
    {
      key: 'fromdate',
      type: 'date',
      label: 'From Date',
      options: [],
      valueField: 'fromdateid',
      titleField: 'fromDate',
    },
    {
      key: 'todate',
      type: 'date',
      label: 'To Date',
      options: [],
      valueField: 'todateid',
      titleField: 'toDate',
    },
  ];

  tableColumns = [
    { key: 'srNo', header: 'Sr No.', width: '90px' },
    { key: 'fileName', header: 'File Name', width: '350px' },
    { key: 'version', header: 'Version', width: '10px' },
    { key: 'backup', header: 'Backup Version', width: '150px' },
    { key: 'uploadByAdmin', header: 'Uploaded by admin', width: '200px' },
    { key: 'uploadTime', header: 'Uploaded Date & Time', width: '200px' },
    { key: 'downloadfile', header: 'Download',width:'70px' },
    { key: 'backupicon', header: 'Backup / Rollback', width: '180px' },
  ];

  constructor(
    private router: Router,
    private equipmentService: StandByEquipmentsService,
    private uploadService: UploadService,
    private excelExportService: ExcelExportService,
    private fb: FormBuilder,
    private breadcrumb: BreadcrumbService,
    private adminPlantSelection: AdminPlantSelectionService
  ) {
    this.uploadForm = this.fb.group({
      excelFile: [null, Validators.required],
    });
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  ngOnInit() {
    const role = localStorage.getItem('userRole');
    if (role == 'superadmin') this.isSuperAdmin = true;
    this.userData = localStorage.getItem('user');
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$
        .pipe(filter((plantId) => !!plantId))
        .subscribe((adminPlantId) => {
          this.plantId = adminPlantId;
          this.breadcrumb.setBreadcrumbUrl(this.router.url);
          this.getUploadedFiles(this.userData);
          this.selectedFile=null;
        });
    }
    else {
      this.getUploadedFiles(this.userData);
      this.breadcrumb.setBreadcrumbUrl(this.router.url);
    }
    if (this.userData) {
      try {
        this.admin = JSON.parse(this.userData) as AdminModel;
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    } else {
      console.warn('No user found in localStorage');
    }

  }

  // get all uploaded files to display in table
  async getUploadedFiles(userData: any) {
    try {
      this.clickLoading = true;
      let plantId;
      if (this.isSuperAdmin) {
        plantId = this.plantId;
      }
      else plantId = JSON.parse(userData).plantIds[0];


      const plantConfig = {
        page: 1,
        limit: 10000,
        sort: 'id,DESC',
        filter: [`plantId||eq||${plantId}`],
      };

      const data = await this.fetchUploadFiles(
        plantConfig,
        'file retrieval',
        true
      );
      this.updateTableData(data);
      this.getbulkUploadData = data;
    } catch (error) {
      // Error already handled in fetchUploadFiles
    }
  }

  private updateTableData(apiData: any[]) {
    this.filteredBulkUploadData = this.transformData(apiData);
    this.dataSource.data = this.mapTableResponse(apiData);
  }

  private transformData(apiResponse: any[]): any[] {
    console.log('apiResponse', apiResponse);


    this.highlightVersion = this.getHighestVersion(apiResponse);
    return apiResponse
      .filter(
        (item) =>
          typeof item?.fileURL === 'string' && item.fileURL.trim() !== ''
      )
      .map((item, index, filteredArray) => ({
        srNo: filteredArray.length - index,
        fileName: item?.filename,
        version: item?.version,
        backup: item?.backup ? item?.backup : 'NA',
        uploadByAdmin: `${item?.admin?.firstName} ${item?.admin?.lastName}`,
        uploadTime: this.convertDate(item?.uploadtime),
        downloadfile: null,
        fileURL: item?.fileURL,
        backupicon: null,
        id: item?.id,
      }));
  }

  private getHighestVersion(objects: any): number {
    console.log('objects', objects);
    if (objects.length == 0) {
      return 1;
    }
    else return Math.max(...objects.map((obj: any) => obj.version)) + 1;
  }

  private mapTableResponse(data: any[]): any[] {
    return data.map((item) => ({
      ...item,
      uploadtime: this.convertDate(item.uploadtime),
    }));
  }

  private convertDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      // Use UTC getters to avoid local timezone conversion
      const day = String(date.getUTCDate()).padStart(2, '0');
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const year = date.getUTCFullYear();
      const hours = String(date.getUTCHours()).padStart(2, '0');
      const minutes = String(date.getUTCMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${day}-${month}-${year} | ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.error('Date conversion error:', error);
      return 'Invalid Date';
    }
  }

  // search/filter data
  private async fetchUploadFiles(
    config: any,
    contextDescription: string,
    dataInResponse: boolean = false
  ): Promise<any> {
    try {
      const params = createAxiosConfig(config);
      const response = await this.uploadService.getUploadFiles(params);

      const responseData = dataInResponse ? response?.data : response;

      if (!responseData) {
        throw new Error(`No data received in ${contextDescription}`);
      }

      return responseData;
    } catch (error) {
      console.error(`Error in ${contextDescription}:`, error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'An unknown error occurred while loading data';

      this.toast.showErrorToast(errorMessage, 6000);
      throw error;
    } finally {
      this.clickLoading = false;
    }
  }

  // search/filter data in table
  async handleSearch(event: any) {
    try {
      this.clickLoading = true;
      const { fromdate, todate, version } = event;

      const searchConfig = {
        filter: [
          `createdTimestamp||$gte||{${this.formatToISO(new Date(fromdate))}}`,
          `createdTimestamp||$lte||{${this.formatToISO(new Date(todate))}}`,
          `version||$in||${version}`,
        ],
      };

      const data = await this.fetchUploadFiles(
        searchConfig,
        'search operation',
        false
      );
      this.updateTableData(data);
    } catch (error) { }
  }

  private formatToISO(date: Date): string {
    const now = new Date();
    const utcDate = new Date(
      Date.UTC(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        now.getHours(),
        now.getMinutes(),
        now.getSeconds(),
        now.getMilliseconds()
      )
    );
    return utcDate.toISOString();
  }

  // upload button click
  onSubmitExcel(): void {
    if (this.selectedFile) {
      const fileName = this.selectedFile.name;
      let plantName;
      if (this.isSuperAdmin) {
        plantName = localStorage.getItem('currPlantName');
      }

      const currentPlantName = this.isSuperAdmin ? plantName || '' : this.admin?.plant[0].name || '';
      const regex = new RegExp(
        `^wbi-checkpoint-temple-${currentPlantName}\\.xlsx$`,
        'i'
      );
      // -${this.highlightVersion}

      if (!regex.test(fileName)) {
        this.toast.showErrorToast(
          'Please upload a valid file for the plant: ' + `${currentPlantName}`,
          5000
        );
        return;
      }
      this.clickLoading = true;
      this.isUploading = true;
      const formData = new FormData();
      formData.append('filedata', this.selectedFile);
      formData.append('plantId', `${this.isSuperAdmin ? this.plantId : this.admin?.plantIds[0]}`);
      formData.append('adminId', `${this.admin?.id}`);
      formData.forEach((value, key) => { });

      this.equipmentService
        .bulkUplodEquipments(formData)
        .then(async (response: any) => {
          this.isUploading = false;
          if (response.responseCode == 200) {
            const uploadedExcelResponse = response;
            // this.downloadExcelFromBase64(response.fileData, `wbi-checkpoint-temple-${this.admin?.plant[0].name}.xlsx`);
            // Close the modal
            const modalElement = document.getElementById('uploadModal');
            const modalInstance = bootstrap.Modal.getInstance(modalElement);
            if (modalInstance) {
              modalInstance.hide();
            }
            let uploadedURL = await this.uploadProfilePic();
            this.fileUploadHistoryCall(uploadedExcelResponse, uploadedURL);
          }
          if (response.responseCode == 400) {
            this.toast.showErrorToast(
              response.message,
              4000
            );
          }
          this.clickLoading = false;
        })
        .catch((error) => {
          console.error('Upload failed:', error);
          this.clickLoading = false;
          this.toast.showErrorToast(
            error.message,
            4000
          );
        });
    }
  }

  fileUploadHistoryCall(response: any, uploadedURL: any) {
    let body = {
      tableName: 'wbi-backup-checkpoint',
      id: response.backupId,
      data: {
        fileURL: uploadedURL,
      },
    };

    this.uploadService
      .fileUploadHistory(body)
      .then(async (response: any) => {
        if (response) {
          this.clickLoading = false;
          this.fileInput.nativeElement.value = '';
          this.toast.showSuccessToast('Uploaded Successully');
          this.resetUploadState();
          this.getUploadedFiles(this.userData);
        }
      })
      .catch((error) => {
        console.error('backup failed:', error);
      });
  }

  async uploadProfilePic(): Promise<string> {
    let url = '';
    if (this.selectedFile) {
      const formData = new FormData();
      formData.append('file', this.selectedFile);

      try {
        url = await this.uploadService.uploadImage(formData);
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }
    return url;
  }

  async downloadExcelFromUrl(downloadUrlData: any): Promise<void> {
    const currentPlantName = this.admin?.plant?.[0]?.name ?? 'unknown-plant';
    const dynamicFileName = `wbi-checkpoint-temple-${currentPlantName}.xlsx`;
    // -${downloadUrlData?.version}

    try {
      if (!downloadUrlData?.id) {
        throw new Error('Invalid download data: missing id');
      }

      const body = { id: downloadUrlData.id };

      const blob: Blob = await this.uploadService.getExcelFile(body);

      if (!(blob instanceof Blob)) {
        throw new Error('Received data is not a Blob');
      }
      const fileBlob = new Blob([blob], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(fileBlob);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', dynamicFileName);
      document.body.appendChild(link);

      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Download failed:', error.message);
      } else {
        console.error('Download failed with unknown error:', error);
      }
    }
  }

  downloadExcelFromBase64(base64Data: string, fileName: string): void {
    try {
      const base64Content = base64Data.replace(/^data:.+;base64,/, '');

      const binaryString = window.atob(base64Content);
      const byteArray = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        byteArray[i] = binaryString.charCodeAt(i);
      }

      // Create Blob and download link
      const blob = new Blob([byteArray], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      // Clean up
      URL.revokeObjectURL(downloadLink.href);
    } catch (error) {
      console.error('Error downloading Excel file:', error);
      // You might want to show an error message to the user here
    }
  }

  resetSearch(data: any) {
    this.getUploadedFiles(this.userData);
  }

  // Handle file input (Browse)
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      this.handleFile(input.files[0]);
    }
  }

  // Handle drag over
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragging = true;
  }

  // Handle drag leave
  onDragLeave(): void {
    this.isDragging = false;
  }

  // Handle drop
  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragging = false;
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      this.handleFile(event.dataTransfer.files[0]);
    }
  }

  // Main file handler (single file)
  handleFile(file: File): void {
    console.log(file,'file');
    
    this.selectedFile = null;
    this.invalidFile = null;
    if (this.allowedFileTypes.includes(file.type)) {
      this.selectedFile = file;
    } else {
      this.invalidFile = {
        name: file.name,
        error: 'This document is not supported. Please select another file.',
      };
    }
  }

  // Remove valid file
  removeFile(): void {
    this.selectedFile = null;
    this.fileInput.nativeElement.value = '';
  }

  // Remove invalid file
  removeInvalidFile(): void {
    this.invalidFile = null;
    this.fileInput.nativeElement.value = '';
  }

  // Reset all
  resetUploadState(): void {
    this.selectedFile = null;
    this.invalidFile = null;
    this.fileInput.nativeElement.value = '';
  }

  uploadFiles(): void {
    if (!this.selectedFile) return;
    this.onSubmitExcel();
  }

  backupRollBackClick(data: any) {
    this.confirmBackupData = data;
    this.setSelectedVersion = data.version;
    const modalElement = document.getElementById('deleteModal');
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
  }

  onButtonClick() {
    this.arrowState = 'moving';
    setTimeout(() => {
      this.arrowState = 'start';
      this.comfirmVersionNo();
      this.closeConfirmModal();
    }, 600);
  }

  comfirmVersionNo() {
    this.clickLoading = true;
    const filteredRecords = this.getbulkUploadData.filter(
      (item) => item.version === this.confirmBackupData.version
    );

    let body = {
      plantId: filteredRecords[0]?.plantId,
      adminId: filteredRecords[0]?.adminId,
      id: filteredRecords[0]?.id,
    };

    this.equipmentService
      .backupUploadFile(body)
      .then(async (response: any) => {
        if (response.responseCode == 200) {
          this.clickLoading = false;
          this.toast.showSuccessToast(
            'Backup / Rollback completed successfully'
          );

          this.getUploadedFiles(this.userData);
        } else {
          this.clickLoading = false;
          this.toast.showErrorToast('Backup / Rollback Failed');
          this.closeConfirmModal();
        }
      })
      .catch((error) => {
        console.error('Response failed:', error);
      });
  }

  closeConfirmModal() {
    const modalElement = document.getElementById('deleteModal');
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
      modalInstance.hide();
    }
  }

  downloadfile() {
    let plant;
    if (this.isSuperAdmin) {
      plant = localStorage.getItem('currPlantName');
    }
    this.excelExportService.exportToExcel(
      `wbi-checkpoint-temple-${this.isSuperAdmin? plant: this.admin?.plant[0].name}`
    );
  }
  // ${this.highlightVersion}
}
