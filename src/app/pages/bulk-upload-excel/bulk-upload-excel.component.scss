.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-data-found {
    text-align: center;
    margin-top: 20px;
    font-size: 16px;
    color: #888;
}

.gradient-header {
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
    color: white;
    /* Optional: Make text white for better contrast */
    padding: 1rem;
    /* Optional: Adjust padding for aesthetics */
    border-top-left-radius: 0.3rem;
    /* Match modal border radius */
    border-top-right-radius: 0.3rem;
}

.templateStyle {
    background-color: #DAECF6;
    border-radius: 15px;
    height: 85px;
    display: none;
}

.fileStyle {
    width: 500px;
}

.closeBtnStyle {
    margin-top: 25px;
}

.uploadBtnStyle {
    margin-left: 5px;
}

.tableStyle {
    width: 100% !important;
    margin-top: 15px;
}

.highlighted-row {
    background-color: #FFFF00;
    /* Light yellow background */
    transition: background-color 0.3s ease;
}

.download-wrapper {
    background-color: #dbeef9;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.newtemplateStyle {
    background-color: #DAECF6;
    border-radius: 15px;
    padding: 1.5rem;
    min-height: 162px;
    // height: 162px;
    display: flex;
    align-items: center;
}

.container {
    width: 100%;
    padding: 0 15px;
}

.content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
}

.download-btn {
    height: 40px;
    display: inline-flex;
    align-items: center;
    background: white;
    border: 1px solid #BD3861;
    border-radius: 50px;
    // padding: 0 20px;
    font-weight: 400;
    color: #BD3861;
    font-family: Arial, sans-serif;
    position: relative;
    margin-left: 35px;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.download-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url('data:image/svg+xml;utf8,<svg fill="white" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M19 4H5v2h14V4zm-7 18 5.5-5.5h-4v-6h-3v6h-4L12 22z"/></svg>') no-repeat center center;
    background-size: contain;
}

.icon-circle {
    background-color: #BD3861;
    border-radius: 50%;
    padding: 8px;
    margin-left: -7px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}

/* Keep existing .icon-circle and .download-icon styles */
/* Add media queries for responsiveness */

@media (max-width: 768px) {
    .newtemplateStyle {
        height: auto;
        padding: 1rem;
    }

    .download-btn {
        margin-top: 1rem;
    }

    .d-flex.align-items-center {
        flex-direction: column;
        // text-align: center;
    }
}

// @media (max-width: 576px) {
//     .download-btn {
//         width: 100%;
//         justify-content: center;
//     }

//     .btn-text {
//         font-size: 13px;
//     }
// }


.upload-box {
    padding: 20px;
    width: 470px;
    height: 115px;
    text-align: center;
    border: 1px dashed #4796C3;
    border-radius: 5px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    &.dragging {
        background-color: #e3f2fd;
    }

    p {
        font-size: 14px;
        color: #555;
    }
}

ul {
    list-style: none;
    padding: 0;

    li {
        div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px;
            border-radius: 5px;
            margin-top: 5px;
        }

        .valid {
            border: 1px solid #6ACD75;
            color: #6ACD75;
        }

        .error {
            border: 1px solid #E95454;
            color: #E95454;
        }

        button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }

        p {
            margin: 4px 0 0 0;
        }
    }
}

.invalidFileStyle {
    margin-bottom: 0px;
    margin-top: 25px;
}

.uploadedFileStyle {
    margin-bottom: -8px;
}

.button-group {
    display: flex;
    gap: 10px;

    button {
        min-width: 80px;
    }
}

.file-list {
    min-height: 140px;
    min-width: 350px;
    /* Adjust as needed */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.button-group {
    margin-top: auto !important;
    /* Pushes buttons to bottom */
    padding-top: 1rem;
    /* Optional spacing */
}

.bgCardStyle {
    background-color: #FFFFFF;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 10px;
    overflow-y: auto;
}

.uploadFileTitle {
    color: #0B74B0;
}

.pageTitleStyle {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    margin-left: 50%;
}

.browseStyle {
    color: #0B74B0;
    cursor: pointer;
    text-decoration: underline;
}

.modal-content {
    width: 90%;
}

.uploadedFileStyle div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    border-radius: 5px;
    margin-top: 5px;
}

.uploadedFileStyle .valid {
    border: 1px solid #6ACD75;
    color: #6ACD75;
}

.invalidFileStyle div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    border-radius: 5px;
    margin-top: 5px;
}

.invalidFileStyle .error {
    border: 1px solid #E95454;
    color: #E95454;
}

.uploadedFileStyle button,
.invalidFileStyle button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.uploadedFileStyle p,
.invalidFileStyle p {
    margin: 4px 0 0 0;
}


.newtemplateStyle {
    background-color: #DAECF6;
    border-radius: 15px;
    height: 150px;
}

.button-submit {
    position: relative;
    overflow: hidden;
}

.left-arrow {
    transition: transform 400ms cubic-bezier(.4, 0, .2, 1);
    // margin-right: 8px;
    vertical-align: middle;
    margin-left: -2px;
}

.rejectStyle {
    vertical-align: middle;
    height: 57px;
    width: 70px;
}

.btn-close {
    background-color: white;
}

.dialogTitle {
    margin-left: 147px;
}

.templateLabel {
    text-align: center;
}

.templateContent {
    text-align: left;
}

@media only screen and (min-width: 900px) and (max-width: 1332px) {
    .templateContent {
        text-align: left;
        width: 220px;
    }

    .newtemplateStyle {
        background-color: #DAECF6;
        border-radius: 15px;
        padding: 1.5rem;
        min-height: 170px;
        display: flex;
        align-items: center;
    }

}