<app-toast-message></app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>

<div class="outer-container mt-3">
  <label class="pageTitleStyle">Upload Checklist</label>
  <div class="parent-container">
    <div>
      <form (ngSubmit)="onSubmitExcel()" #uploadForm="ngForm">
        <div>
          <div class="modal-body">
            <!-- <div class="alert alert-info mb-3 templateStyle">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <div>
                                        <strong>Need a template?</strong>
                                        <p class="mb-2">Please download and use our sample Excel template for correct
                                            formatting.</p>
                                    </div>
                                </div>
                                <div class="download-wrapper">
                                    <button class="download-btn" (click)="downloadfile()">
                                        <span class="icon-circle">
                                            <i class="download-icon"></i>
                                        </span>
                                        <span class="btn-text">Download Template</span>
                                    </button>
                                </div>
                            </div>
                        </div> -->

            <div class="d-flex gap-4 align-items-start bgCardStyle">
              <div class="upload-box-container">
                <label class="fw-bold mb-1 uploadFileTitle">Bulk upload</label>
                <div
                  class="upload-box"
                  [class.dragging]="isDragging"
                  (dragover)="onDragOver($event)"
                  (dragleave)="onDragLeave()"
                  (drop)="onDrop($event)"
                >
                  <div>
                    <img
                      style="width: 5%"
                      alt=""
                      src="../../../assets/svg/upload-plain.svg"
                    />
                  </div>
                  <p>
                    <b>
                      Drag & Drop files here or
                      <a (click)="fileInput.click()" class="browseStyle"
                        >Browse</a
                      >
                    </b>
                  </p>
                  <p style="font-size: xx-small">Supported formats: Excel</p>
                  <input
                    type="file"
                    (change)="onFileSelected($event)"
                    multiple
                    hidden
                    #fileInput
                  />
                </div>
              </div>

              <!-- Uploading File List -->
              <div class="file-list">
                <div *ngIf="invalidFile" class="invalidFileStyle">
                  <div class="error">
                    {{ invalidFile.name }}
                    <button (click)="removeInvalidFile()">
                      <i
                        class="bi bi-x"
                        style="cursor: pointer; color: red; font-size: 18px"
                      ></i>
                    </button>
                  </div>
                  <p style="font-size: x-small">{{ invalidFile.error }}</p>
                </div>

                <div *ngIf="selectedFile" class="uploadedFileStyle">
                  <label class="fw-bold mt-3"></label>
                  <div class="valid">
                    {{ selectedFile.name }}
                    <button (click)="removeFile()">
                      <i
                        class="bi bi-x"
                        style="cursor: pointer; color: #0f8669; font-size: 18px"
                      ></i>
                    </button>
                  </div>
                  <p style="font-size: x-small; color: green">
                    The uploaded file format is supported.
                  </p>
                </div>

                <!-- Buttons -->
                <div class="button-group mt-3">
                  <button
                    type="button"
                    class="btn btn-primary me-2"
                    [disabled]="!selectedFile"
                    (click)="uploadFiles()"
                  >
                    Upload
                  </button>
                  <button
                    class="btn btn-secondary"
                    (click)="resetUploadState()"
                  >
                    Close
                  </button>
                </div>
              </div>

              <div class="alert alert-info mb-3 newtemplateStyle">
                <div class="container">
                  <div class="content-wrapper">
                    <div
                      class="d-flex flex-column align-items-center justify-content-between"
                    >
                      <div class="text-md-center">
                        <div
                          class="d-flex align-items-center justify-content-center"
                        >
                          <i class="bi bi-info-circle me-2 fs-5"></i>
                          <div>
                            <strong class="templateLabel"
                              >Need a template?</strong
                            >
                            <p class="mb-2 templateContent">
                              Please download and use our sample Excel template
                              for correct formatting.
                            </p>
                          </div>
                        </div>
                      </div>
                      <div class="text-center mt-3 mt-md-0">
                        <button class="download-btn" (click)="downloadfile()">
                          <span class="icon-circle">
                            <i class="download-icon"></i>
                          </span>
                          <span class="btn-text">Download Template</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <app-ad-table
      [data]="filteredBulkUploadData"
      tableTitle="Uploaded Listing"
      [version]="highlightVersion"
      [isSearchShow]="true"
      [isTableFilter]="true"
      [isTableTitle]="true"
      [columns]="tableColumns"
      (backupRollBackClick)="backupRollBackClick($event)"
      [filters]="searchInputs"
      (search)="handleSearch($event)"
      (reset)="resetSearch($event)"
      (downloadExcelFromUrl)="downloadExcelFromUrl($event)"
    >
    </app-ad-table>

    <!-- <div>
            <table style="width: 100% !important; margin-top: 15px;" mat-table [dataSource]="dataSource"
                class="mat-elevation-z8 ">
                <ng-container matColumnDef="srNo">
                    <th style="width:70px" mat-header-cell *matHeaderCellDef> Sr No. </th>
                    <td mat-cell *matCellDef="let element; let i = index">
                        {{ dataSource.data.indexOf(element) + 1 }}
                    </td>
                </ng-container>

                <ng-container matColumnDef="fileName">
                    <th mat-header-cell *matHeaderCellDef> File Name </th>
                    <td mat-cell *matCellDef="let element"> {{ element?.filename ? element?.filename : 'NA' }} </td>
                </ng-container>

                <ng-container matColumnDef="version">
                    <th mat-header-cell *matHeaderCellDef> Version </th>
                    <td mat-cell *matCellDef="let element"> {{ element?.version ? element?.version :'NA'}} </td>
                </ng-container>
                <ng-container matColumnDef="uploadByAdmin">
                    <th mat-header-cell *matHeaderCellDef> Upload by Admin </th>
                    <td mat-cell *matCellDef="let element"> {{ element?.admin?.firstName
                        ? element?.admin?.firstName + ' ' + element?.admin?.lastName : 'NA' }} </td>
                </ng-container>
                <ng-container matColumnDef="uploadTime">
                    <th mat-header-cell *matHeaderCellDef> Upload Time </th>
                    <td mat-cell *matCellDef="let element"> {{ element?.uploadtime ? element.uploadtime :'NA'}} </td>
                </ng-container>

                <ng-container matColumnDef="downloadfile">
                    <th mat-header-cell *matHeaderCellDef> Download File </th>
                    <td mat-cell *matCellDef="let element">
                        <button class="btn btn-sm btn-outline-primary"
                            (click)="downloadExcelFromUrl(element?.fileURL, 'download')">
                            <i class="bi bi-download me-1"></i>
                            Download File
                        </button>
                    </td>
                </ng-container>

                <ng-container matColumnDef="backup">
                    <th mat-header-cell *matHeaderCellDef> Backup / Rollback </th>

                    <td mat-cell *matCellDef="let element">
                        <img style="height: 35px; width: 35px; cursor: pointer;" (click)="backupRollBackClick(element)"
                            matTooltip="Backup / Rollback" src="../../assets/img/backup.png" />
                        <span *ngIf="element.version == highlightVersion">
                            (current)
                        </span>
                    </td>
                </ng-container>

                <tr style="width:100% !important" mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let element; columns: displayedColumns;" class="example-element-row"
                    [class.highlighted-row]="element.version === highlightVersion">
                </tr>
            </table>
            <div *ngIf="!dataSource || dataSource?.data?.length === 0" class="no-data-found">
                <p>No data found</p>
            </div>
        </div>
        <mat-paginator [pageSizeOptions]="[5, 10, 20]" [pageSize]="5" [length]="dataSource.data.length"
            showFirstLastButtons aria-label="Select page of periodic elements">
        </mat-paginator>
        <div style="height: 50px"></div> -->
  </div>
  <div style="height: 50px"></div>
</div>

<div
  class="modal fade"
  id="deleteModal"
  tabindex="-1"
  aria-labelledby="deleteModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header gradient-header">
        <h5 class="dialogTitle" id="uploadModalLabel">Confirmation</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <p>
          Are you sure, you want to backup / rollback this version
          {{ setSelectedVersion }}?
        </p>
      </div>
      <div class="modal-footer">
        <div class="d-flex gap-3 justify-content-center w-100">
          <button
            type="button"
            id="bt_approve"
            class="button-submit button-left"
            (click)="onButtonClick()"
            [disabled]="arrowState === 'moving'"
          >
            <img
              alt=""
              src="../../../assets/svg/right-arrow.svg"
              class="left-arrow"
              [@arrowMove]="arrowState"
            />
            <span st>Yes</span>
          </button>
          <button
            type="button"
            id="bt_reject"
            class="button-back button-left"
            (click)="closeConfirmModal()"
          >
            <span class="ps-5">No</span>
            <img
              alt=""
              src="../../../assets/svg/Edit-User.svg"
              class="rejectStyle"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
