import { Component } from '@angular/core';
import { BreadcrumbService } from '../../common/breadcrumb/breadcrumb.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-work-summary',
  templateUrl: './work-summary.component.html',
  styleUrl: './work-summary.component.scss'
})
export class WorkSummaryComponent {
     constructor(private breadcrumbservice: BreadcrumbService,private router: Router){}

     ngOnInit(){
       this.breadcrumbservice.setBreadcrumbUrl(this.router.url)
     }
}
