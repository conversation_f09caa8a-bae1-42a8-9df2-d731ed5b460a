import { Component, OnInit } from '@angular/core';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTitleSubtitle,
  NgApexchartsModule
} from 'ng-apexcharts';
import { AdminService } from '../../../services/admin/admin.service';
import { CommonModule } from '@angular/common';
import { AdminPlantSelectionService } from '../../../services/admin/admin-plant-selection.service';
import { filter } from 'rxjs';

@Component({
  selector: 'app-current-day-report',
  templateUrl: './current-day-report.component.html',
  styleUrl: './current-day-report.component.scss',
  standalone: true,
  imports: [NgApexchartsModule, CommonModule],
})
export class CurrentDayReportComponent implements OnInit {
  adminId: any;
  plantId: any;
  selectedplantId: any;
  isSuperAdmin:boolean=false;

  public chartOptions: {
    series: ApexAxisChartSeries;
    chart: ApexChart;
    xaxis: ApexXAxis;
    dataLabels: ApexDataLabels;
    colors: string[];
    title: ApexTitleSubtitle;
  };
  isLoading: any;

  constructor(private adminService: AdminService, private adminPlantSelection: AdminPlantSelectionService) {
    this.chartOptions = {
      series: [], // Initialize as empty
      chart: {
        type: 'bar',
        height: 350,
        toolbar:{
          show:false,
        }
      },
      colors: [
        "#008FFB", // Scanned
        "#00E396", // Standby
        "#FEB019", // Remaining
      ],
      title: {
        text: 'Current Day report',
        align: 'left'
      },
      xaxis: {
        categories: ['Scanned', 'Standby', 'Remaining'] // Categories for the bars
      },
      dataLabels: {
        enabled: true
      },
    };
  }

  ngOnInit(): void {
    const role= localStorage.getItem('userRole');
    if (role=='superadmin') this.isSuperAdmin=true;
    if (this.isSuperAdmin) {
      this.adminPlantSelection.selectedPlant$.pipe(
                    filter((plantId) => !!plantId) 
                  ).subscribe(adminPlantId => {
        this.selectedplantId=adminPlantId;
          this.getAdminDetail();
          this.getCurrentDayReportGraph();
      });
    }
    else{
    this.getAdminDetail();
    this.getCurrentDayReportGraph();
    }
    // console.log('plant id in day-report', + this.plantId);
    
  }

  private getAdminDetail() {
    const currentUser = JSON.parse(localStorage.getItem('user') ?? ' ');
    this.adminId = currentUser.id;
    this.plantId = this.selectedplantId ? this.selectedplantId : currentUser.plantIds[0]
  }

  private getCurrentDayReportGraph() {
    this.isLoading = true
    const data = {
      plantId: this.plantId
    };
    // console.log('params', data);
    
    this.adminService.currentDayReportGraph(data)
      .then((response) => {
        this.isLoading = false
        if (response && response.message === "success" && response.status === 200 && response.data && response.data.length > 0) {
          const reportData = response.data[0];

          const scanned = parseInt(reportData.scan_equipment_count);
          const standby = parseInt(reportData.standby_count);
          const total = parseInt(reportData.total_equipment);
          const remaining = total - scanned - standby;

          this.chartOptions = {
            ...this.chartOptions, // Keep existing options
            series: [{
              name: 'Count',
              data: [scanned, standby, remaining]
            }],
          };
        } else {
          console.error("Error fetching current day report graph:", response);
          // Handle error gracefully, e.g., display a message to the user
          this.chartOptions = { // set default value when api return error
            ...this.chartOptions,
            series: [{
              name: 'Count',
              data: [0, 0, 0]
            }],
          };
        }
      }).catch(error => {
        console.error("Error fetching current day report graph:", error);
        this.chartOptions = { // set default value when api return error
          ...this.chartOptions,
          series: [{
            name: 'Count',
            data: [0, 0, 0]
          }],
        };
      });
  }
}