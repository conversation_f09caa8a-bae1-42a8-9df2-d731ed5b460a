import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { ManageUserComponent } from './pages/manage-user/manage-user.component';
import { Mr<PERSON>tatusComponent } from './pages/mr-status/mr-status.component';
import { StandbyEquipmentComponent } from './pages/standby-equipment/standby-equipment.component';
import { SectionManagementComponent } from './pages/section-management/section-management.component';
import { HomeComponent } from './pages/home/<USER>';
import { LoginComponent } from './common/login/login.component';
import { UserRegistryComponent } from './pages/user-registry/user-registry.component';
import { ProvisionSectionComponent } from './pages/provision-section/provision-section.component';
import { WorkSummaryComponent } from './pages/work-summary/work-summary.component';
import { authGuard } from './auth.guard';
import { SecurityManagementComponent } from './pages/security-management/security-management.component';
import { AdvisoryManagementComponent } from './pages/advisory-management/advisory-management.component';
import { PdfComponent } from './common/pdf/pdf.component';
import { SecurityDashboardComponent } from './pages/security-dashboard/security-dashboard.component';
import { FeedbackManagementComponent } from './pages/security-dashboard/feedback-management/feedback-management.component';
import { IncidentReportComponent } from './pages/security-dashboard/incident-report/incident-report.component';
import { LostAndFoundComponent } from './pages/security-dashboard/lost-and-found/lost-and-found.component';
import { SosCallHistoryComponent } from "./pages/security-dashboard/sos-call-history/sos-call-history.component";
import { BulkUploadExcelComponent } from './pages/bulk-upload-excel/bulk-upload-excel.component';

const routes: Routes = [
  { path: "", component: LoginComponent },
  // { path: "login", component: LoginComponent }, once login is added
  {
    path: 'home',
    component: HomeComponent,
    canActivateChild: [authGuard],
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      {
        path: 'provision-section',
        component: ProvisionSectionComponent,
      },
      {
        path: 'work-summary',
        component: WorkSummaryComponent,
      },
      {
        path: 'manage-user',
        component: ManageUserComponent,
      },
      {
        path: 'mr-status',
        component: MrStatusComponent,
      },
      {
        path: 'standby-equipment',
        component: StandbyEquipmentComponent,
      },
      {
        path: 'section-management',
        component: SectionManagementComponent,
      },
      {
        path: 'user-register',
        component: UserRegistryComponent
      },
      {
        path: 'security-dashboard',
        component: SecurityDashboardComponent
      },
      {
        path: 'security-dashboard/security-management',
        component: SecurityManagementComponent
      },
      {
        path: 'security-dashboard/advisory-management',
        component: AdvisoryManagementComponent
      },
      {
        path: 'security-dashboard/feedback-management',
        component: FeedbackManagementComponent
      },
      {
        path: 'security-dashboard/incident-report',
        component: IncidentReportComponent
      },
      {
        path: 'security-dashboard/lost-&-found',
        component: LostAndFoundComponent
      },
      {
        path: 'security-dashboard/sos-call-history',
        component: SosCallHistoryComponent
      },
      {
        path: 'plant-pdf',
        component: PdfComponent
      },
      {
        path: 'bulk-upload',
        component: BulkUploadExcelComponent
      },
    ],
  },
  { path: '**', redirectTo: '' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
