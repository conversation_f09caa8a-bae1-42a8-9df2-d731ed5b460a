import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AdminModel } from '../../models/admin.model';
import { AdminService } from '../../services/admin/admin.service';
import { AuthService } from '../../services/auth/auth.service';
import { QrService } from '../../services/qr/qr.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { StandByEquipmentsService } from '../../services/stand-by-equipments/stand-by-equipments.service';
import { UploadService } from '../../services/shared/upload.service';
import { ToastMessageComponent } from '../toast-message/toast-message.component';
import axios from 'axios';
import saveAs from 'file-saver';
import { ExcelExportService } from '../../services/shared/excel-export/excel-export.service';
import jsPDF from 'jspdf';
import { PlantService } from '../../services/plant/plant.service';
import { AdminPlantSelectionService } from '../../services/admin/admin-plant-selection.service';

declare var bootstrap: any;

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
})
export class HeaderComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  editUserForm!: FormGroup<any>;
  profileImageUrl: any;
  submitted: any;
  departments: any;
  isEditUserModalOpen: boolean = false;
  public admin: AdminModel | any = null; // Variable to store admin data
  uploadForm: FormGroup;
  isUploading = false;
  selectedFile: File | null = null;
  clickLoading: any;
  plants: any = [];
  active: string | undefined;
  input?: HTMLInputElement;
  isSuperAdmin: boolean = false;
  plantName: any={

  };
  
  constructor(
    private router: Router,
    private adminService: AdminService,
    private authService: AuthService,
    private equipmentService: StandByEquipmentsService,
    private fb: FormBuilder,
    private uploadService: UploadService,
    private excelExportService: ExcelExportService,
    private plantService: PlantService,
    private qrService: QrService,
    private adminPlantSelection: AdminPlantSelectionService
  ) {
    this.uploadForm = this.fb.group({
      excelFile: [null, Validators.required],
    });
  }

  ngOnInit() {
    const user = localStorage.getItem('user');
    const role = localStorage.getItem('userRole');
    if (role == 'superadmin') this.isSuperAdmin = true;
    const savedRoutes = sessionStorage.getItem('activeRoute');
    if (savedRoutes) {
      this.active = savedRoutes;
      this.router.navigate([savedRoutes]);
    } else {
      this.active = this.router.url;
    }
    this.getDepartment();
    if (this.isSuperAdmin) {
      this.getPlants();
    }
    if (user) {
      try {
        this.admin = JSON.parse(user) as AdminModel;
        console.log(this.admin,'admin obj');
        
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    } else {
      console.warn('No user found in localStorage');
    }
    this.editUserForm = this.fb.group(
      {
        firstName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z]{1,15}$')],
        ],
        lastName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z]{1,15}$')],
        ],
        email: ['', [Validators.required, Validators.email]],
        contactNumber: [
          '',
          [Validators.required, Validators.pattern(/^\d{10}$/)],
        ],
        wbiRoleId: ['', Validators.required],
        wbiDepartmentId: ['', Validators.required],
        gender: ['', Validators.required],
      },
      { validator: this.emailsShouldNotBeSame }
    );

  
  }

  getDepartment() {
    const data = {
      page: 1,
      limit: 10000,
    };
    const param = createAxiosConfig(data);
    this.adminService.getDepartment(param).then((response: any) => {
      this.departments = response.data;
    });
  }

  getPlants() {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: ['enabled||eq||true'],
    };
    const param = createAxiosConfig(data);
    this.plantService.getAllPlants(param).then((response) => {
      this.plants = response.data;
      this.plantName.name = response.data[0].name;
      this.plantName.id = response.data[0].id;
      this.adminPlantSelection.setSelectedPlant(response.data[0].id, this.plantName.name);
      // console.log(this.plantName.name,this.plantName.id );
    });
  }

  onPlantChange(selectedPlant: any) {
   let selectPlant= JSON.parse(selectedPlant.target.value)
    const plantId = selectPlant.id;
    const plantName= selectPlant.name;
    console.log('selectedPlant', plantId, plantName);
    this.adminPlantSelection.setSelectedPlant(plantId,plantName); // Set selected plant ID
    this.getPlantName(plantId);
  }

  emailsShouldNotBeSame(control: AbstractControl): ValidationErrors | null {
    const emailPattern =
      /^[a-zA-Z][a-zA-Z0-9]*(?:[._-][a-zA-Z0-9]+)*@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,3}$/;

    const email = control.value['email'];

    if (email && !emailPattern.test(email)) {
      return { validEmail: true };
    }

    return null;
  }

  onFileChange(event: Event): void {
    this.input = event.target as HTMLInputElement;
    if (this.input.files && this.input.files.length > 0) {
      this.selectedFile = this.input.files[0];
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (e.target?.result) {
          this.profileImageUrl = e.target.result as string; // Set the result as the preview URL
        }
      };
      reader.readAsDataURL(this.selectedFile); // Read the file as a data URL
      //this.uploadService.uploadImage(formData)
    } else {
      console.error('No file selected.');
    }
  }

  openUserProfileDialog() {
    this.profileImageUrl = this.admin?.profilePicture;
   
    this.editUserForm.patchValue({
      firstName: this.admin?.firstName,
      lastName: this.admin?.lastName,
      email: this.admin?.email,
      contactNumber: this.admin?.contactNumber,
      wbiDepartmentId: this.admin?.wbiDepartmentId,
      wbiRoleId: this.admin?.wbiRoleId,
      gender: this.admin?.gender,
    });
    this.editUserForm.get('wbiRoleId')?.disable();
    this.editUserForm.get('contactNumber')?.disable();
    this.editUserForm.get('email')?.disable();
    this.isEditUserModalOpen = true;
  }

  onSubmitExcel(): void {
    if (this.selectedFile) {
      // Extract file name without the extension
      const fileName = this.selectedFile.name;
    //  console.log(this.admin);
     
     
   
      

      // Get the current plant name dynamically
      const currentPlantName = this.isSuperAdmin? this.plantName.name || '': this.admin?.plant[0].name || ''; // Assuming 'name' contains the plant name
      const regex = new RegExp(
        `^wbi-checkpoint-temple-${currentPlantName}\\.xlsx$`,
        'i'
      ); // Regex with dynamic plant name

      // Validate file name
      if (!regex.test(fileName)) {
        alert(`Please upload a valid file for the plant: ${currentPlantName}`);
        return;
      }
      this.clickLoading = true;
      this.isUploading = true;
      const formData = new FormData();
      formData.append('filedata', this.selectedFile);
      formData.append('plantId', `${this.isSuperAdmin? this.plantName.id : this.admin?.plant[0].id}`);
      formData.append('adminId', this.admin.id);
      console.log('Selected file before upload:', this.selectedFile);
      console.log('FormData entries:');
      formData.forEach((value, key) => {
        console.log(key, value);
      });

      this.equipmentService
        .bulkUplodEquipments(formData)
        .then((response: any) => {
          this.isUploading = false;
          console.log('Upload successful:', response);
          this.clickLoading = false;
          if (response.responseCode == 200) {

            if(response.invalidRecords > 0)
            this.downloadExcelFromBase64(
              response.fileData,
              `wbi-checkpoint-temple-${this.isSuperAdmin? this.plantName.name : this.admin?.plant[0].name}.xlsx`
            );
            // Close the modal
            const modalElement = document.getElementById('uploadModal');
            const modalInstance = bootstrap.Modal.getInstance(modalElement);
            if (modalInstance) {
              modalInstance.hide();
            }
          }
        })
        .catch((error) => {
          console.error('Upload failed:', error);
        });
    }
  }

  private downloadExcelFromBase64(base64Data: string, fileName: string): void {
    try {
      // Remove data URI prefix if present
      const base64Content = base64Data.replace(/^data:.+;base64,/, '');

      // Convert base64 to binary
      const binaryString = window.atob(base64Content);
      const byteArray = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        byteArray[i] = binaryString.charCodeAt(i);
      }

      // Create Blob and download link
      const blob = new Blob([byteArray], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = fileName;

      // Trigger download
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      // Clean up
      URL.revokeObjectURL(downloadLink.href);
    } catch (error) {
      console.error('Error downloading Excel file:', error);
      // You might want to show an error message to the user here
    }
  }

  @Input() showElement: boolean = false;
  username: string = 'User';

  logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['']);
  }

  openModal(event: Event) {
    event.preventDefault(); // Prevent default link behavior
    const modalElement = document.getElementById('uploadModal');
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
  }

  bulkQRDownload() {
    this.clickLoading = true;
    var data = {
      plantId: this.isSuperAdmin? this.plantName.id: this.admin?.plantIds[0],
    };

    this.qrService
      .downloadQrCodes(data)
      .then((response: any) => {
        if (response.status==400) {
          this.toast.showErrorToast('Data not available.');
          this.clickLoading = false;
          return;
        }else{
          window.open(response, '_blank');
          this.clickLoading = false;
        }

      })
      .catch((error) => {
        this.clickLoading = false;
        console.error('Error while downloading QR codes:', error);
      });
  }

  async onEditSubmit() {
    this.submitted = true;
    if (this.editUserForm.invalid) {
      return;
    } else {
      this.clickLoading = true;
      var formValue = this.editUserForm.value;
      formValue['plantIds'] = this.admin?.plantIds;
      formValue['plant'] = [{ id: this.admin?.plantIds[0] }];
      formValue['applicationId'] = 2;
      formValue['wbiRoleId'] = parseInt(formValue['wbiRoleId']);
      formValue['wbiDepartmentId'] = parseInt(formValue['wbiDepartmentId']);
      if (this.selectedFile) {
        formValue['profilePicture'] = await this.uploadProfilePic();
      }
      delete formValue.plant;
      var data = {
        tableName: 'admins',
        id: this.admin?.id,
        data: formValue,
      };
      this.adminService.updateUser(data).then((response: any) => {
        this.toast.showSuccessToast('successully edit user');
        this.clickLoading = false;
        localStorage.setItem('user', JSON.stringify(response));
        console.log(JSON.stringify(response));
      });
    }
  }
  resetEditForm() {
    console.log('file-input-before', this.input?.value);
    if (this.input) {
      this.input.value = '';
    }
    console.log('file-input-after', this.input?.value);
    this.profileImageUrl = this.admin?.profilePicture;
    this.editUserForm.patchValue({
      firstName: this.admin?.firstName,
      lastName: this.admin?.lastName,
      email: this.admin?.email,
      contactNumber: this.admin?.contactNumber,
      wbiDepartmentId: this.admin?.wbiDepartmentId,
      wbiRoleId: this.admin?.wbiRoleId,
      gender: this.admin?.gender,
    });
  }

  closeEditUserModal() {
    this.profileImageUrl = this.admin?.profilePicture;
    this.isEditUserModalOpen = false;
  }
  closeEditModal() {
    this.profileImageUrl = this.admin?.profilePicture;
    this.isEditUserModalOpen = false;
  }
  async uploadProfilePic(): Promise<string> {
    let url = '';
    if (this.selectedFile) {
      const formData = new FormData();
      // Append the selected file to form data
      formData.append('file', this.selectedFile);

      try {
        // Await the service call to upload the image
        url = await this.uploadService.uploadImage(formData);
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }
    return url;
  }

  getPlantName(id?: any) {
    this.plants.forEach((item: any) => {
      if (item.id == id && id) {
        this.plantName.name = item.name;
        this.plantName.id = item.id;
      }
    });
  }

  async downloadFile() {
    // await this.getPlantName();

    let excelplantName = this.plantName.name || this.admin?.plant[0].name;

    this.excelExportService.exportToExcel(
      `wbi-checkpoint-temple-${excelplantName}`
    );
  }

  activateNav(url: string) {}
}
