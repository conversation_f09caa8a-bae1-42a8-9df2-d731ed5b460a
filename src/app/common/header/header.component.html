<!-- <div class="container-fluid top-bar">
    <div class="row">
        <div class="col-sm">
            <img src="../../../assets/img/ambuja-logo.png" alt="" style="height: 32px; width: 81px;">
        </div>
        <div class="col-sm">
            <img src="../../../assets/img/bog.png" alt="" style="height: 65px; width: 253px;">
        </div>
        <div class="col-sm">
            <div class="row">
                <div class="col-sm switch-container">
                    <label class="switch-label-right">BOG</label>
                    <div class="switch">
                        <input type="checkbox" id="toggle" class="switch-input">
                        <label for="toggle" class="switch-label"></label>
                      </div>
                      <label class="switch-label-right">WBI</label>
                </div>
                <div class="col-sm">
                    <img src="../../../assets/img/profile.jpg" alt="..." class="rounded-circle" style="width: 51px; height: 51px;">
                </div>
                <div class="col-sm">
                    <img src="../../../assets/img/ACC.png" alt="" style="height: 30px; width: 95px;">
                </div>
            </div>
        </div>
    </div> 
</div> -->
<app-toast-message> </app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>
<div class="header">
  <!-- Text Logo -->
  <div class="text-logo">
    <a href="javascript:void(0)">
      <img src="../../../assets/img/ambuja-logo.png" alt="" />
    </a>
  </div>

  <!-- Group Logo (Center Aligned) -->
  <div class="group-logo">
    <img
      src="../../../assets/img/adani-group-icon.png"
      alt=""
      style="width: 100px"
    />
    <img
      src="../../../assets/img/we-care-logo.png"
      alt=""
      style="width: 120px"
    />
  </div>

  <!-- Plant Selection (Moved Before Avatar Profile) -->
  @if (isSuperAdmin) {
  <div class="col-md-2 plant-selection">
    <select
      class="form-control"
      (change)="onPlantChange($event)"
    >
    <!-- <option value="">Select Plant</option> -->
      <option *ngFor="let plant of plants" [value]="plant | json">
        {{ plant.name }}
      </option>
    </select>
    <!-- Uncomment if needed -->
    <!-- <div *ngIf="registrationForm.get('plantIds')?.invalid && registrationForm.get('plantIds')?.touched"
        class="text-danger">Plant is required.</div> -->
  </div>
  }

  <!-- Avatar Profile -->
  <div class="avatar-profile">
    <div *ngIf="showElement" class="avatar dropdown" style="margin-top: -3px">
      <a
        id="dropdownMenuButton"
        data-bs-toggle="dropdown"
        aria-haspopup="true"
        aria-expanded="false"
      >
        <div class="d-flex align-items-center">
          <img
            [src]="
              admin?.profilePicture
                ? admin?.profilePicture
                : '../../../assets/svg/Avatar.svg'
            "
            alt=""
            style="
              margin-right: 8px;
              height: 36px;
              width: 36px;
              margin-top: 5px;
            "
          />
          <img
            src="../../../assets/svg/down-vector.svg"
            alt=""
            style="width: 15px; margin-top: 10px"
          />
        </div>
      </a>
      <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
        <!-- Dropdown Items -->
        <!-- Add other dropdown items here -->
        <a
          class="dropdown-item d-flex align-items-center"
          href="javascript:void(0);"
          (click)="openUserProfileDialog()"
        >
          <div class="profile-dropdown-img">
            <img
              [src]="
                admin?.profilePicture
                  ? admin?.profilePicture
                  : '../../../assets/svg/Avatar.svg'
              "
              alt="Admin Avatar"
            />
          </div>
          Hi {{ admin?.firstName }}{{ admin?.lastName }}!
        </a>
        <div>
          <hr class="dropdown-divider" />
        </div>
        <a
          class="dropdown-item d-flex align-items-center"
          href="javascript:void(0);"
          (click)="bulkQRDownload()"
        >
          <div class="profile-dropdown-img">
            <img src="../../../assets/svg/qr-code.svg" alt="" />
          </div>
          Download QR
        </a>
        <!-- <div>
          <hr class="dropdown-divider">
        </div>
        <a
          class="dropdown-item d-flex align-items-center"
          href="javascript:void(0);"
          (click)="openModal($event)"
        >
          <div class="profile-dropdown-img">
            <img src="../../../assets/svg/filetype-xls.svg" alt="" />
          </div>
          Bulk Upload Excel
        </a> -->
        <div>
          <hr class="dropdown-divider" />
        </div>
        <a
          class="dropdown-item d-flex align-items-center"
          href="javascript:void(0);"
          (click)="logout()"
        >
          <div class="profile-dropdown-img">
            <img src="../../../assets/svg/Logout.svg" alt="" />
          </div>
          Logout
        </a>
      </div>
    </div>
    <div class="acc-logo">
      <img src="../../../assets/svg/ACC-Logo.svg" alt="" />
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div *ngIf="isUploading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Uploading File...</div>
  </div>
</div>
<!-- Modal -->
<div
  class="modal fade"
  id="uploadModal"
  tabindex="-1"
  aria-labelledby="uploadModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <form (ngSubmit)="onSubmitExcel()" #uploadForm="ngForm">
      <div class="modal-content">
        <div class="modal-header gradient-header">
          <h5 class="modal-title" id="uploadModalLabel">Upload Excel File</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <!-- Template Download Notice -->
          <div class="alert alert-info mb-3">
            <div class="d-flex align-items-center">
              <i class="bi bi-info-circle me-2"></i>
              <div>
                <strong>Need a template?</strong>
                <p class="mb-2">
                  Please download and use our sample Excel template for correct
                  formatting.
                </p>
                <button
                  class="btn btn-sm btn-outline-primary"
                  (click)="downloadFile()"
                >
                  <i class="bi bi-download me-1"></i>
                  Download Template
                </button>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="excelFile" class="form-label">Choose Excel File</label>
            <input
              type="file"
              class="form-control"
              id="excelFile"
              accept=".xls, .xlsx"
              (change)="onFileChange($event)"
              #fileInput
              [disabled]="isUploading"
            />
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
            [disabled]="isUploading"
          >
            Close
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="!selectedFile || isUploading"
          >
            Upload
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
<app-custom-modal
  [title]="'Edit Profile'"
  [width]="'350px'"
  *ngIf="isEditUserModalOpen"
  (closeModal)="closeEditUserModal()"
  (onClickCross)="closeEditModal()"
>
  <div>
    <form [formGroup]="editUserForm" (ngSubmit)="onEditSubmit()">
      <div class="input-text-group text-center">
        <img
          [src]="profileImageUrl || '../../../assets/svg/Avatar.svg'"
          alt="Profile Picture"
          class="profile-avatar mb-3"
          width="100"
          height="100"
          style="border-radius: 50%; object-fit: cover"
        />
        <input
          type="file"
          #fileInput
          class="form-control form-control-lg"
          id="profile"
          (change)="onFileChange($event)"
          accept="image/*"
        />
      </div>
      <div class="input-text-group">
        <label for="role">Role</label>
        <select class="form-control" formControlName="wbiRoleId" id="role">
          <option value="">Select Role</option>
          <option value="3">User</option>
          <option value="2">Admin</option>
          <option value="1" [hidden]="!isSuperAdmin">Super Admin</option>
        </select>
        <span
          id="txt_roll_error"
          class="text-danger"
          *ngIf="(editUserForm.get('wbiRoleId')?.touched || submitted) && editUserForm.get('wbiRoleId')?.errors?.['required']"
        >
          Please select a role.
        </span>
      </div>

      <div class="input-text-group">
        <label for="firstName">First Name</label>
        <input
          type="text"
          class="form-control"
          formControlName="firstName"
          placeholder="Edit First Name"
          oninput="event.target.value=event.target.value.trimStart('')"
        />
        <span
          id="txt_firstName_error"
          class="text-danger"
          *ngIf="(editUserForm.get('firstName')?.touched || submitted) && editUserForm.get('firstName')?.errors?.['required']"
        >
          First Name is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="lastName">Last Name</label>
        <input
          type="text"
          class="form-control"
          formControlName="lastName"
          placeholder="Edit Last Name"
          oninput="event.target.value=event.target.value.trimStart('')"
        />
        <span
          id="txt_lastName_error"
          class="text-danger"
          *ngIf="(editUserForm.get('lastName')?.touched || submitted) && editUserForm.get('lastName')?.errors?.['required']"
        >
          Last Name is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="gender">Gender</label>
        <select class="form-control" formControlName="gender" id="gender">
          <option value="">Select Gender</option>
          <option value="1">Male</option>
          <option value="0">Female</option>
          <option value="2">Other</option>
        </select>
        <span
          id="txt_gender_error"
          class="text-danger"
          *ngIf="(editUserForm.get('gender')?.touched || submitted) && editUserForm.get('gender')?.errors?.['required']"
        >
          Please select a gender.
        </span>
      </div>
      <div class="input-text-group">
        <label for="email">Email</label>
        <input
          type="text"
          class="form-control"
          formControlName="email"
          placeholder="Edit Email"
          oninput="event.target.value=event.target.value.trimStart('')"
          readonly
        />
        <div
          id="txt_email_error"
          class="text-danger validation"
          *ngIf="
            editUserForm.get('email')?.value &&
            editUserForm.hasError('validEmail')
          "
        >
          Invalid Email ID.
        </div>
        <span
          id="txt_email_error"
          class="text-danger"
          *ngIf="(editUserForm.get('email')?.touched || submitted) && editUserForm.get('email')?.errors?.['required']"
        >
          Email is required.
        </span>
      </div>
      <div class="input-text-group">
        <label for="contact">Contact No.</label>
        <input
          type="text"
          class="form-control"
          formControlName="contactNumber"
          placeholder="Edit Contact"
          onkeypress="return event.charCode >= 48 && event.charCode <= 57"
          maxlength="10"
        />
        <span
          id="txt_contactNumber_error"
          class="text-danger"
          *ngIf="(editUserForm.get('contactNumber')?.touched || submitted) && editUserForm.get('contactNumber')?.errors?.['required'] || editUserForm.get('contactNumber')?.touched && editUserForm.get('contactNumber')?.invalid"
        >
          Please enter valid Mobile Number.
        </span>
      </div>
      <div class="input-text-group">
        <label for="department">Department</label>
        <select class="form-control" formControlName="wbiDepartmentId">
          <option value="">Select Department</option>
          <option *ngFor="let dept of departments" [value]="dept.id">
            {{ dept.title }}
          </option>
        </select>
        <span
          id="txt_department_error"
          class="text-danger"
          *ngIf="(editUserForm.get('wbiDepartmentId')?.touched || submitted) && editUserForm.get('wbiDepartmentId')?.errors?.['required']"
        >
          Department is required.
        </span>
      </div>
      <div class="w-100 d-flex mt-4 btn-section">
        <button class="button-submit" type="submit">Submit</button>
        <button type="button" (click)="resetEditForm()" class="button-back">
          Reset
        </button>
      </div>
    </form>
  </div>
</app-custom-modal>
