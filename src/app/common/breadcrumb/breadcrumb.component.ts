import { Component, EventEmitter, Output } from '@angular/core';
import { BreadcrumbItem } from './breadcrumb-item';
import { BreadcrumbService } from './breadcrumb.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
})
export class BreadcrumbComponent {
 @Output() getUrl = new EventEmitter<any>();

  breadcrumb: BreadcrumbItem[] = [];
  breadcrumbArray: any = [];

  constructor(
    private breadcrumbService: BreadcrumbService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.breadcrumbService.breadcrumb$.subscribe((breadcrumb) => {
      this.breadcrumb = breadcrumb;
    });
    this.getBreadcrumbUrl();
  }

  getBreadcrumbUrl() {
    this.breadcrumbService.getBreadcrumbUrl().subscribe((res: any) => {
      if (res && res != '/dashboard') {
        this.setBreadcrumbUrl(res);
      }
    });
  }

  setBreadcrumbUrl(url: any) {
    this.breadcrumbArray = [];
    let urlPath = url.split('?')[0]; // Remove query parameters
  
    let urlPage = urlPath.split('/').filter((item:any) => item !== ''); // Remove empty items
  
    let accumulatedPath = '';
  
    urlPage.forEach((item:any, index:any) => {
      if (item != '' && item != 'dashboard') { 
        accumulatedPath += `/${item}`;
        this.breadcrumbArray.push({
          label: item.replace(/-/g, ' ').replace(/\b\w/g, (char:any) => char.toUpperCase()), 
          url: accumulatedPath,
        });
      }
    });
     console.log(this.breadcrumbArray);
     
    this.getUrl.emit(url);
  }
  
  navigateToUrl(url: any) {
    console.log(url)
    if(url  == '/home'){
      sessionStorage.removeItem('activeRoute')
      this.router.navigate([url], {
        queryParams: this.activatedRoute.snapshot.queryParams,
      });
    }
    else{
      sessionStorage.setItem('activeRoute', url);
      this.router.navigate([url], {
        queryParams: this.activatedRoute.snapshot.queryParams,
      });
    }
   
  }
}
