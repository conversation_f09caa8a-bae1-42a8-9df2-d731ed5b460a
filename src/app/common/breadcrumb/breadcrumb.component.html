<nav aria-label="breadcrumb" style="display: none;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item" *ngFor="let item of breadcrumb; let last = last">
            <ng-container *ngIf="!last">
                <a [routerLink]="item.url">{{ item.label }}</a>
            </ng-container>
            <ng-container *ngIf="last">
                {{ item.label }}
            </ng-container>
            <!-- <a [routerLink]="item.url">{{ item.label }}</a> -->
        </li>
    </ol>
</nav>

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <!-- <li class="breadcrumb-item"><a [routerLink]="['/home']"></a></li> -->
        <li class="breadcrumb-item" *ngFor="let item of breadcrumbArray; let last = last">
            <ng-container *ngIf="!last">
                <a href="javascript:void()" (click)="navigateToUrl(item['url'])">{{ item.label }}</a>
            </ng-container>
            <ng-container *ngIf="last">
                {{ item.label }}
            </ng-container>
        </li>
    </ol>
</nav>
