import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class FormService {

  constructor() { }
  trimFormValues(form: FormGroup) {
    Object.keys(form.controls).forEach((key) => {
      const control = form.get(key);
      if (control && control.value !== null && typeof control.value === 'string') {
        const inputElement = document.querySelector(`[formControlName="${key}"]`) as HTMLInputElement;
        if (inputElement && inputElement.type === 'file') {
          return;
        }
        control.setValue(control.value.trim(), { emitEvent: false });
      }
    });
  }
  convertDate(date: Date): string {
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      return '';
    }
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Add 1 to month (0-indexed)
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

}
