import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class TrimFormValueService {

  constructor() { }
  // trimFormValues(form: FormGroup): void {
  //   Object.keys(form.controls).forEach((key) => {
  //     const control = form.get(key);
  //     if (control && control.value && typeof control.value === 'string') {
  //       control.setValue(control.value.trim(), { emitEvent: false });
  //     }
  //   });
  // }
  trimFormValues(form: FormGroup) {
    Object.keys(form.controls).forEach((key) => {
      const control = form.get(key);
      if (control && control.value !== null && typeof control.value === 'string') {
        const inputElement = document.querySelector(`[formControlName="${key}"]`) as HTMLInputElement;
        if (inputElement && inputElement.type === 'file') {
          return;
        }
        control.setValue(control.value.trim(), { emitEvent: false });
      }
    });
  }
}
