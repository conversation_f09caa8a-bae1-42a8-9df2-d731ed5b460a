<app-toast-message>
</app-toast-message>
<div *ngIf="clickLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-2">Please wait...</div>
  </div>
</div>
<div class="main-container">
  <div class="header">
    <app-header [showElement]="false"></app-header>
  </div>

  <div class="login-container" *ngIf="!showRegister" [@formSlide]>
    <div class="row">
      <div class="col-12 col-md-6">
        <div class="image-section">
          <!-- Image section content here -->
        </div>
      </div>
  
      <div class="col-12 col-md-6">
        <div class="login-section" *ngIf="!showOtp" [@slideInOut]>
          <h5><b>LOGIN TO WBI</b></h5>
          <br>
          <form [formGroup]="emailForm" (ngSubmit)="toggleForm()" novalidate>
            <div class="form-group">
              <label><b>AD ID / Email ID</b></label>
              <input class="form-control form-control-lg" formControlName="email"
                [ngClass]="{'is-invalid': emailForm.get('email')?.invalid && emailForm.get('email')?.touched}"
                type="email" placeholder="Enter your AD ID / Email ID" required>
              <div *ngIf="emailForm.get('email')?.touched && emailForm.get('email')?.invalid" class="invalid-feedback">
                <div *ngIf="emailForm.get('email')?.errors?.['required']">AD ID or Email is required.</div>
              </div>
            </div>
            <br>
            <button type="submit" class="btn btn-gradient" [disabled]="emailForm.invalid">Send OTP</button>
          </form>
          <div (click)="toggleRegister()" class="register-button-wrap"><button class="register-button"
              type="button">Register here</button></div>
          <br>
          <p class="policy">By continuing, You agree to our T&C and Privacy Policy.</p>
        </div>
        <div class="otp-section" *ngIf="showOtp" [@slideInOut]>
          <br>
          <form>
            <div class="form-group">
              <label>Please enter otp sent to <b>{{email}}</b></label>
              <br>
              <div class="otp-input"><ng-otp-input (onInputChange)="onOtpChange($event)"
                  [config]="{length:4, allowNumbersOnly: true}"></ng-otp-input></div>
            </div>
            <br>
            <div class="btn-group">
              <button (click)="toggleForm(true)" type="submit" class="btn btn-gradient">Back</button>
              <button type="submit" (click)="resendOtp()" class="btn resend-otp btn-outline-primary">Resend OTP</button>
            </div>
          </form>
          <br>
          <p class="policy">By continuing, You agree to our T&C and Privacy Policy.</p>
        </div>
      </div>
    </div>
  
    <div class="row">
      <div class="col-12 col-md-6">
        <div class="notice-section">
          <h6><b>Important Notices</b></h6>
          <p>1) Introducing, the new toll-free number to call our Customer Care and avail our helpline services: 1800 121
            4444 66</p>
          <p>3) If you are using an old version of Internet Explorer, please update to the latest version immediately, to
            enjoy a seamless banking experience.</p>
        </div>
      </div>
  
      <div class="col-12 col-md-6">
        <div class="security-section">
          <h6><b>Important security information</b></h6>
          <p>1) Before logging in, please ensure that the URL address on the address bar of your internet browser
            starts with https://wbi.adani.com.</p>
          <p>2) Never provide your User ID or password to anyone on the phone or in response to a mail.
            Report a suspicious mail.</p>
          <p>3) Do not enter login or other sensitive information in any pop-up window.</p>
          <p>4) Verify the site's security certificate by clicking on the padlock icon of your internet browser.
            For more details, click here.</p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="register-container" *ngIf="showRegister" [@formSlide]>
    <div class="card register-card">
      <div class="registeration-form">
        <h4>Registration Form</h4>
        <br>
        <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()">
          <div class="row g-4">
            <div class="form-group col-md-6">
              <label>First Name</label>
              <input type="text" class="form-control form-control-lg" placeholder="First Name"
                formControlName="firstName">
              <div *ngIf="registrationForm.get('firstName')?.invalid && registrationForm.get('firstName')?.touched"
                class="text-danger">
                <div *ngIf="registrationForm.get('firstName')?.errors?.['required']">
                  First Name is required.
                </div>
                <div *ngIf="registrationForm.get('firstName')?.errors?.['pattern']">
                  First Name must be alphabetic and 1-15 characters long.
                </div>
              </div>
            </div>
            <div class="form-group col-md-6">
              <label>Last Name</label>
              <input type="text" class="form-control form-control-lg" placeholder="Last Name"
                formControlName="lastName">
              <div *ngIf="registrationForm.get('lastName')?.invalid && registrationForm.get('lastName')?.touched"
                class="text-danger">
                <div *ngIf="registrationForm.get('lastName')?.errors?.['required']">
                  Last Name is required.
                </div>
                <div *ngIf="registrationForm.get('lastName')?.errors?.['pattern']">
                  Last Name must be alphabetic and 1-15 characters long.
                </div>
              </div>
            </div>
            <div class="form-group col-md-6">
              <label>Date of Birth</label>
              <input type="date" class="form-control form-control-lg" formControlName="dob" [max]="maxDate">
              <div *ngIf="registrationForm.get('dob')?.invalid && registrationForm.get('dob')?.touched"
                class="text-danger">
                Date of Birth is required.
              </div>
            </div>
            <div class="form-group col-md-6">
              <label for="inputGender">Gender</label>
              <select id="inputGender" class="form-control" formControlName="gender">
                <option value="">Select Gender</option>
                <option value=1>Male</option>
                <option value=0>Female</option>
                <option value=2>Other</option>
              </select>
              <div *ngIf="registrationForm.get('gender')?.invalid && registrationForm.get('gender')?.touched"
                class="text-danger">
                Gender is required.
              </div>
            </div>
            <div class="form-group col-md-6">
              <label>Contact Number</label>
              <input type="text" class="form-control form-control-lg" placeholder="Contact Number"
                formControlName="contactNumber">
              <div
                *ngIf="registrationForm.get('contactNumber')?.invalid && registrationForm.get('contactNumber')?.touched"
                class="text-danger">
                Contact Number is required and must be 10 digits.
              </div>
            </div>
            <div class="form-group col-md-6">
              <label>Email Address</label>
              <input type="email" class="form-control form-control-lg" placeholder="Email Address"
                formControlName="email">
              <div *ngIf="registrationForm.get('email')?.invalid && registrationForm.get('email')?.touched"
                class="text-danger">
                Please enter a valid Email Address.
              </div>
              <div class="text-danger" *ngIf="registrationForm.get('email')?.hasError('adaniDomain')">Email must belong
                to the adani.com domain and must contain one alphabet</div>
            </div>
            <div class="form-group col-md-6">
              <label for="inputDepartment">Select WBI Department</label>
              <select id="inputDepartment" class="form-control" formControlName="wbiDepartmentId">
                <option value="">Choose...</option>
                <option value="">Select Department</option>
                <option *ngFor="let dept of departments" [value]="dept.id">{{ dept.title }}</option>
              </select>
              <div
                *ngIf="registrationForm.get('wbiDepartmentId')?.invalid && registrationForm.get('wbiDepartmentId')?.touched"
                class="text-danger">
                Department is required.
              </div>
            </div>
            <div class="form-group col-md-6">
              <label for="inputRole">Select WBI Role</label>
              <select id="inputRole" class="form-control" formControlName="wbiRoleId">
                <option value="">Select Role</option>
                <option value=3>User</option>
                <option value=2>Plant Admin</option>
              </select>
              <div *ngIf="registrationForm.get('wbiRoleId')?.invalid && registrationForm.get('wbiRoleId')?.touched"
                class="text-danger">
                Role is required.
              </div>
            </div>
            <div class="form-group col-md-6">
              <label for="inputPlant">Select Plant</label>
              <select id="inputPlant" class="form-control" formControlName="plantIds">
                <option value="">Choose...</option>
                <option value="">Select Department</option>
                <option *ngFor="let plant of plants" [value]="plant.id">{{ plant.name }}</option>
              </select>
              <div *ngIf="registrationForm.get('plantIds')?.invalid && registrationForm.get('plantIds')?.touched"
                class="text-danger">
                Plant is required.
              </div>
            </div>
            <div class="register-form-button col-md-12">
              <button type="submit" class="btn btn-gradient" [disabled]="!registrationForm.valid">Register</button>
            </div>
            <button (click)="toggleRegister()" class="register-button btn btn-secondary" type="button">Back to
              Login</button>
          </div>
        </form>
      </div>
    </div>
  </div>


  <div class="footer">
    <app-footer></app-footer>
  </div>
</div>