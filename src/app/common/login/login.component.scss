.main-container {
    display: flex; /* Use flex layout */
    height: 100vh; /* Full viewport height */
    flex-direction: column; /* Stack the children vertically */
    background: linear-gradient(106.98deg, #A89DCA 8.39%, #CEC1DD 32.06%, #F4EDF5 56.25%, #CFBAD7 83.02%, #C3A9CD 100%);
    overflow: auto;
}
.header {
    flex-shrink: 0; /* Prevent shrinking of the header */
}
.row {
    flex-grow: 1;
  }
footer {
    margin-top: auto;
  }

.login-container {
    flex-grow: 1;
    overflow-y: auto; /* Allows vertical scrolling */
    padding: 20px;
}
.notice-section {
    height: 320px;
    margin-right: 10px;
    padding: 29px 10px 0px 44px;
    background: linear-gradient(89.27deg, rgba(30, 105, 178, 0.1) 0.6%, rgba(128, 66, 146, 0.1) 51.03%, rgba(175, 58, 110, 0.1) 99.39%);
    border-radius: 20px;
    overflow: auto;
}

p {
    font-size: 13px;
    margin-bottom: 5px;
}
.security-section{
    margin-left: 10px;
    height: 320px;
    padding: 29px 10px 0px 44px;
    background: linear-gradient(89.27deg, rgba(30, 105, 178, 0.1) 0.6%, rgba(128, 66, 146, 0.1) 51.03%, rgba(175, 58, 110, 0.1) 99.39%);
    border-radius: 20px;
    overflow: auto;
}


.login-section {
    width: 80%;
    padding: 0px 50px 0px 50px;
    align-content: center;
    margin: 0 auto;

    label {
        color: #0B74B0;
        margin-bottom: 10px;
    }

    input {
        width: 100%;
        height: 45px;
    }

}

.btn-gradient {
    width: 100%;
    color: white;
    border-radius: 20px;
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
}

.otp-section {
    width: 80%;
    padding: 0px 50px 0px 50px;
    align-content: center;
    margin: 0 auto;

    label {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }

    .otp-input {
        display: flex;
        justify-content: center;
    }

    input {
        width: 100%;
        height: 45px;
    }
}
.btn-group{
    display:flex;
    gap: 20px;
    justify-content: space-between;
    margin: 10px;
    .btn-gradient {
        width: 10px;
        color: white;
        border-radius: 20px;
        background: linear-gradient(90deg, #0B74B0 0%, #75479C 54.17%, #BD3861 100%);
    }
    .resend-otp{
        border-radius: 20px;
        width: 10px;
    }

}

.register-button{
    text-align: center;
    border: none;
    background-color: transparent;
    text-decoration: underline;
    margin-top: 10px;
    color: #0B74B0;
}

.register-button-wrap{
    text-align: center;
}
.register-form-button{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 200px 0px 200px;
}

.policy {
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}
.register-container {
    flex-grow: 1;
    overflow-y: auto; /* Allows vertical scrolling */
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    padding: 40px;
}
.register-card{
    background-color: white;
    width: 800px;
    overflow-y: auto ;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Optional: Add some shadow */
    border-radius: 8px; /* Optional: Add rounded corners */
    padding: 20px;

    input{
        height: 45px;
    }
    select{
        height: 45px;
    }
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .spinner-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  @media screen and (max-width: 767px) {
    .login-container {
        flex-grow: 1;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
    }

    .register-container {
        flex-grow: 1;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
    }

    .security-section{
        margin-left: 50px;
        margin-right: 50px;
        margin-bottom: 50px;
    }
    .notice-section{
        margin-left: 50px;
        margin-right: 50px;
        margin-bottom: 50px;
    }
    .register-form-button{
        padding: 0px 50px 0px 50px;
    }
}