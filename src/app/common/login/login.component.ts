import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService } from '../../services/auth/auth.service';
import { trigger, transition, style, animate } from '@angular/animations';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgOtpInputComponent } from 'ng-otp-input';
import { Router } from '@angular/router';
import { ToastMessageComponent } from '../toast-message/toast-message.component';
import { PlantService } from '../../services/plant/plant.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { AdminService } from '../../services/admin/admin.service';
import { DatePipe } from '@angular/common';
import { adaniDomainValidator } from '../../core/utilities/adani-email-validator';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateX(100%)' }),
        animate('300ms ease-out', style({ transform: 'translateX(0)' }))
      ]),
    ]),
    trigger('formSlide', [
      transition(':enter', [
        style({ transform: 'translateX(100%)' }),
        animate('600ms ease-out', style({ transform: 'translateX(0)' }))
      ]),
    ])
  ]
})
export class LoginComponent implements OnInit {
  @ViewChild(NgOtpInputComponent, { static: false }) ngOtpInput!: NgOtpInputComponent
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
  maxDate!: string;
  emailForm: FormGroup
  showOtp = false
  showRegister = false
  email: string = ''
  registrationForm!: FormGroup<any>;
  plants: any = []
  departments: any = []
  clickLoading: any
  constructor(private authService: AuthService,
    private fb: FormBuilder,
    private router: Router,
    private plantService: PlantService,
    private adminService: AdminService,
    private datePipe: DatePipe
  ) {
    this.maxDate = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
    this.emailForm = this.fb.group({
      email: ['', [Validators.required,]],
    });
    this.registrationForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.pattern('^[a-zA-Z]{1,15}$')]],
      lastName: ['', [Validators.required, Validators.pattern('^[a-zA-Z]{1,15}$')]],
      dob: ['', Validators.required],
      gender: ['', Validators.required],
      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      email: ['', [Validators.required, adaniDomainValidator()]],
      wbiDepartmentId: ['', Validators.required],
      wbiRoleId: ['', Validators.required],
      plantIds: ['', Validators.required],
    });
  }


  ngOnInit(): void {
    this.getPlants()
    this.getDepartments()
  }

  toggleForm(back?: any) {
    if (back) {
      this.showOtp = !this.showOtp;
    } else {
      if (this.emailForm.valid) {
        this.clickLoading = true
        this.email = this.emailForm.value.email
        // Check if '@adani.com' is at the end, if not, add it
        if (!this.email.endsWith('@adani.com')) {
          this.email += '@adani.com';
        }
        this.authService.sendOtp({ email: this.email }).then((response) => {
          this.clickLoading = false
          if (response.responseCode == 404) {
            this.toast.showErrorToast(response.message);
            return
          }
          if (response.responseCode == 200) {
            this.toast.showSuccessToast('OTP Sent');
            this.showOtp = !this.showOtp;
            return
          }
          else {
            this.toast.showErrorToast(response.message);
          }
        })
      }
    }
  }
  toggleRegister() {
    this.showRegister = !this.showRegister
  }

  onOtpChange($event: string) {
    if ($event.length > 3) {
      this.authService.verifyOtp({ email: this.email, otp: $event }).then((response) => {
        if (response['responseCode'] == 400) {
          this.ngOtpInput.setValue("")
          this.toast.showErrorToast('Invalid OTP!');
          return
        }
        if (response['responseCode'] == 200) {
          this.router.navigate(['/home']); // Redirect to homepage
        }
        if (response['responseCode'] == 407) {
          this.ngOtpInput.setValue("")
          this.toast.showErrorToast(response['message']);
          return
        }
      })
    }
  }

  onSubmit() {
    this.clickLoading = true
    let formValue = this.registrationForm.value;
    formValue['plant'] = [{ id: formValue['plantIds'] }]
    formValue['plantIds'] = [formValue['plantIds']]
    formValue['wbiStatus'] = 0
    formValue['applicationId'] = 2
    formValue['dob'] = this.datePipe.transform(formValue['dob'], 'yyyy-MM-dd HH:mm:ss.SSSSSS');
    formValue['adminsRoleId'] = parseInt(formValue['wbiRoleId'])
      formValue['adminsRole'] = { id: parseInt(formValue['wbiRoleId']) }
    formValue['wbiRoleId'] = parseInt(formValue['wbiRoleId'])
    formValue['adminsRoleId'] = parseInt(formValue['wbiRoleId'])
    formValue['wbiDepartmentId'] = parseInt(formValue['wbiDepartmentId'])
    console.log(formValue)
    this.adminService.registerUser(formValue).then((response) => {
      this.clickLoading = false
      if (response.responseCode == 200) {
        this.toast.showSuccessToast("user register successfully, wait for plant admin approval")
        this.registrationForm.reset()
      } else {
        this.toast.showErrorToast(response.message)
      }
    })

  }
  getPlants() {
    const data = {
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: [
        'enabled||eq||true'
      ]
    }
    const param = createAxiosConfig(data);
    this.plantService.getAllPlants(param).then((response) => {
      this.plants = response.data
    })
  }

  getDepartments() {
    const data = {
      page: 1,
      limit: 10000,
    }
    const param = createAxiosConfig(data);
    this.adminService.getDepartment(param).then((response) => {
      this.departments = response.data
    })
  }

  resendOtp() {
    this.clickLoading = true
    this.email = this.emailForm.value.email
    this.authService.sendOtp({ email: this.email }).then((response) => {
      this.clickLoading = false
      if (response['responseCode'] == 404) {
        this.toast.showErrorToast('User not found');
        return
      }
      if (response['responseCode'] == 200) {
        this.toast.showSuccessToast('OTP Sent');
        return
      }
      this.toast.showErrorToast('Internal server error');
    })
  }
}

