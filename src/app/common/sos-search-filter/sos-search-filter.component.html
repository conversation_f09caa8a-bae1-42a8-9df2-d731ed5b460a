<div style="width: 100%;">
  <form [formGroup]="form" (ngSubmit)="onSearch()" class="search-form">
    <div class="filter-container">
      <div class="filter-item1 col-md-3">
        <div>
          <!-- <label for="plantIds" class="form-label">Select Plants</label> -->
          <ng-select [ngClass]="{
              'is-invalid': showerr1
            }" style="margin-top: 0.5px;" class="custom-ng-select" [items]="plants" bindLabel="name" bindValue="id"
            [multiple]="true" placeholder="Select plants *" id="plantIds" formControlName="plantIds"
            [closeOnSelect]="false" [clearable]="true" (change)="getSelectValue($event)">
          </ng-select>
          <div *ngIf="showerr1" class="text-danger">
            <div>Please select atleast one plant.</div>
          </div>
        </div>

      </div>

      <div class="filter-item col-md-3">
        <mat-form-field appearance="outline" class="full-width" [class.mat-dense]="true">
          <mat-label>Enter Control Room Mobile No.</mat-label>
          <input matInput type="number" formControlName="toPhoneNumber" min="1" required />
        </mat-form-field>

        <div *ngIf="form.get('toPhoneNumber')?.invalid && form.get('toPhoneNumber')?.touched"
          class="error-message-mat-select text-danger">
          Control Room Mobile No. is required.
        </div>
      </div>

      <div class="col-md-2">
        <label for="fromDate" class="mb-1 fw-bold">From*</label>
        <input type="date" class="form-control" formControlName="fromDate" [max]="todayDate" required />
        <div *ngIf="form.get('fromDate')?.invalid && form.get('fromDate')?.touched" class="error-message text-danger">
          From date is required.
        </div>
      </div>

      <div class="col-md-2">
        <label for="toDate" class="mb-1 fw-bold">To*</label>
        <input type="date" class="form-control" formControlName="toDate" [max]="todayDate" required />
        <div *ngIf="form.get('toDate')?.invalid && form.get('toDate')?.touched" class="error-message text-danger">
          To date is required.
        </div>
      </div>



      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="button-search btn" mat-raised-button color="primary" type="submit">
          <i class="bi bi-search" style="margin-right: 5px;font-size: 12px !important;"></i> Search
        </button>
        <button class="button-reset btn" mat-raised-button type="button" (click)="resetSelection()">
          <i class="bi bi-arrow-repeat" style="margin-right: 5px;"></i> Reset
        </button>
      </div>
    </div>
  </form>
</div>