import { Component, EventEmitter, Output } from '@angular/core';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { PlantService } from '../../services/plant/plant.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BreadcrumbService } from '../breadcrumb/breadcrumb.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-sos-search-filter',
  templateUrl: './sos-search-filter.component.html',
  styleUrls: ['./sos-search-filter.component.scss']
})
export class SosSearchFilterComponent {
  plants: any[] = [];
  form!: FormGroup;
  todayDate: string = new Date().toISOString().split('T')[0];
  maxToDate?: string;
  showerr1: boolean = false;
  selectedPlant: any = [];
  @Output() search = new EventEmitter<any>();
  @Output() reset = new EventEmitter<any>();

  constructor(
    private plantService: PlantService,
    private router: Router,
    private fb: FormBuilder,
    private breadcrumb: BreadcrumbService
  ) {
    this.initializeForm();
    this.loadPlants();
    this.setupFromDateChangeListener();
  }

  ngOnInit() {
    this.breadcrumb.setBreadcrumbUrl(this.router.url);
  }

  private initializeForm() {
    this.form = this.fb.group({
      plantIds: [[], Validators.required],
      toPhoneNumber: ['', Validators.required],
      fromDate: ['', Validators.required],
      toDate: ['', Validators.required]
    });
  }

  private loadPlants() {
    const params = createAxiosConfig({
      page: 1,
      limit: 10000,
      sort: 'name,ASC',
      filter: ['enabled||eq||true']
    });

    this.plantService.getAllPlants(params).then(response => {
      this.plants = response.data;
    });
  }

  private setupFromDateChangeListener() {
    this.form.get('fromDate')?.valueChanges.subscribe(value => {
      this.updateMaxToDate(value);
    });
  }

  private updateMaxToDate(fromDate: string) {
    if (fromDate) {
      const selectedDate = new Date(fromDate);
      const maxDate = new Date(selectedDate);
      maxDate.setDate(selectedDate.getDate() + 30);
      this.maxToDate = maxDate.toISOString().split('T')[0];
    } else {
      this.maxToDate = undefined;
    }
  }

  getSelectValue(event: any) {
    this.selectedPlant = event;
    this.showerr1 = false
    let plant: any = [];
    let filterplantIds: any = [];

    this.selectedPlant.forEach((item: any) => {
      filterplantIds.push(item.id);
      let a = {
        id: item.id,
      };
      plant.push(a);
    });
    this.form.patchValue({ plantIds: filterplantIds });

  }

  toggleAllSelection() {
    const currentSelection = this.form.get('plantIds')?.value || [];
    const allPlantIds = this.plants.map(plant => plant.id);

    const newSelection = currentSelection.length === allPlantIds.length ? [] : allPlantIds;
    this.form.get('plantIds')?.setValue(newSelection);
  }

  resetSelection() {
    this.selectedPlant = []
    this.showerr1 = false;
    this.form.reset({
      plantIds: [],
      toPhoneNumber: '',
      fromDate: '',
      toDate: ''
    })
    this.reset.emit();
  }

  onSearch() {
    this.form.markAllAsTouched();
    if (this.selectedPlant.length === 0) {
      this.showerr1 = true;
      // setTimeout(() => {
      //   this.showerr1 = false;
      // }, 4000);
      return;
    }
    else {
      if (this.form.valid) {
        const selectedValues = this.form.value;
        this.search.emit(selectedValues);
      }
    }
  }
}
