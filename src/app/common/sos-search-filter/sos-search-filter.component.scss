:host {
    display: inline-flex;
}

.full-width {
    width: 100%;
    background-color: white;
    height: 60%;
}

.action-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    padding: 0px !important;
    margin-top: 20px !important;
    margin-left: 20px;
}

.btn {
    border: 0;
    border-radius: 30px;
    height: 39px;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    width: 110px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn i {
    margin-right: 5px;
    font-size: 16px;
}

.button-search {
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
}

.error-message {
    font-size: 12px;
}

.error-message-mat-select {
    font-size: 12px;
    margin-top: -5px;
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: flex-start;
}

.filter-container {
    display: flex;
    gap: 15px
}

.button-reset {
    color: #bd3861 !important;
    background: #fff;
    border: 1px solid;
    font-weight: 700
}

.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
    padding-top: 0px;
    padding-bottom: 0px;
    margin-top: 4%;
    /*you can tweak around with this, */
    margin-bottom: -15%;
    /*and this*/
}

/*you also have to adjust the label, after the above*/
.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label {
    top: 50%;
}

.mdc-floating-label--float-above {
    top: 80% !important;
}

::ng-deep .mat-mdc-form-field-infix {
    padding-top: 8px !important;
    padding-bottom: 5px !important;
    min-height: 5px !important;
    display: flex;
    align-items: center;
    // border: 2px solid green;
}

::ng-deep .mat-mdc-floating-label {
    // top: -6px !important; 
    padding-bottom: 15px !important;
}

::ng-deep .mat-mdc-select-arrow-wrapper {
    padding-bottom: 10px !important;
}

.filter-item {
    box-sizing: border-box;
    margin-top: 22px;
    flex: 1 1 calc(25% - 20px);
    min-width: 220px;
}

.filter-item1 {
    box-sizing: border-box;
    margin-top: 18px;
    margin-right: 50px;
    flex: 1 1 calc(10% - 10px);
    min-width: 270px;
    margin-right: 5px;
    border: 1px;
}

::ng-deep .ng-select.custom-ng-select .ng-select-container {
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    width: 270px;
    background-color: #fff;
    box-shadow: none;
    display: flex;
    align-items: flex-start;
    /* changed from center */
    flex-wrap: wrap;
    padding: 0.25rem;
}

::ng-deep .ng-select.custom-ng-select .ng-value-container {
    max-height: 64px;
    overflow-y: auto;
    padding-top: 0;
    padding-bottom: 0;
}

::ng-deep .ng-select.custom-ng-select .ng-value {
    font-size: 0.85rem;
    /* Matches input text */
    margin: 0 4px 0 0;
}

::ng-deep .ng-select.custom-ng-select .ng-value-label {
    line-height: 0.5;
    font-weight: bold;
}

::ng-deep .ng-select.custom-ng-select .ng-input input {
    font-size: 0.85rem;
    margin: 0;
    padding: 0;
}

::ng-deep .ng-select.custom-ng-select .ng-dropdown-panel .ng-option-label {
    font-size: 1rem;
    line-height: 1.5;
}

::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
    top: 22%;
    // padding-bottom: 5px;
    padding-left: 0px;
    font-size: 14px;
}

::ng-deep .ng-select .ng-select-container .ng-value-container .ng-placeholder {
    color: #595C5F;
}

::ng-deep .ng-select.custom-ng-select .ng-dropdown-panel .ng-option-label {
    font-size: 12px;
    font-weight: 600;
}

::ng-deep .ng-select.custom-ng-select .ng-value-label {
    font-size: 12px;
}