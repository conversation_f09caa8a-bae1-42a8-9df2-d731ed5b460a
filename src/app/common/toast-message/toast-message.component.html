<div
  class="toast-content-success"
  *ngIf="showToast && toastType === 'success'"
  [ngClass]="{ 'show-toast': showToast }"
>
  <div class="d-flex bd-toast">
    <div>
      <img alt="Success Icon" src="../../../../assets/svg/Success.svg" />
    </div>
    <div class="toast-text">
      {{ toastMsg }}
    </div>
    <button
      type="button"
      class="btn-close"
      (click)="resetToast()"
    ></button>
  </div>
</div>

<div
  class="toast-content-error"
  *ngIf="showToast && toastType === 'error'"
  [ngClass]="{ 'show-toast': showToast }"
>
  <div class="d-flex bd-toast">
    <div>
      <img alt="Error Icon" src="../../../../assets/svg/Error.svg" />
    </div>
    <div class="m-2 toast-text">
      {{ toastMsg }}
    </div>
    <button
      type="button"
      class="btn-close"
      (click)="resetToast()"
    ></button>
  </div>
</div>

