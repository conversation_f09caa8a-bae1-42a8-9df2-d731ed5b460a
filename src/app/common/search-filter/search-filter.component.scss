:host {
  display: block;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  justify-content: flex-start;
}

.filter-container {
  display: flex;
  gap: 5px
}

.filter-item {
  flex: 1 1 calc(25% - 20px);
  min-width: 180px;
  box-sizing: border-box;
}

.full-width {
  width: 100%;
}

.full-width-category {
  width: 60%;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  padding: 0px !important;
  margin-top: 0px !important;
}

@media (max-width: 768px) {
  .filter-item {
    flex: 1 1 100%;
  }
}


.btn {
  border: 0;
  border-radius: 30px;
  height: 39px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  width: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn i {
  margin-right: 5px;
  font-size: 16px;
}

.button-search {
  background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
}

.button-reset {
  color: #bd3861 !important;
  background: #fff;
  border: 1px solid;
  font-weight: 700
}


::ng-deep mat-form-field,
::ng-deep mat-label,
::ng-deep mat-select,
::ng-deep mat-option,
::ng-deep mat-input-element,
::ng-deep mat-form-field {
  font-family: "adani", sans-serif !important;
  // background-color: #f0f0f0 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

::ng-deep .mat-mdc-form-field-infix {
  padding-top: 12px !important;
  padding-bottom: 6px !important;
  min-height: 30px !important;
  display: flex;
  align-items: center;
  // border: 2px solid green;
}

::ng-deep .mat-mdc-icon-button.mat-mdc-button-base {
  // height: 20px !important;
  --mdc-icon-button-state-layer-size: 40px !important;
  padding: 0px !important;
}


::ng-deep .mat-mdc-floating-label {
  // top: -6px !important; 
  padding-bottom: 20px !important;
}

::ng-deep .mat-mdc-select-arrow-wrapper {
  padding-bottom: 10px !important;
}