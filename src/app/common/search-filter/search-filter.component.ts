import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';

@Component({
  selector: 'app-search-filter',
  templateUrl: './search-filter.component.html',
  styleUrl: './search-filter.component.scss'
})
export class SearchFilterComponent {
  @Input() filters: any[] = []; 
  @Output() search = new EventEmitter<any>(); 
  @Output() reset = new EventEmitter<any>();

  searchForm!: FormGroup;
  previousSearchValue:any
  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm() {
    const formControls: { [key: string]: FormControl } = {};
    this.filters.forEach((filter) => {
      if (filter.key) {
        formControls[filter.key] = new FormControl('');
      }
    });
    this.searchForm = new FormGroup(formControls);
  }
  

  onSearch() {

    if(!this.isFormEmpty()){
      const currentValue = this.searchForm.value;
      const isSameAsPrevious = this.isSameSearch(currentValue, this.previousSearchValue);
  
      if (!isSameAsPrevious) {
        this.previousSearchValue = currentValue // Update the previous search value
        this.search.emit(currentValue); // Emit the new search value
      } else {
        console.log("Search skipped: Same search value as the previous.");
      } 

    }
    
  }
  isSameSearch(currentValue: any, previousValue: any): boolean {
    return JSON.stringify(currentValue) === JSON.stringify(previousValue);
  }

  filterAutocompleteOptions(key: string, event: any) {
    const input = event.target.value;
    if (!key) {
      console.warn('Autocomplete invoked with a null or undefined key.');
      return;
    }
    const filter = this.filters.find((f) => f.key === key);
    if (filter && filter.options) {
      const valueField = filter.valueField || 'title'; // Fallback to 'title'
      filter.filteredOptions = filter.options.filter((option: any) =>
        option[valueField]?.toLowerCase().includes(input.toLowerCase())
      );
    }
  }
  

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['filters']) {
    }
  }
  isFormEmpty(): boolean {
    return Object.values(this.searchForm.value).every((value) => !value);
  }

  resetFields(){
    if (!this.isFormEmpty()) {
      this.searchForm.reset();
      this.reset.emit(true);
    }
    else{
      this.reset.emit(false)
    }
  }
}


