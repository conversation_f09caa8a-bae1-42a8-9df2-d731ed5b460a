<form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
  <div class="filter-container">
    <ng-container *ngFor="let filter of filters">
      <ng-container [ngSwitch]="filter.type">
        <div *ngSwitchCase="'text'" class="filter-item">
          <mat-form-field appearance="fill" class="full-width" class="custom-mat-field" appearance="outline">
            <mat-label>{{ filter.label }}</mat-label>
            <input style="border:2px solid green" matInput type="text" [formControlName]="filter.key"
              placeholder="Enter {{ filter.label }}" />
          </mat-form-field>
        </div>

        @if (filter?.key === 'category') {
        <div *ngSwitchCase="'dropdown'" style="margin-right: -60px;">
          <mat-form-field appearance="fill" class="full-width-category" appearance="outline">
            <mat-label>{{ filter.label }}</mat-label>
            <mat-select [formControlName]="filter.key">
              <mat-option *ngFor="let option of filter.options" [value]="option[filter.valueField]">
                {{ option[filter.titleField] }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        }
        @else {
        <div *ngSwitchCase="'dropdown'" class="filter-item">
          <mat-form-field appearance="fill" class="full-width" appearance="outline">
            <mat-label>{{ filter.label }}</mat-label>
            <mat-select [formControlName]="filter.key">
              <mat-option *ngFor="let option of filter.options" [value]="option[filter.valueField]">
                {{ option[filter.titleField] }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        }
        <div *ngSwitchCase="'autocomplete'" class="filter-item">
          <mat-form-field appearance="fill" class="full-width" appearance="outline">
            <mat-label>{{ filter.label }}</mat-label>
            <input matInput type="text" [formControlName]="filter.key" [matAutocomplete]="auto"
              (input)="filterAutocompleteOptions(filter.key, $event)" />
            <mat-autocomplete #auto="matAutocomplete">
              <mat-option *ngFor="let option of filter.filteredOptions || filter.options" [value]="option.valueField">
                {{ option.titleField }}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
        </div>
        <div *ngSwitchCase="'date'" class="filter-item">
          <mat-form-field class="full-width" appearance="outline">
            <mat-label>{{ filter.label }}</mat-label>
            <input matInput [matDatepicker]="picker" [formControlName]="filter.key" placeholder="Select Date">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
      </ng-container>
    </ng-container>

    <div class="action-buttons">
      <button class="button-search btn" mat-raised-button color="primary" type="submit">
        <i class="bi bi-search" style="margin-right: 5px;font-size: 12px !important;"></i> Search
      </button>
      <button class="button-reset btn" mat-raised-button type="button" (click)="resetFields()">
        <i class="bi bi-arrow-repeat" style="margin-right: 5px;"></i> Reset
      </button>
    </div>
  </div>
</form>