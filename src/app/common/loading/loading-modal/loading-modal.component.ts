import { Component, ElementRef, ViewChild } from '@angular/core';
declare var bootstrap: any;

@Component({
  selector: 'app-loading-modal',
  templateUrl: './loading-modal.component.html',
  styleUrls: ['./loading-modal.component.scss'],
})
export class LoadingModalComponent {
  @ViewChild('loadingModal', { static: true }) modalElement!: ElementRef;

  showModal(): void {
    const modal = new bootstrap.Modal(this.modalElement.nativeElement);
    modal.show();
  }

  closeModal(): void {
    console.log('modal hide')
    const modal = bootstrap.Modal.getInstance(this.modalElement.nativeElement);
    modal.hide();
  }
}


