import { Component, OnInit } from '@angular/core';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

@Component({
  selector: 'app-pdf',
  templateUrl: './pdf.component.html',
  styleUrl: './pdf.component.scss'
})

export class PdfComponent implements OnInit {
plants: any;

ngOnInit(): void {
  this.plants = [1, 2, 3, 4];
  // Generate PDF once the page loads
  setTimeout(() => {
    window.print();
  }, 1000); // Delay to ensure the page is fully rendered
}
generatePDF(): void {
  const containers = document.querySelectorAll('.container') as NodeListOf<HTMLElement>;
  
  const doc = new jsPDF({
    unit: 'px', // Unit in pixels
    format: [595, 842] // A4 size in px
  });

  const margin = 10; // Define a margin
  let pageHeight = 842; // A4 height in px
  let yPosition = margin; // Starting position for the first container

  containers.forEach((container, index) => {
    if (index > 0) {
      doc.addPage(); // Add a new page after the first container
      yPosition = margin; // Reset Y position when adding a new page
    }

    doc.html(container, {
      callback: (doc: jsPDF) => {
        // Check if the content goes beyond the page height and adjust the Y position
        if (yPosition + 500 > pageHeight) { // Adjust 500 based on your content size
          doc.addPage();
          yPosition = margin; // Reset Y position for the new page
        }

        // Update Y position after each container
        yPosition += 500; // Adjust based on the height of your content

        // Save the PDF only once all containers are rendered
        if (index === containers.length - 1) {
          doc.save('pdf-export');
        }
      },
      margin: [margin, margin, margin, margin], // Margins for content
      x: margin, // Horizontal position
      y: yPosition, // Vertical position
      width: 575, // Content width (ensuring it fits A4 size)
      windowWidth: 800 // This ensures the page renders properly on smaller screens
    });
  });
}








}
