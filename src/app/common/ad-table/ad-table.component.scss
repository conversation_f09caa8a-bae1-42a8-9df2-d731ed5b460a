$fontColor5: #161616;
$fontColor6: #ffffff;
$fontColor10: #464646;


$backgroundColor0: transparent;
$backgroundColor1: #ffffff;
$backgroundColor2: #2B67C3;
$backgroundColor7: #555555;
$backgroundColor8: #CED4DA;

$hoverColor1: #043b8e;

$borderColor2: #CED4DA;
$borderColor3: #6B6B6E;

.table {
    margin-bottom: 0;
    border: 1px solid $backgroundColor8 !important;
}

.mdc-data-table__header-cell{
    padding: 0 16px 0 16px !important;
}
.mdc-data-table__cell, .mdc-data-table__header-cell {
    padding: 0 16px 0 16px !important;
}

.custom-header-padding{
    padding: 0 6px 0 6px !important;
}

.bg-white {
    background-color: $backgroundColor1;
}

.mat-elevation-z8 {
    margin: 16px 0;
    padding: 16px;
    border-radius: 12px;
    overflow-x: auto;
}
.mdc-data-table__table{
    max-width: 150% !important;
}

.user-action {
    margin-bottom: 10px;
    float: right;
    display: flex;
    vertical-align: middle;
    flex-direction: row-reverse;

    .excel {
        height: 21px;
        cursor: pointer;
    }

    .user-setting {
        height: 21px;
        cursor: pointer;

    }

    .fliter {
        height: 21px;
        margin-right: 0px !important;

    }

    .excel:hover {
        // border: solid 1px $hoverColor1;
        background: $backgroundColor8;
    }

    .fliter:hover {
        // border: solid 1px $hoverColor1;
        background: $backgroundColor8;
    }

    .user-setting:hover {
        // border: solid 1px $hoverColor1;
        background: $backgroundColor8;
    }

    .icon-border {
        background: $backgroundColor1;
        border: 1px solid $borderColor2;
        border-radius: 2px;
        // padding: 7px;
        margin-right: 13px;
        height: 29px;

        img {
            padding: 7px;
        }
    }

    .search-icon {
        background: url('../../../assets/img/icons/search.png') no-repeat scroll 10px 7px;
        padding-left: 30px;
        background-size: 10px;
    }
}

.input-text-ty1 {
    border: 1px solid $borderColor3;
    border-radius: 6px !important;
    flex: none;
    order: 2;
    flex-grow: 0;
    z-index: 0;
    box-sizing: border-box;
    width: 250px;
    // height: 29px;
    font-weight: 400;
    font-size: 12px;
    color: $fontColor5
}

.input-text-ty1:focus-visible {
    outline: none;
}

.mat-table {
    border: 1px solid $backgroundColor8;
}

.pagination-content {
    // border: solid 0.5px #CED4DA;
    background-color: $backgroundColor1;
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 0;
    flex: 0.9;
}

.pagination button {
    // background-color: #007bff;
    color: #464646;
    border: none;
    padding: 10px 15px;
    margin: 0 5px;
    background-color: $backgroundColor1;
    cursor: pointer;
}

.pagination button.active {
    // background-color: #0056b3;
    background: linear-gradient(90deg, #0B74B0 0%, #75479C 52.08%, #BD3861 100%);
    padding: 9px;
    border-radius: 9px;
    padding: 1px 9px;
    border-radius: 9px;
    color: #fff;
}

.pagination button[disabled] {
    background-color: transparent;
    cursor: not-allowed;
}

button {
    font-family: 'adani';
    font-size: 14px;
    font-weight: 500;
}

.pg-dp-label {
    font-size: 16px;
    font-weight: 500;
    margin-right: 15px;
    margin-left: 20px;
}

.form-select {
    width: 100px !important;
}

.total {
    margin-right: 15px;
}

.total span {
    font-size: 15px;
    font-weight: 700;
    margin-left: 15px;
}

tbody tr .status {
    color: #11AF22;
}

tbody tr .link {
    cursor: pointer;
}

tbody td {
    text-align: justify;
}

.table-responsive {
    // min-height: 395px;
    height: auto;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #f9f9f9;
    min-width: 110px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

/* Show the dropdown menu on hover */
.dropdown:hover .dropdown-content {
    display: block;
}

/* Style the dropdown links */
.dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

/* Change color of dropdown links on hover */
.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.no-data {
    img {
        /* input */
        cursor: no-drop;
    }

    img:hover {
        color: white;
    }

    .icon-border:hover {
        background-color: white;
    }
}

thead tr th:first-child,
tbody tr td:first-child {
    padding-left: 30px !important;
}

.btn-status {
    font-weight: 400;
    border: 2px solid;
    padding: 5px 30px;
    border-radius: 5px;
    /* border-color: red;
    background: red; */
    color: white;
    // width: 70px;
    width: 100px;
    text-align: center;
    white-space: nowrap;

    &.sent {
        border-color: #0B74B0 !important;
        background-color: #0B74B0 !important;
    }

    &.reject {
        border-color: #B00005 !important;
        background-color: #B00005 !important;
    }

    &.accept {
        border-color: #219A0D !important;
        background-color: #219A0D !important;
    }

    &.hold {
        border-color: #00A6B0 !important;
        background-color: #00A6B0 !important;
    }

    &.pending {
        border-color: #00A6B0 !important;
        background-color: #00A6B0 !important;
    }

    &.processing {
        border-color: #D79E01 !important;
        background-color: #D79E01 !important;
    }

    &.completed {
        border-color: #219A0D !important;
        // color:white;
        background-color: #219A0D !important;
    }

}

.modal-track {
    width: 840px !important;

    .modal-body {
        overflow: auto;
    }

    .modal-content {
        background-color: #F9F9F9;
    }

    .d-flex {
        padding: 10px;
        background-color: #ffffff;
        border-radius: 5px !important;
    }

    p {
        font-size: 12px !important;
    }

    .circle {
        height: 14px;
        width: 14px;
        border: 1px solid #00A6B0;
        border-radius: 40px;
    }

    .accept {
        background-color: #11AF22;
        border-color: #11AF22;
    }

    .pvr {
        border-left: 2px dotted;
        color: #cdbebe;
        height: 80%;
        margin: 5px 5px;
    }
}

.noRecond {
    text-align: center;
    color: var(--borderColor2);

    margin-top: 3%;
    height: 60vh;
    width: 100%;

    i {
        display: block;
        font-size: 93px;
    }

    h3 {
        margin-top: 10px;
        font-size: 10px;
    }
}

.modal.left .modal-dialog,
.modal.right .modal-dialog {
    position: fixed;
    margin: auto;
    width: 320px;
    height: 100%;
    -webkit-transform: translate3d(0%, 0, 0);
    -ms-transform: translate3d(0%, 0, 0);
    -o-transform: translate3d(0%, 0, 0);
    transform: translate3d(0%, 0, 0);
}

.modal.left .modal-content,
.modal.right .modal-content {
    height: 100%;
    overflow-y: auto;
    border-radius: 0;
}

.modal.left .modal-body,
.modal.right .modal-body {
    padding: 15px 15px 80px;
}

/*Left*/
.modal.left.fade .modal-dialog {
    left: 0;
    -webkit-transition: opacity 0.3s linear, left 0.3s ease-out;
    -moz-transition: opacity 0.3s linear, left 0.3s ease-out;
    -o-transition: opacity 0.3s linear, left 0.3s ease-out;
    transition: opacity 0.3s linear, left 0.3s ease-out;
}

.modal.left.fade.in .modal-dialog {
    left: 0;
}

/*Right*/
.modal.right.fade .modal-dialog {
    right: 0;
    -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
    -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
    -o-transition: opacity 0.3s linear, right 0.3s ease-out;
    transition: opacity 0.3s linear, right 0.3s ease-out;
}

.modal.right.fade.in .modal-dialog {
    right: 0;
}

.bt-action-section {

    .bt-action {
        margin-left: 10px;
    }

    img {
        height: 20px;
        width: 20px;
    }

    a {
        cursor: pointer;
    }
}

.hidden {
    display: none;
}

:host {
    display: block;
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: flex-start;
}

.filter-container {
    // display: flex;
    // gap: 5px
}

.filter-item {
    // flex: 1 1 calc(25% - 20px);
    min-width: 180px !important;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 15px;
}

.full-width {
    width: 100%;
}

.action-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    padding: 0px !important;
    margin-top: 10px !important;
}

@media (max-width: 768px) {
    .filter-item {
        //   flex: 1 1 100%;
        width: 100%;
    }
}


.btn {
    border: 0;
    border-radius: 30px;
    height: 39px;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn i {
    margin-right: 5px;
    font-size: 16px;
}

.button-search {
    background: linear-gradient(90deg, #0b74b0 0%, #75479c 52.08%, #bd3861 100%);
}

.button-reset {
    color: #bd3861 !important;
    background: #fff;
    border: 1px solid;
    font-weight: 700
}

.button-submit,
.button-back {
    width: 110px;
}

label {
    font-size: small;

}

.action {
    height: 30px;
    width: 30px;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    /* Space between checkbox and label */
    margin-bottom: 8px;
    /* Add spacing between items */
    padding-left: 20px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    /* Space between checkbox and label */
    padding-bottom: 10px;
    padding-top: 10px;
    width: 100%;
}

/* Bigger checkbox */
.custom-checkbox {
    width: 20px;
    height: 20px;
}

/* Make label bold and medium font size */
.checkbox-label {
    font-weight: bold;
    font-size: 16px;
}

/* Apply border-bottom only if it's not the last element */
.border-bottom {
    border-bottom: 1px dashed #e2d0d0 !important;
}

.no-data-found {
    text-align: center;
    margin-top: 20px;
    font-size: 16px;
    color: #888;
}

.fixed-width {
    width: 150px;
    /* Adjust width as needed */
    white-space: normal;
    word-wrap: break-word;
}

.mat-header-cell,
.mat-cell {
    overflow: hidden;
    text-overflow: ellipsis;
}

td,
th {
    white-space: normal;
    word-wrap: break-word;
    max-width: 400px;
}

.highlight {
    font-weight: bold;
    color: #1A73E8;
    font-size: 12px;
}

.backupImg {
    height: 30%;
    width: 30%;
    cursor: pointer;
}

.highlightText {
    font-size: 12px;
    margin-left: 10px;
}

.custom-download-img{
    height: 40% !important;
    width: 40%  !important;
    cursor: pointer;
    margin-left: 12px;
}

.custom-backup-img{
    height: 22% !important;
    width: 22%  !important;
    cursor: pointer;
    margin-left: 22px;
}