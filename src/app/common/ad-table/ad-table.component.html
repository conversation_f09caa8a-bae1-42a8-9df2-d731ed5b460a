<div class="mat-elevation-z8 bg-white">
  <div>
    <div class="row mt-2">
      <div class="col-sm">
        <h6 *ngIf="isTableTitle" class="page-sub-header m-t-revert">{{tableTitle}}</h6>
      </div>

      <div class="col-sm-7" *ngIf="pagedData.length>0">
        <div class="user-action">
          <input placeholder="Search..." (input)="applyFilter($event)" type="text"
            class="input-text-ty1 icon-border d-inline-block search-icon" id="searchinput" [(ngModel)]="searchText"
            *ngIf="isSearchShow">
          <div class="fliter icon-border" *ngIf="isTableFilter">
            <img src="../../../assets/img/icons/filter.png" (click)="openTableFilterModal()">
          </div>

          <div class="user-setting icon-border" *ngIf="isTableFilter">
            <img src="../../../assets/img/icons/user-setting.png" class="" (click)="openTableSettingModal()">
          </div>

          <div class="dropdown excel icon-border" *ngIf="isTableFilter">

            <img src=" ../../../assets/img/icons/excel.png" class="" alt="">

            <div class="dropdown-content">
              <a href="javascript:void(0);" (click)="exportToExcel()">Excel</a>
            </div>
          </div>
        </div>
      </div>

      <!-- No Data Available -->
      <div class="col-sm-7" *ngIf="pagedData.length==0">
        <div class="user-action no-data">
          <input placeholder="Search here..." (input)="applyFilter($event)" type="text" [(ngModel)]="searchText"
            class=" input-text-ty1 icon-border d-inline-block search-icon" id="searchinput" *ngIf="isSearchShow">

          <div class="fliter icon-border" *ngIf="isTableFilter">
            <img src="../../../assets/img/icons/filter.png" class="" alt="" data-bs-toggle="modal"
              data-bs-target="#filterModal">
          </div>

          <div class="user-setting icon-border" *ngIf="isTableFilter">
            <img src="../../../assets/img/icons/user-setting.png" class="" alt="">
          </div>

          <div class="dropdown excel icon-border" *ngIf="isTableFilter">

            <img src=" ../../../assets/img/icons/excel.png" class="" alt="">

            <div class="dropdown-content">
              <a href="javascript:void(0);" (click)="exportToExcel()">Excel</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <table mat-table [dataSource]="dataSource" class="mat-table" matSort [style.min-width]="tableTitle=='Uploaded Listing' ? '105%':'100%'" >
    <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column.key">
      <th mat-header-cell *matHeaderCellDef mat-sort-header
        [ngStyle]="{'width': column?.width ? column?.width : '100px'}" class="fixed-width" [ngClass]="tableTitle=='Uploaded Listing'? 'custom-header-padding':''">
        {{ column.header }}
      </th>
      <td mat-cell *matCellDef="let element" class="fixed-width" [ngStyle]="{'width': column?.width ? column?.width : '150px'}" [ngClass]="tableTitle=='Uploaded Listing'? 'custom-header-padding':''">
        <ng-container *ngIf="column.key.toLocaleLowerCase() !== 'status'">
          <span>
            {{ element[column.key] }}
          </span>
        </ng-container>

        <ng-container *ngIf="column.key.toLocaleLowerCase() == 'status'">
          <span class="m-0 btn-status" [ngClass]="{
              'accept': element[column.key] == 'Open',
              'reject': element[column.key] == 'Close' || element[column.key] == 'Rejected'
            }">
            {{ element[column.key] }}
          </span>
        </ng-container>

        <ng-container *ngIf="column.key.toLocaleLowerCase() == 'action'">
          <div style="gap: 10px;" class="d-flex">
            <img class="action"
              *ngIf="element['status'] == 'Open'|| element['status'] == '!Close' || element['status'] == '!Rejected'"
              alt="" src="../../../assets/svg/actionbutton.svg" title="Mark Item As Lost/Found"
              (click)="onEdit(element)" />
            <img *ngIf="element['status'] == 'Open'" class="action" alt="" src="../../../assets/svg/tickicon.svg"
              title="Approve Item" (click)="onApprove(element)" />
            <img *ngIf="element['status'] == 'Close' || element['status'] == 'Rejected'" class="action" alt=""
              title="View Item" src="../../../assets/svg/eye-scan.svg" (click)="onEyeClick(element)" />
          </div>
        </ng-container>

        <ng-container *ngIf="column.key.toLocaleLowerCase() == 'incidentaction'">
          <div style="gap: 10px;" class="d-flex">
            <i class="bi bi-pencil-fill" style="cursor: pointer; color: #634ad3; font-size: 16px;"
              (click)="onApprove(element)" matTooltip="Edit Incident"></i>
            <i class="bi bi-x-circle-fill" (click)="onSoftDelete(element)"
              style="cursor: pointer; color: #e23939; font-size: 18px;" matTooltip="Delete Incident"></i>
          </div>
        </ng-container>

        <ng-container *ngIf="column.key.toLocaleLowerCase() == 'downloadfile'">
          <!-- <button class="btn btn-sm btn-outline-primary" (click)="downloadExcelFromUrl('test', 'download')"> -->
          <!-- <i class="bi bi-download me-1" (click)="downloadFile(element, 'download')"> Download File</i> -->

          <!-- </button> -->
          <img style="height: 30%; width: 30%; cursor: pointer;" title="Download File"
            src="../../../assets/svg/download-file.png" (click)="downloadFile(element)" [ngClass]="tableTitle=='Uploaded Listing'? 'custom-download-img':''" />

        </ng-container>

        <ng-container *ngIf="column.key.toLocaleLowerCase() == 'backupicon'">
          <img class="backupImg" title="Backup / Rollback file" src="../../assets/svg/data-recovery.png" [ngClass]="tableTitle=='Uploaded Listing'? 'custom-backup-img':''"
            (click)="backupEvent(element)" />
          <span class="highlightText" *ngIf="element.version == version" [class.highlight]="element.showHighlight">
            Current
          </span>

        </ng-container>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="columnsToDisplay"></tr>
    <tr mat-row *matRowDef="let row; columns: columnsToDisplay;"></tr>
  </table>

  <div *ngIf="!dataSource || dataSource?.data?.length === 0" class="no-data-found">
    <p>No data found</p>
  </div>
  <div class="pagination-content mb-4" *ngIf="pagedData && pagedData.length > 0">
    <div class="pagination">
      <button class="btn" (click)="prevPage()" [disabled]="currentPage === 1">&lt;</button>
      <ng-container *ngFor="let page of visiblePages">
        <button (click)="goToPage(page)" [class.active]="currentPage === page">{{ page
          }}</button>
      </ng-container>
      <button (click)="nextPage()" [disabled]="currentPage === totalPages">&gt;</button>
      <label for="pageDropdown" class="pg-dp-label">Jump to </label>
      <select class="form-select" id="pageDropdown" [(ngModel)]="currentPage" (change)="onPageChange($event)">
        <option *ngFor="let page of pages" [value]="page">{{ page }}</option>
      </select>
    </div>
    <div class="total">Total: <span>{{ totalItems }}</span></div>
  </div>


</div>

<app-custom-modal [title]=" 'Select Column Header'" [width]="'30%'" *ngIf="settingModal" (onClickCross)="onClickCross()"
  (closeModal)="closeSettingModal()">
  <div class="mb-0">
    <form [formGroup]="columnForm" style="text-align: justify">
      <div formArrayName="settingControlArray">
        <div *ngFor="let control of getSettingControlArray().controls; let i = index" class="form-check">
          <div [ngClass]="{'border-bottom': i !== getSettingControlArray().controls.length - 1}"
            class="checkbox-container">
            <input type="checkbox" [formControlName]="i" class="custom-checkbox" id="checkbox-{{ i }}" />
            <label class="form-check-label checkbox-label" for="checkbox-{{ i }}">
              {{ displayedColumns[i]?.header }}
            </label>
          </div>
        </div>
      </div>

      <div style="margin-left: 20px;" class="d-flex mt-4">
        <button type="submit" id="bt_approve" style="width: 130px;" class="button-submit button-left"
          (click)="applySetting()">
          <span class="ps-2"> Apply</span>
          <img alt="" src="../../../assets/svg/right-pink.svg" style="margin-left: -45px" class="left-arrow" />
        </button>
        <button type="button" id="bt_reject" class="button-back button-left" style="width: 130px;"
          (click)="resetSetting()">
          <span class="ps-2"><b>Reset</b></span>
          <img style="margin-left: -35px" alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
        </button>
      </div>
      <!-- <div class="bt-section mt-5 justify-content-start">
            <button type="submit" class="button-submit" data-bs-dismiss="modal" (click)="applySetting()">Apply</button>
            <button class="button-back" data-bs-dismiss="modal" (click)="resetSetting()">Reset</button>
          </div> -->
    </form>
  </div>

</app-custom-modal>

<app-custom-modal [title]="'Filter'" [width]="'30%'" *ngIf="filterModal" (onClickCross)="onClickCross()"
  (onClickCross)="onClickCross()" (closeModal)="closeFilterModal()">
  <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
    <div class="m-2">
      <ng-container *ngFor="let filter of filters" class="test">
        <ng-container [ngSwitch]="filter.type">
          <div *ngSwitchCase="'text'" class="filter-item">
            <label class="ml-12"><b>{{ filter.label }}</b></label>
            <input class="form-control" type="text" [formControlName]="filter.key"
              placeholder="Enter {{ filter.label }}" />
          </div>


          <div *ngSwitchCase="'dropdown'" class="filter-item">
            <label class="ml-12"><b>{{ filter.label }}</b></label>
            <select class="form-control" [formControlName]="filter.key">
              <option value="">Select {{ filter.label }}</option>
              <option *ngFor="let option of filter.options" [value]="option[filter.valueField]"> {{
                option[filter.titleField] }}</option>
            </select>
          </div>


          <div *ngSwitchCase="'date'" class="filter-item">
            <label><b>{{ filter.label }}</b></label>
            <input type="date" class="form-control" [formControlName]="filter.key" id="date" />
          </div>
        </ng-container>
      </ng-container>


      <div class="row">
        <div class="d-flex mt-4">
          <button type="submit" id="bt_approve" style="width: 130px;" class="button-submit button-left">
            <span class="ps-2"> Submit</span>
            <img alt="" src="../../../assets/svg/right-arrow.svg" style="margin-left: -45px" class="left-arrow" />
          </button>
          <button type="button" id="bt_reject" (click)="resetSearch()" class="button-back button-left"
            style="width: 130px;">
            <span class="ps-2">Reset</span>
            <img style="margin-left: -35px" alt="" src="../../../assets/svg/Edit-User.svg" class="left-arrow" />
          </button>
        </div>
      </div>
    </div>
  </form>
</app-custom-modal>