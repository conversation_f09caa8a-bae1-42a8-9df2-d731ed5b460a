import { Component, EventEmitter, Input, output, Output, SimpleChange, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { FormService } from '../Services/form-service.service';

@Component({
  selector: 'app-ad-table',
  templateUrl: './ad-table.component.html',
  styleUrl: './ad-table.component.scss'
})
export class AdTableComponent {
  @Input() data: any[] = [];
  displayedColumns!: { key: string, header: string, width?: string, height?: string, style?: string }[];
  @Input() columns: { key: string, header: string, width?: string, height?: string, style?: string }[] = [];
  @Input() isTableTitle: boolean = false;
  @Input() isTableFilter: boolean = false;
  @Input() isSearchShow: boolean = false;
  @Input() tableData: any[] = [];
  @Input() searchObject: any[] = [];
  @Input('tableTitle') tableTitle: any;
  @Input() filters: any[] = [];
  @Output() search = new EventEmitter<any>();
  @Output() reset = new EventEmitter<any>();
  @Output() edit = new EventEmitter<any>();
  @Output() approve = new EventEmitter<any>();
  @Output() delete = new EventEmitter<any>();
  @Output() view = new EventEmitter<any>();
  @Output() downloadExcelFromUrl = new EventEmitter<any>();
  @Output() backupRollBackClick = new EventEmitter<any>();
  @Input() version: any;
  displayedColumnKeys!: string[];
  dataSource!: MatTableDataSource<any>;
  searchText: any = '';
  public pagedData: any[] = [];
  pages: number[] = [1, 2, 3];
  totalPages: number = 0;
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalItems: number = 0;
  deleteItemId: number = 0;
  page: number = 1
  visiblePages: number[] = [];
  public apiPagedData: any[] = [];
  dynamicSearchForm !: FormGroup;
  columnForm!: FormGroup
  displayedColumns_constant: any;
  searchForm !: FormGroup;
  columnsToDisplay: any[] = this.displayedColumnKeys?.slice();
  settingModal: boolean = false;
  filterModal: boolean = false;
  previousSearchValue: any = {};
  showHighlight = false;

  constructor(private fb: FormBuilder, private fs: FormService) {

  }
  ngOnChanges(changes: SimpleChange): void {
    console.log('changes', changes)
    this.pagedData = this.data
    this.dataSource = new MatTableDataSource(this.pagedData);
    this.displayedColumns = this.columns;
    this.displayedColumnKeys = this.columns.map(col => col.key);
    this.dataSource.data = this.pagedData
    this.version = this.version
    this.columnsToDisplay = this.displayedColumnKeys?.slice();
    this.columnForm = this.fb.group({
      settingControlArray: this.fb.array(this.displayedColumns.map(() => this.fb.control(true)))
    })
    this.dynamicSearchForm = this.fb.group({})
    this.totalItems = this.data.length;
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    this.updatePagination();
    if (this.data) {
      this.data.forEach(element => {
        if (element.version === this.version) {
          element.showHighlight = true;
          setTimeout(() => {
            element.showHighlight = false;
          }, 3000);
        }
      });
    }
  }
  updatePagination(): void {
    this.pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
    this.updateVisiblePages();
    this.paginateData();
  }
  paginateData(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.pagedData = this.data.slice(startIndex, endIndex);
    this.dataSource.data = this.pagedData
  }
  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }
  exportToExcel(): void {
    // Extract only the displayed columns and visible data
    const exportData = this.pagedData.map(row => {
      const filteredRow: any = {};
      this.displayedColumns.forEach(colKey => {
        filteredRow[colKey.header] = row[colKey.key];
      });
      return filteredRow;
    });

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Visible Data");

    // Create a Blob and trigger download
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(dataBlob, 'TableData.xlsx');
  }

  get startIndex(): number {
    return (this.currentPage - 1) * this.itemsPerPage;
  }

  get endIndex(): number {
    return this.currentPage * this.itemsPerPage;
  }

  get serialNumberStart(): number {
    return this.startIndex + 1;
  }
  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  goToPage(page: number): void {
    this.currentPage = page;
    this.updatePagination();
  }


  updatePagedData(): void {

    if (this.data.length > 0 && Object.values(this.data)) {
      this.apiPagedData = this.data ? this.data.slice(this.startIndex, this.endIndex) : [];
      this.pagedData = this.data ? this.data.slice(this.startIndex, this.endIndex) : [];
    }
    // console.log("pagedData", this.pagedData);

  }


  onPageChange(event: any): void {
    this.currentPage = Number(event.target.value);
    this.updatePagination();
  }
  updateVisiblePages() {
    this.visiblePages = this.pages.slice(
      Math.max(0, this.currentPage - 3),
      Math.min(this.totalPages, this.currentPage + 2)
    );
  }

  applySearch() {

  }
  resetSearch() {
    this.searchForm.reset()
    this.reset.emit();

  }
  getCheckedColumns(): { key: string, header: string }[] {
    return this.getSettingControlArray().value
      .map((checked: boolean, index: number) => (checked ? this.columns[index] : null))
      .filter((column: any) => column !== null);
  }

  applySetting() {
    const selectedColumns = this.getCheckedColumns();
    this.columnsToDisplay = this.columnsToDisplay.filter(column =>
      selectedColumns.some(selected => selected.key === column.key)
    );

    // Add newly selected columns that are not already in columnsToDisplay
    selectedColumns.forEach(selectedColumn => {
      if (!this.columnsToDisplay.some(col => col.key === selectedColumn.key)) {
        this.columnsToDisplay.push(selectedColumn.key);
      }
    });

    console.log('Updated columnsToDisplay:', this.columnsToDisplay);
    this.onClickCross()
  }
  resetSetting() {
    this.columnForm.reset()
  }
  openTableSettingModal() {
    this.settingModal = true;
    let controlArray = this.columnForm.get('settingControlArray') as FormArray;
    console.log('controlArray', controlArray);
  }
  getSettingControlArray(): FormArray {
    return this.columnForm.get('settingControlArray') as FormArray;
  }
  closeSettingModal() {
    this.settingModal = false;
  }
  onClickCross() {
    this.settingModal = false;
    this.filterModal = false;
  }
  openTableFilterModal() {
    this.filterModal = true;
  }
  closeFilterModal() {
    this.filterModal = false;
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm() {
    const formControls: { [key: string]: FormControl } = {};
    this.filters.forEach((filter) => {
      if (filter.key) {
        formControls[filter.key] = new FormControl('');
      }
    });
    this.searchForm = new FormGroup(formControls);
  }


  onSearch() {
    this.fs.trimFormValues(this.searchForm)
    if (!this.isFormEmpty()) {
      const currentValue = this.searchForm.value;
      // Remove empty values
      const filteredSearchValue = Object.fromEntries(
        Object.entries(currentValue).filter(([_, value]) => value !== null && value !== undefined && value !== '')
      );

      if (Object.keys(filteredSearchValue).length > 0) { // Ensure at least one value exists
        const isSameAsPrevious = this.isSameSearch(filteredSearchValue, this.previousSearchValue);

        if (!isSameAsPrevious) {
          this.previousSearchValue = filteredSearchValue; // Update the previous search value
          this.search.emit(filteredSearchValue); // Emit only non-empty values
        } else {
          console.log("Search skipped: Same search value as the previous.");
        }
      }

    }

  }
  isSameSearch(currentValue: any, previousValue: any): boolean {
    return JSON.stringify(currentValue) === JSON.stringify(previousValue);
  }

  filterAutocompleteOptions(key: string, event: any) {
    const input = event.target.value;
    if (!key) {
      console.warn('Autocomplete invoked with a null or undefined key.');
      return;
    }
    const filter = this.filters.find((f) => f.key === key);
    if (filter && filter.options) {
      const valueField = filter.valueField || 'title'; // Fallback to 'title'
      filter.filteredOptions = filter.options.filter((option: any) =>
        option[valueField]?.toLowerCase().includes(input.toLowerCase())
      );
    }
  }

  isFormEmpty(): boolean {
    return Object.values(this.searchForm.value).every((value) => !value);
  }

  resetFields() {
    if (!this.isFormEmpty()) {
      this.searchForm.reset();
      this.reset.emit(true);
    }
    else {
      this.reset.emit(false)
    }
  }
  onEdit(item: any) {
    this.edit.emit(item);
  }

  onApprove(item: any) {
    this.approve.emit(item);
  }
  onEyeClick(item: any) {
    this.view.emit(item);
  }

  onSoftDelete(item: any) {
    this.delete.emit(item);
  }

  downloadFile(item: any) {
    this.downloadExcelFromUrl.emit(item)
  }

  backupEvent(item: any) {
    this.backupRollBackClick.emit(item)
  }

}
