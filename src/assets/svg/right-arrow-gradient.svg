<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.8">
<g filter="url(#filter0_d_2851_2391)">
<circle cx="23.5" cy="19.5" r="19.5" transform="rotate(-90 23.5 19.5)" fill="url(#paint0_linear_2851_2391)"/>
<circle cx="23.5" cy="19.5" r="19" transform="rotate(-90 23.5 19.5)" stroke="white"/>
</g>
<g clip-path="url(#clip0_2851_2391)">
<path d="M27.964 18.9416L20.892 12.7937C20.5817 12.5245 20.079 12.5245 19.7679 12.7937C19.4576 13.0629 19.4576 13.5001 19.7679 13.7692L26.2789 19.4294L19.7687 25.0895C19.4584 25.3586 19.4584 25.7959 19.7687 26.0657C20.079 26.3349 20.5825 26.3349 20.8928 26.0657L27.9647 19.9178C28.2704 19.6514 28.2704 19.2073 27.964 18.9416Z" fill="white" stroke="white" stroke-width="0.691568"/>
</g>
</g>
<defs>
<filter id="filter0_d_2851_2391" x="0.716989" y="0" width="45.566" height="45.566" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28301"/>
<feGaussianBlur stdDeviation="1.64151"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.713726 0 0 0 0 0.223529 0 0 0 0 0.4 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2851_2391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2851_2391" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2851_2391" x1="10.6022" y1="6.60204" x2="32.7382" y2="33.7876" gradientUnits="userSpaceOnUse">
<stop stop-color="#0B74B0"/>
<stop offset="0.541667" stop-color="#75479C"/>
<stop offset="1" stop-color="#BD3861"/>
</linearGradient>
<clipPath id="clip0_2851_2391">
<rect width="15.7266" height="13.6753" fill="white" transform="matrix(0 -1 1 0 17.0176 27.3496)"/>
</clipPath>
</defs>
</svg>
