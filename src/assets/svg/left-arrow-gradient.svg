<svg width="47" height="46" viewBox="0 0 47 46" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.8">
<g filter="url(#filter0_d_2851_2397)">
<circle cx="19.5" cy="19.5" r="19.5" transform="matrix(0 -1 -1 0 43 39)" fill="url(#paint0_linear_2851_2397)"/>
<circle cx="19.5" cy="19.5" r="19" transform="matrix(0 -1 -1 0 43 39)" stroke="white"/>
</g>
<g clip-path="url(#clip0_2851_2397)">
<path d="M19.036 18.9416L26.108 12.7937C26.4183 12.5245 26.921 12.5245 27.2321 12.7937C27.5424 13.0629 27.5424 13.5001 27.2321 13.7692L20.7211 19.4294L27.2313 25.0895C27.5416 25.3586 27.5416 25.7959 27.2313 26.0657C26.921 26.3349 26.4175 26.3349 26.1072 26.0657L19.0353 19.9178C18.7296 19.6514 18.7296 19.2073 19.036 18.9416Z" fill="white" stroke="white" stroke-width="0.691568"/>
</g>
</g>
<defs>
<filter id="filter0_d_2851_2397" x="0.716989" y="0" width="45.566" height="45.566" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28301"/>
<feGaussianBlur stdDeviation="1.64151"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.713726 0 0 0 0 0.223529 0 0 0 0 0.4 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2851_2397"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2851_2397" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2851_2397" x1="6.60223" y1="6.60204" x2="28.7382" y2="33.7876" gradientUnits="userSpaceOnUse">
<stop stop-color="#0B74B0"/>
<stop offset="0.541667" stop-color="#75479C"/>
<stop offset="1" stop-color="#BD3861"/>
</linearGradient>
<clipPath id="clip0_2851_2397">
<rect width="15.7266" height="13.6753" fill="white" transform="matrix(0 -1 -1 0 29.9824 27.3496)"/>
</clipPath>
</defs>
</svg>
