<svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2835_2442)">
<circle cx="19.5" cy="19.5" r="19.5" transform="matrix(1 0 0 -1 3.5 39.1636)" fill="url(#paint0_linear_2835_2442)"/>
<circle cx="19.5" cy="19.5" r="19" transform="matrix(1 0 0 -1 3.5 39.1636)" stroke="white"/>
</g>
<g clip-path="url(#clip0_2835_2442)">
<path d="M23.5584 15.2006L29.7063 22.2725C29.9755 22.5828 29.9755 23.0856 29.7063 23.3967C29.4371 23.707 28.9999 23.707 28.7308 23.3967L23.0706 16.8857L17.4105 23.3959C17.1414 23.7062 16.7041 23.7062 16.4343 23.3959C16.1651 23.0856 16.1651 22.582 16.4343 22.2717L22.5822 15.1998C22.8486 14.8942 23.2927 14.8942 23.5584 15.2006Z" fill="white" stroke="white" stroke-width="0.691568"/>
</g>
<defs>
<filter id="filter0_d_2835_2442" x="0.216989" y="0.163574" width="45.566" height="45.566" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28301"/>
<feGaussianBlur stdDeviation="1.64151"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.713726 0 0 0 0 0.223529 0 0 0 0 0.4 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2835_2442"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2835_2442" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2835_2442" x1="6.60223" y1="6.60204" x2="28.7382" y2="33.7876" gradientUnits="userSpaceOnUse">
<stop stop-color="#0B74B0"/>
<stop offset="0.541667" stop-color="#75479C"/>
<stop offset="1" stop-color="#BD3861"/>
</linearGradient>
<clipPath id="clip0_2835_2442">
<rect width="15.7266" height="13.6753" fill="white" transform="matrix(1 0 0 -1 15.1504 26.146)"/>
</clipPath>
</defs>
</svg>
