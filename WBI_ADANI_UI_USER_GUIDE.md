# WBI ADANI UI - Comprehensive User Guide

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Requirements](#system-requirements)
3. [Installation and Setup](#installation-and-setup)
4. [Authentication](#authentication)
5. [User Roles and Permissions](#user-roles-and-permissions)
6. [Core Features](#core-features)
7. [Module Documentation](#module-documentation)
8. [API Integration](#api-integration)
9. [Troubleshooting](#troubleshooting)
10. [Development Guidelines](#development-guidelines)

## Project Overview

**WBI (Work-Based Inspection) ADANI UI** is a comprehensive industrial plant management system designed for Adani Group's operations. This Angular-based web application provides a centralized platform for managing equipment inspections, maintenance requests, security operations, and administrative tasks across multiple industrial plants.

### Purpose
The application serves as a digital transformation solution for:
- **Equipment Management**: Track and manage standby equipment across industrial plants
- **Work Summary Reporting**: Monitor daily and weekly equipment scanning activities
- **Maintenance Request (MR) Tracking**: Manage SAP-integrated maintenance workflows
- **Security Operations**: Handle incident reports, SOS calls, lost & found, and advisory management
- **User Administration**: Manage plant administrators and user registrations
- **Quality Assurance**: Bulk upload and manage equipment checkpoint data

### Business Domain
WBI ADANI UI operates in the industrial plant management domain, specifically designed for:
- Cement manufacturing plants
- Multi-plant industrial operations
- Equipment maintenance and inspection workflows
- Security and safety management
- Compliance and audit tracking

## System Requirements

### Frontend Requirements
- **Node.js**: Version 16.x or higher
- **npm**: Version 8.x or higher (or yarn equivalent)
- **Angular CLI**: Version 17.3.8
- **Modern Web Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Development Environment
- **TypeScript**: Version 5.4.2
- **Angular**: Version 17.3.0
- **Bootstrap**: Version 5.3.3
- **Angular Material**: Version 17.3.10

### Backend Integration
- **API Base URL**: `https://bogapi.adani.com/`
- **Authentication**: OTP-based authentication system
- **File Storage**: Cloud-based file upload and management

## Installation and Setup

### 1. Clone the Repository
```bash
git clone https://<EMAIL>/Adani-Cement/WBI_Adani/_git/WBI_ADANI_UI
cd WBI_ADANI_UI
```

### 2. Install Dependencies
```bash
# Using npm
npm install

# Or using yarn
yarn install
```

### 3. Environment Configuration
The application uses environment configuration located in `src/app/enviornments/enviornments.ts`:

```typescript
export const environment = {
  production: false,
  apiUrl: 'https://bogapi.adani.com/'
};
```

### 4. Development Server
```bash
# Start development server
ng serve

# Application will be available at http://localhost:4200/
```

### 5. Build for Production
```bash
# Production build
ng build

# Build artifacts will be stored in dist/ directory
```

## Authentication

### Login Process
1. **Email Entry**: Users enter their registered email address
2. **OTP Generation**: System sends a One-Time Password to the registered email
3. **OTP Verification**: Users enter the 6-digit OTP to authenticate
4. **Role Assignment**: System assigns appropriate role (Super Admin or Plant Admin)

### User Session Management
- **Token Storage**: JWT tokens stored in localStorage
- **Auto-redirect**: Authenticated users automatically redirected to dashboard
- **Session Persistence**: Login state maintained across browser sessions
- **Logout**: Clears all stored authentication data

### Security Features
- **Route Guards**: Protected routes require valid authentication
- **OTP Validation**: Time-limited OTP with attempt restrictions
- **Role-based Access**: Different permissions for Super Admin vs Plant Admin

## User Roles and Permissions

### Super Admin
**Capabilities:**
- Access to all plants across the organization
- Plant selection dropdown for cross-plant operations
- Complete user management (create, edit, delete users)
- System-wide reporting and analytics
- Bulk operations across multiple plants
- Security dashboard with organization-wide visibility

### Plant Admin
**Capabilities:**
- Access limited to assigned plant(s)
- Plant-specific equipment and section management
- Local user registration and management
- Plant-specific reporting and work summaries
- Equipment scanning and maintenance request tracking
- Local security incident management

## Core Features

### Dashboard
The main dashboard provides:
- **Statistics Overview**: Total users, standby equipment, sections
- **Graphical Reports**: Employee registration trends, work summary charts
- **Quick Navigation**: Direct access to all major modules
- **Plant Selection**: (Super Admin only) Switch between different plants

### Equipment Management
- **Standby Equipment Tracking**: Monitor equipment status and availability
- **QR Code Generation**: Generate QR codes for equipment identification
- **Equipment Scanning**: Track daily scanning activities
- **Section-wise Organization**: Manage equipment by plant sections

### Work Summary & Reporting
- **Daily Reports**: Current day scanning vs standby vs remaining equipment
- **Weekly Trends**: 7-day equipment activity analysis
- **Graphical Visualization**: ApexCharts-powered interactive graphs
- **Export Capabilities**: Download reports in Excel format

### Maintenance Request (MR) Management
- **SAP Integration**: Track SAP-generated maintenance requests
- **Status Monitoring**: View MR lifecycle from creation to completion
- **Equipment Correlation**: Link MRs to specific equipment and checkpoints
- **Notification Tracking**: Monitor MR status changes and updates

## Module Documentation

### 1. User Management (`/home/<USER>
**Purpose**: Manage plant administrators and system users

**Features:**
- User registration with role assignment
- Department and designation management
- Contact number validation
- User status management (enable/disable)
- Bulk user operations

**Workflow:**
1. Navigate to Manage User section
2. Click "Register User" to add new users
3. Fill required information (name, email, contact, department, designation)
4. Assign plant access and role permissions
5. System validates and creates user account

### 2. Section Management (`/home/<USER>
**Purpose**: Organize plant operations by sections and manage equipment assignments

**Features:**
- Section creation and management
- Equipment assignment to sections
- Section enable/disable functionality
- Equipment listing by section
- Bulk section operations

**Workflow:**
1. View list of plant sections in left panel
2. Select section to view assigned equipment
3. Use checkboxes to assign/unassign equipment
4. Enable or disable sections as needed
5. Changes are automatically saved

### 3. Standby Equipment (`/home/<USER>
**Purpose**: Monitor and manage equipment in standby status

**Features:**
- Real-time standby equipment listing
- Equipment status filtering
- Excel export functionality
- Equipment details view
- Plant-specific filtering

**Data Fields:**
- Equipment Name and Number
- Functional Location
- Section Assignment
- Standby Status
- Last Updated Timestamp

### 4. MR Status (`/home/<USER>
**Purpose**: Track maintenance requests and their lifecycle

**Features:**
- Collapsible MR cards with detailed information
- Status tracking (CREATED, PENDING, COMPLETED)
- SAP integration for MR numbers
- Equipment correlation
- Date-based filtering

**MR Information Includes:**
- Functional Location
- Checklist and Checkpoint Names
- SAP MR Number
- Equipment Details
- Creation Date and Creator
- Current Status

### 5. Work Summary (`/home/<USER>
**Purpose**: Visualize equipment scanning and maintenance activities

**Components:**
- **Weekly Report Graph**: 7-day trend analysis
- **Current Day Report**: Today's scanning statistics

**Metrics Tracked:**
- Scanned Equipment Count
- Standby Equipment Count
- Total Equipment Count
- Remaining Equipment (not scanned)

### 6. Security Dashboard (`/home/<USER>
**Purpose**: Centralized security operations management

**Sub-modules:**
- **Security Management**: Plant registration and site details
- **Advisory Management**: Upload and manage safety advisories
- **Incident Reports**: Record and track security incidents
- **Lost & Found**: Manage lost and found items
- **SOS Call History**: Track emergency call records
- **Feedback Management**: Handle user feedback and suggestions

### 7. Bulk Upload (`/home/<USER>
**Purpose**: Mass upload equipment checkpoint data via Excel files

**Features:**
- Excel template download
- File validation (naming convention: `wbi-checkpoint-temple-{PlantName}.xlsx`)
- Upload progress tracking
- Backup and rollback functionality
- Upload history management

**Process:**
1. Download Excel template for specific plant
2. Fill equipment checkpoint data
3. Upload file (system validates format and plant name)
4. Review upload results
5. Backup/rollback options available

### 8. User Registry (`/home/<USER>
**Purpose**: Register new users in the system

**Features:**
- User information collection
- Department and designation assignment
- Plant access provisioning
- Contact validation
- Role assignment

### 9. Provision Section (`/home/<USER>
**Purpose**: Assign sections to users for access control

**Features:**
- User-section mapping
- Bulk section assignment
- Access permission management
- Section-based filtering

## API Integration

### Base Configuration
- **API Base URL**: `https://bogapi.adani.com/`
- **Authentication**: Bearer token-based
- **Request Format**: JSON
- **Response Format**: JSON with standardized structure

### Key Endpoints

#### Authentication
- `POST /admins/wbisuperadmin-otp` - Send OTP
- `POST /admins/verify-otp` - Verify OTP
- `POST /admins/logout` - Logout

#### User Management
- `GET /admins` - Get users list
- `POST /admins/wbiuser-register` - Register new user
- `POST /update` - Update user information

#### Equipment Management
- `GET /wbi-standby` - Get standby equipment
- `GET /wbi-equipment` - Get equipment by section
- `POST /wbi-equipment/bulkuploadxl` - Bulk upload equipment data

#### Reporting
- `POST /admins/dayWise-currentweek-graph` - Weekly report data
- `POST /admins/currentdayreport-graph` - Current day report data

#### Security Module
- `GET /sos` - Get security data
- `POST /sos` - Register new plant/incident
- Various endpoints for advisory, incident, and lost & found management

### Error Handling
The application implements comprehensive error handling:
- **Network Errors**: Automatic retry mechanisms
- **Authentication Errors**: Redirect to login
- **Validation Errors**: User-friendly error messages
- **Server Errors**: Graceful degradation with error notifications

## Troubleshooting

### Common Issues

#### 1. Login Problems
**Issue**: Cannot receive OTP
**Solutions:**
- Verify email address is correctly entered
- Check spam/junk folder
- Ensure email is registered in the system
- Contact system administrator for account verification

#### 2. Plant Selection Issues (Super Admin)
**Issue**: Plant dropdown not loading
**Solutions:**
- Refresh the page
- Clear browser cache
- Verify network connectivity
- Check if user has proper Super Admin permissions

#### 3. File Upload Failures
**Issue**: Excel upload fails
**Solutions:**
- Verify file naming convention: `wbi-checkpoint-temple-{PlantName}.xlsx`
- Check file format (must be .xlsx)
- Ensure file size is within limits
- Validate data format matches template

#### 4. Performance Issues
**Issue**: Slow loading or timeouts
**Solutions:**
- Check network connectivity
- Clear browser cache and cookies
- Disable browser extensions
- Try different browser
- Contact IT support for server status

### Browser Compatibility
- **Recommended**: Chrome 90+, Firefox 88+
- **Supported**: Safari 14+, Edge 90+
- **Not Supported**: Internet Explorer

### Network Requirements
- **Stable Internet Connection**: Required for real-time data
- **Firewall Settings**: Ensure access to `https://bogapi.adani.com/`
- **CORS**: Application handles cross-origin requests

## Development Guidelines

### Code Structure
```
src/
├── app/
│   ├── common/           # Shared components
│   ├── core/            # Core utilities and endpoints
│   ├── models/          # TypeScript interfaces
│   ├── pages/           # Feature modules
│   ├── services/        # API services
│   └── shared/          # Shared utilities
├── assets/              # Static assets
└── environments/        # Environment configurations
```

### Key Technologies
- **Frontend Framework**: Angular 17.3.0
- **UI Components**: Angular Material + Bootstrap 5.3.3
- **Charts**: ApexCharts via ng-apexcharts
- **State Management**: NgRx Store
- **HTTP Client**: Angular HttpClient with Axios integration
- **File Handling**: xlsx library for Excel operations
- **PDF Generation**: jsPDF and html2canvas

### Development Commands
```bash
# Start development server
ng serve

# Run tests
ng test

# Build for production
ng build --configuration production

# Generate new component
ng generate component component-name

# Generate new service
ng generate service service-name
```

### Deployment
The application is configured for Azure Static Web Apps deployment with:
- **Build Configuration**: Angular CLI build process
- **Routing**: SPA routing with fallback to index.html
- **Security Headers**: Configured in staticwebapp.config.json
- **Environment Variables**: Managed through Azure portal

---

**Note**: This user guide covers the core functionality of WBI ADANI UI. For specific technical implementation details or advanced configuration, please refer to the source code documentation or contact the development team.
