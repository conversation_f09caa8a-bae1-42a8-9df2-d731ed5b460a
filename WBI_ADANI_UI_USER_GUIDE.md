# WBI ADANI UI - Comprehensive User Guide

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Requirements](#system-requirements)
3. [Installation and Setup](#installation-and-setup)
4. [Authentication](#authentication)
5. [User Roles and Permissions](#user-roles-and-permissions)
6. [Core Features](#core-features)
7. [Module Documentation](#module-documentation)
8. [API Integration](#api-integration)
9. [Troubleshooting](#troubleshooting)
10. [Development Guidelines](#development-guidelines)

## Project Overview

**WBI (Work-Based Inspection) ADANI UI** is a comprehensive industrial plant management system designed for Adani Group's operations. This Angular-based web application provides a centralized platform for managing equipment inspections, maintenance requests, security operations, and administrative tasks across multiple industrial plants.

### Purpose
The application serves as a digital transformation solution for:
- **Equipment Management**: Track and manage standby equipment across industrial plants
- **Work Summary Reporting**: Monitor daily and weekly equipment scanning activities
- **Maintenance Request (MR) Tracking**: Manage SAP-integrated maintenance workflows
- **Security Operations**: Handle incident reports, SOS calls, lost & found, and advisory management
- **User Administration**: Manage plant administrators and user registrations
- **Quality Assurance**: Bulk upload and manage equipment checkpoint data

### Business Domain
WBI ADANI UI operates in the industrial plant management domain, specifically designed for:
- Cement manufacturing plants
- Multi-plant industrial operations
- Equipment maintenance and inspection workflows
- Security and safety management
- Compliance and audit tracking

## System Requirements

### Frontend Requirements
- **Node.js**: Version 16.x or higher
- **npm**: Version 8.x or higher (or yarn equivalent)
- **Angular CLI**: Version 17.3.8
- **Modern Web Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Development Environment
- **TypeScript**: Version 5.4.2
- **Angular**: Version 17.3.0
- **Bootstrap**: Version 5.3.3
- **Angular Material**: Version 17.3.10

### Backend Integration
- **API Base URL**: `https://bogapi.adani.com/`
- **Authentication**: OTP-based authentication system
- **File Storage**: Cloud-based file upload and management

## Installation and Setup

### 1. Clone the Repository
```bash
git clone https://<EMAIL>/Adani-Cement/WBI_Adani/_git/WBI_ADANI_UI
cd WBI_ADANI_UI
```

### 2. Install Dependencies
```bash
# Using npm
npm install

# Or using yarn
yarn install
```

### 3. Environment Configuration
The application uses environment configuration located in `src/app/enviornments/enviornments.ts`:

```typescript
export const environment = {
  production: false,
  apiUrl: 'https://bogapi.adani.com/'
};
```

### 4. Development Server
```bash
# Start development server
ng serve

# Application will be available at http://localhost:4200/
```

### 5. Build for Production
```bash
# Production build
ng build

# Build artifacts will be stored in dist/ directory
```

## Authentication

### Login Process Flow

```mermaid
graph TD
    A[User Opens Application] --> B[Login Page]
    B --> C[Enter Email Address]
    C --> D[Click 'Send OTP']
    D --> E[System Validates Email]
    E --> F{Email Registered?}
    F -->|No| G[Error: Email not found]
    F -->|Yes| H[Generate & Send OTP]
    H --> I[User Receives OTP Email]
    I --> J[Enter 6-Digit OTP]
    J --> K[Click 'Verify OTP']
    K --> L[System Validates OTP]
    L --> M{OTP Valid?}
    M -->|No| N[Error: Invalid OTP]
    M -->|Yes| O[Authenticate User]
    O --> P[Determine User Role]
    P --> Q{wbiRoleId = 1?}
    Q -->|Yes| R[Set Role: Super Admin]
    Q -->|No| S[Set Role: Plant Admin]
    R --> T[Store Authentication Data]
    S --> T
    T --> U[Redirect to Dashboard]

    G --> C
    N --> J
```

### Detailed Authentication Steps

#### 1. **Email Entry Phase**
- User navigates to application root (`/`)
- System displays login component
- User enters registered email address
- Email validation occurs on frontend

#### 2. **OTP Generation Phase**
- System calls `POST /admins/wbisuperadmin-otp` endpoint
- Backend validates email exists in system
- If valid, generates time-limited OTP
- OTP sent to user's registered email address
- Frontend displays OTP input form

#### 3. **OTP Verification Phase**
- User enters 6-digit OTP received via email
- System calls `POST /admins/verify-otp` endpoint
- Backend validates OTP against stored value
- Checks OTP expiration time and attempt count

#### 4. **Role Determination Process**
```typescript
// Role assignment logic from auth.service.ts
if (response['user'].wbiRoleId == 1) {
  role = 'superadmin'
} else {
  role = 'plantadmin'
}
```

#### 5. **Session Establishment**
Upon successful authentication, system stores:
```typescript
localStorage.setItem('token', response['accessToken']);
localStorage.setItem('user', JSON.stringify(response['user']));
localStorage.setItem('userRole', role);
```

### User Session Management

#### **Token Storage Structure**
```json
{
  "token": "JWT_ACCESS_TOKEN",
  "user": {
    "id": 12345,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "wbiRoleId": 1,
    "plantIds": [1, 2, 3],
    "plant": [
      {
        "id": 1,
        "name": "Plant-A",
        "location": "Gujarat"
      }
    ],
    "designationId": 5,
    "departmentId": 3
  },
  "userRole": "superadmin"
}
```

#### **Session Persistence**
- **Auto-login**: If valid token exists, user automatically redirected to `/home`
- **Route Protection**: All routes under `/home` protected by `authGuard`
- **Token Validation**: System validates token on each protected route access
- **Session Timeout**: Handled by backend token expiration

#### **Logout Process**
1. User clicks logout button
2. System calls `POST /admins/logout` endpoint
3. Clears all localStorage data:
   - `token`
   - `user`
   - `userRole`
   - Any cached plant selection data
4. Redirects user to login page (`/`)

### Security Features

#### **Route Guards Implementation**
```typescript
export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const token = localStorage.getItem('token');

  if (!token) {
    router.navigate(['/']);  // Redirect to login
    sessionStorage.removeItem('activeRoute')
    return false;
  }
  return true;  // Allow access
};
```

#### **OTP Security Measures**
- **Time Limitation**: OTP expires after specified time period
- **Attempt Restrictions**: Limited number of OTP verification attempts
- **Rate Limiting**: Prevents spam OTP requests
- **Single Use**: Each OTP can only be used once

#### **Role-based Access Control**
- **Frontend Validation**: UI elements shown/hidden based on role
- **Backend Validation**: API endpoints validate user permissions
- **Plant Restriction**: Plant Admin access limited to assigned plants
- **Feature Gating**: Certain features only available to Super Admin

### Authentication Error Handling

#### **Common Error Scenarios**

1. **Email Not Found**
   - Error Message: "Email not registered in system"
   - Action: User must contact administrator for account creation

2. **Invalid OTP**
   - Error Message: "Invalid OTP entered"
   - Action: User can retry with correct OTP

3. **OTP Expired**
   - Error Message: "OTP has expired"
   - Action: User must request new OTP

4. **Too Many Attempts**
   - Error Message: "Too many failed attempts"
   - Action: Account temporarily locked, contact administrator

5. **Network Issues**
   - Error Message: "Connection failed"
   - Action: Check network connectivity and retry

### Role-Based Dashboard Redirection

#### **Post-Login Navigation**
```typescript
// After successful authentication
if (userRole === 'superadmin') {
  // Super Admin sees plant selector
  // Can access organization-wide features
  router.navigate(['/home/<USER>']);
} else {
  // Plant Admin sees plant-specific dashboard
  // Limited to assigned plant operations
  router.navigate(['/home/<USER>']);
}
```

#### **Plant Selection for Super Admin**
- Plant selector dropdown appears in header
- Selection stored in service: `AdminPlantSelectionService`
- All subsequent operations filtered by selected plant
- Can switch plants without re-authentication

## User Roles and Permissions

### Super Admin (wbiRoleId = 1)

#### **Access Level**: Organization-wide across all plants

#### **Core Capabilities:**
- **Multi-Plant Access**: Can switch between any plant in the organization via plant selection dropdown
- **Global User Management**: Create, edit, delete, and manage users across all plants
- **System Administration**: Full administrative control over the entire WBI system
- **Cross-Plant Analytics**: View aggregated reports and analytics across multiple plants
- **Bulk Operations**: Perform bulk uploads and operations across different plants
- **Security Oversight**: Organization-wide security dashboard and incident management

#### **Super Admin Workflow:**

```mermaid
graph TD
    A[Super Admin Login] --> B[Dashboard with Plant Selector]
    B --> C{Select Plant}
    C --> D[Plant-Specific Operations]
    C --> E[Cross-Plant Analytics]

    D --> F[Equipment Management]
    D --> G[User Management]
    D --> H[Section Management]
    D --> I[Work Summary]
    D --> J[MR Status]
    D --> K[Security Operations]

    E --> L[Organization Reports]
    E --> M[Multi-Plant Comparisons]
    E --> N[Global User Analytics]

    F --> O[View/Manage Standby Equipment]
    G --> P[Register Users for Any Plant]
    G --> Q[Assign Plant Access]
    H --> R[Manage Sections Across Plants]
    I --> S[Weekly/Daily Reports by Plant]
    J --> T[Track MRs Across Plants]
    K --> U[Security Dashboard - All Plants]
```

#### **What Super Admin CAN Do:**
✅ **Plant Operations:**
- Switch between any plant using the plant selector dropdown
- Access all modules for any selected plant
- View plant-specific dashboards and statistics
- Manage equipment, sections, and users for any plant

✅ **User Management:**
- Register new users for any plant
- Assign multiple plants to a single user
- Modify user roles and permissions
- Enable/disable user accounts across the organization
- View user registration graphs and analytics

✅ **Equipment & Maintenance:**
- View and manage standby equipment across all plants
- Access work summary reports for any plant
- Track maintenance requests (MRs) organization-wide
- Perform bulk equipment data uploads for any plant
- Generate QR codes for equipment across plants

✅ **Security Operations:**
- Access security dashboard with organization-wide visibility
- Manage incidents, advisories, and SOS calls for all plants
- Handle lost & found items across the organization
- Upload and manage advisory files for multiple plants

✅ **Reporting & Analytics:**
- Generate cross-plant comparison reports
- View organization-wide statistics and trends
- Export data and reports for any plant
- Access historical data across all plants

#### **What Super Admin CANNOT Do:**
❌ **System Limitations:**
- Cannot modify system-level configurations without backend access
- Cannot directly integrate with SAP (read-only MR status)
- Cannot modify API endpoints or system architecture
- Cannot access other users' personal authentication details

### Plant Admin (wbiRoleId ≠ 1)

#### **Access Level**: Limited to assigned plant(s) only

#### **Core Capabilities:**
- **Single/Limited Plant Access**: Operations restricted to assigned plant(s)
- **Local User Management**: Register and manage users within assigned plant
- **Plant-Specific Operations**: Full control over assigned plant operations
- **Local Reporting**: Access to plant-specific reports and analytics
- **Equipment Management**: Manage equipment and sections within assigned plant
- **Local Security**: Handle security incidents and operations for assigned plant

#### **Plant Admin Workflow:**

```mermaid
graph TD
    A[Plant Admin Login] --> B[Plant-Specific Dashboard]
    B --> C[Assigned Plant Operations Only]

    C --> D[Equipment Management]
    C --> E[Local User Management]
    C --> F[Section Management]
    C --> G[Work Summary]
    C --> H[MR Status]
    C --> I[Security Operations]

    D --> J[View Standby Equipment - Own Plant]
    E --> K[Register Users - Own Plant Only]
    E --> L[Manage Local Users]
    F --> M[Manage Plant Sections]
    G --> N[Plant-Specific Reports]
    H --> O[Track Plant MRs]
    I --> P[Local Security Dashboard]

    J --> Q[Equipment Scanning Activities]
    K --> R[User Registration with Plant Assignment]
    M --> S[Section-Equipment Assignment]
    N --> T[Daily/Weekly Work Reports]
    O --> U[Maintenance Request Tracking]
    P --> V[Incident/Advisory Management]
```

#### **What Plant Admin CAN Do:**
✅ **Plant-Specific Operations:**
- Access dashboard for assigned plant(s) only
- View plant-specific statistics and metrics
- Manage all operations within assigned plant boundaries
- Access all modules but with plant-restricted data

✅ **Local User Management:**
- Register new users for their assigned plant
- Manage user accounts within their plant
- Assign sections and permissions to local users
- View user registration analytics for their plant

✅ **Equipment & Maintenance:**
- View and manage standby equipment in assigned plant
- Access work summary reports for their plant
- Track maintenance requests (MRs) for their plant equipment
- Perform bulk equipment uploads for their plant
- Generate QR codes for their plant equipment

✅ **Section Management:**
- Create and manage sections within their plant
- Assign equipment to sections in their plant
- Enable/disable sections as needed
- Manage section-user assignments

✅ **Security Operations:**
- Access security dashboard for their plant
- Manage incidents and advisories for their plant
- Handle SOS calls and lost & found for their plant
- Upload advisory files for their plant

✅ **Reporting:**
- Generate reports specific to their plant
- Export data for their assigned plant
- View historical data for their plant
- Access work summary and equipment analytics

#### **What Plant Admin CANNOT Do:**
❌ **Cross-Plant Restrictions:**
- Cannot access other plants' data or operations
- Cannot switch between plants (no plant selector dropdown)
- Cannot view organization-wide analytics or reports
- Cannot manage users from other plants

❌ **System Limitations:**
- Cannot create or assign users to other plants
- Cannot view cross-plant comparison reports
- Cannot access global security dashboard
- Cannot perform organization-wide bulk operations
- Cannot modify system-level settings or configurations

### Role-Based UI Differences

#### **Super Admin Interface:**
- **Plant Selector Dropdown**: Visible in header/navigation
- **Global Navigation**: Access to organization-wide features
- **Extended User Management**: Options to assign multiple plants
- **Cross-Plant Reports**: Additional reporting options
- **Organization Dashboard**: System-wide statistics and metrics

#### **Plant Admin Interface:**
- **No Plant Selector**: Fixed to assigned plant(s)
- **Plant-Specific Navigation**: Limited to local operations
- **Local User Management**: Restricted user creation options
- **Plant Reports Only**: Limited to assigned plant data
- **Plant Dashboard**: Plant-specific statistics only

### Permission Matrix

| Feature | Super Admin | Plant Admin |
|---------|-------------|-------------|
| **Dashboard Access** | All Plants | Assigned Plant Only |
| **User Registration** | Any Plant | Own Plant Only |
| **User Management** | Organization-wide | Local Plant Only |
| **Equipment Management** | All Plants | Own Plant Only |
| **Section Management** | All Plants | Own Plant Only |
| **Work Summary Reports** | All Plants | Own Plant Only |
| **MR Status Tracking** | All Plants | Own Plant Only |
| **Security Dashboard** | Organization-wide | Local Plant Only |
| **Bulk Upload** | Any Plant | Own Plant Only |
| **QR Code Generation** | All Plants | Own Plant Only |
| **Advisory Management** | All Plants | Own Plant Only |
| **Incident Reporting** | All Plants | Own Plant Only |
| **Plant Selection** | ✅ Available | ❌ Not Available |
| **Cross-Plant Analytics** | ✅ Available | ❌ Not Available |
| **Global User View** | ✅ Available | ❌ Not Available |

## Detailed Operational Workflows

### Super Admin Daily Operations

#### **Morning Routine Workflow**
```mermaid
graph TD
    A[Super Admin Login] --> B[View Organization Dashboard]
    B --> C[Check Overall Statistics]
    C --> D[Review Plant Performance]
    D --> E{Issues Detected?}
    E -->|Yes| F[Select Problematic Plant]
    E -->|No| G[Continue Monitoring]
    F --> H[Investigate Plant Issues]
    H --> I[Take Corrective Actions]
    I --> J[Document Actions]
    G --> K[Review Cross-Plant Reports]
    K --> L[Plan Daily Activities]
```

#### **User Management Operations**
1. **Creating New Plant Admin**
   ```
   Navigate to: /home/<USER>
   → Click "Register User"
   → Fill user details (Name, Email, Contact)
   → Select Department & Designation
   → Choose Plant Assignment (can select multiple)
   → Set Role as Plant Admin
   → Submit Registration
   → System sends OTP to new user
   ```

2. **Cross-Plant User Transfer**
   ```
   Navigate to: /home/<USER>
   → Search for existing user
   → Edit user profile
   → Modify plant assignments
   → Add/Remove plant access
   → Update role if necessary
   → Save changes
   ```

#### **Equipment Management Across Plants**
1. **Multi-Plant Equipment Review**
   ```
   Dashboard → Select Plant A
   → Review standby equipment status
   → Note any issues
   → Switch to Plant B (via plant selector)
   → Compare equipment performance
   → Generate comparative reports
   → Take organization-wide decisions
   ```

2. **Bulk Operations Management**
   ```
   Navigate to: /home/<USER>
   → Select target plant
   → Download plant-specific template
   → Upload equipment data
   → Monitor upload progress
   → Switch to another plant
   → Repeat process
   → Generate consolidated reports
   ```

### Plant Admin Daily Operations

#### **Daily Plant Management Workflow**
```mermaid
graph TD
    A[Plant Admin Login] --> B[View Plant Dashboard]
    B --> C[Check Plant Statistics]
    C --> D[Review Equipment Status]
    D --> E[Check Work Summary]
    E --> F[Review MR Status]
    F --> G{Issues Found?}
    G -->|Yes| H[Investigate Issues]
    G -->|No| I[Continue Monitoring]
    H --> J[Take Local Actions]
    J --> K[Update Status]
    K --> L[Report to Management]
    I --> M[Plan Daily Activities]
    M --> N[Manage Local Team]
```

#### **Equipment Scanning Management**
1. **Daily Equipment Review**
   ```
   Navigate to: /home/<USER>
   → Review standby equipment list
   → Check scanning status
   → Identify unscanned equipment
   → Coordinate with field teams
   → Monitor scanning progress
   → Update equipment status
   ```

2. **Work Summary Analysis**
   ```
   Navigate to: /home/<USER>
   → Review current day report
   → Analyze scanning vs standby ratios
   → Check weekly trends
   → Identify improvement areas
   → Plan corrective actions
   → Generate reports for management
   ```

#### **Local User Management**
1. **Register Plant Personnel**
   ```
   Navigate to: /home/<USER>
   → Fill user information
   → Assign to current plant only
   → Set appropriate permissions
   → Assign relevant sections
   → Submit registration
   → Follow up on account activation
   ```

2. **Section Assignment**
   ```
   Navigate to: /home/<USER>
   → Select user from plant roster
   → Choose relevant sections
   → Assign access permissions
   → Save assignments
   → Verify user can access sections
   ```

### Operational Restrictions and Boundaries

#### **Super Admin Operational Boundaries**
✅ **Can Perform:**
- Switch between any plant in real-time
- Create users for any plant in the organization
- View aggregated data across all plants
- Perform bulk operations on multiple plants
- Access organization-wide security dashboard
- Generate cross-plant comparison reports
- Manage plant transfers and assignments

❌ **Cannot Perform:**
- Directly modify SAP system data
- Change system-level configurations
- Access other users' authentication credentials
- Modify API endpoints or backend logic
- Override system security protocols

#### **Plant Admin Operational Boundaries**
✅ **Can Perform:**
- Full control over assigned plant operations
- Register and manage local plant users
- Manage all equipment within assigned plant
- Generate plant-specific reports
- Handle local security incidents
- Coordinate with local teams and departments
- Upload and manage plant-specific data

❌ **Cannot Perform:**
- Access other plants' data or operations
- View organization-wide analytics
- Create users for other plants
- Switch plant context (no plant selector)
- View cross-plant comparison data
- Access global security dashboard
- Perform organization-wide bulk operations

### Data Access Patterns

#### **Super Admin Data Access**
```typescript
// Super Admin can access any plant data
const plantId = this.selectedplantId ? this.selectedplantId : currentUser.plantIds[0];

// Plant selection service manages current context
this.adminPlantSelection.selectedPlant$.subscribe(adminPlantId => {
  this.selectedplantId = adminPlantId;
  this.loadPlantSpecificData();
});
```

#### **Plant Admin Data Access**
```typescript
// Plant Admin restricted to assigned plants only
const currentUser = JSON.parse(localStorage.getItem('user') ?? '{}');
const plantId = currentUser.plantIds[0]; // First assigned plant

// No plant selection - fixed to assigned plant
this.loadPlantSpecificData(plantId);
```

### Error Handling and Access Control

#### **Role-Based Error Scenarios**

1. **Super Admin Accessing Invalid Plant**
   ```
   Error: "Plant not found or access denied"
   Resolution: Verify plant exists and user has organization access
   ```

2. **Plant Admin Attempting Cross-Plant Access**
   ```
   Error: "Access denied - insufficient permissions"
   Resolution: Contact Super Admin for additional plant access
   ```

3. **Unauthorized Feature Access**
   ```
   Error: "Feature not available for your role"
   Resolution: Verify role permissions or contact administrator
   ```

### Best Practices for Each Role

#### **Super Admin Best Practices**
1. **Regular Plant Rotation**: Systematically review each plant's performance
2. **Delegation**: Assign capable Plant Admins and provide necessary training
3. **Monitoring**: Set up alerts for cross-plant issues and anomalies
4. **Documentation**: Maintain records of organization-wide decisions
5. **Communication**: Regular coordination with Plant Admins

#### **Plant Admin Best Practices**
1. **Daily Monitoring**: Regular review of plant-specific metrics
2. **Team Coordination**: Effective communication with local teams
3. **Proactive Management**: Identify and address issues before escalation
4. **Data Quality**: Ensure accurate and timely data entry
5. **Escalation**: Promptly report significant issues to Super Admin

## Core Features

### Dashboard
The main dashboard provides:
- **Statistics Overview**: Total users, standby equipment, sections
- **Graphical Reports**: Employee registration trends, work summary charts
- **Quick Navigation**: Direct access to all major modules
- **Plant Selection**: (Super Admin only) Switch between different plants

### Equipment Management
- **Standby Equipment Tracking**: Monitor equipment status and availability
- **QR Code Generation**: Generate QR codes for equipment identification
- **Equipment Scanning**: Track daily scanning activities
- **Section-wise Organization**: Manage equipment by plant sections

### Work Summary & Reporting
- **Daily Reports**: Current day scanning vs standby vs remaining equipment
- **Weekly Trends**: 7-day equipment activity analysis
- **Graphical Visualization**: ApexCharts-powered interactive graphs
- **Export Capabilities**: Download reports in Excel format

### Maintenance Request (MR) Management
- **SAP Integration**: Track SAP-generated maintenance requests
- **Status Monitoring**: View MR lifecycle from creation to completion
- **Equipment Correlation**: Link MRs to specific equipment and checkpoints
- **Notification Tracking**: Monitor MR status changes and updates

## Module Documentation

### 1. User Management (`/home/<USER>
**Purpose**: Manage plant administrators and system users

**Features:**
- User registration with role assignment
- Department and designation management
- Contact number validation
- User status management (enable/disable)
- Bulk user operations

**Workflow:**
1. Navigate to Manage User section
2. Click "Register User" to add new users
3. Fill required information (name, email, contact, department, designation)
4. Assign plant access and role permissions
5. System validates and creates user account

### 2. Section Management (`/home/<USER>
**Purpose**: Organize plant operations by sections and manage equipment assignments

**Features:**
- Section creation and management
- Equipment assignment to sections
- Section enable/disable functionality
- Equipment listing by section
- Bulk section operations

**Workflow:**
1. View list of plant sections in left panel
2. Select section to view assigned equipment
3. Use checkboxes to assign/unassign equipment
4. Enable or disable sections as needed
5. Changes are automatically saved

### 3. Standby Equipment (`/home/<USER>
**Purpose**: Monitor and manage equipment in standby status

**Features:**
- Real-time standby equipment listing
- Equipment status filtering
- Excel export functionality
- Equipment details view
- Plant-specific filtering

**Data Fields:**
- Equipment Name and Number
- Functional Location
- Section Assignment
- Standby Status
- Last Updated Timestamp

### 4. MR Status (`/home/<USER>
**Purpose**: Track maintenance requests and their lifecycle

**Features:**
- Collapsible MR cards with detailed information
- Status tracking (CREATED, PENDING, COMPLETED)
- SAP integration for MR numbers
- Equipment correlation
- Date-based filtering

**MR Information Includes:**
- Functional Location
- Checklist and Checkpoint Names
- SAP MR Number
- Equipment Details
- Creation Date and Creator
- Current Status

### 5. Work Summary (`/home/<USER>
**Purpose**: Visualize equipment scanning and maintenance activities

**Components:**
- **Weekly Report Graph**: 7-day trend analysis
- **Current Day Report**: Today's scanning statistics

**Metrics Tracked:**
- Scanned Equipment Count
- Standby Equipment Count
- Total Equipment Count
- Remaining Equipment (not scanned)

### 6. Security Dashboard (`/home/<USER>
**Purpose**: Centralized security operations management

**Sub-modules:**
- **Security Management**: Plant registration and site details
- **Advisory Management**: Upload and manage safety advisories
- **Incident Reports**: Record and track security incidents
- **Lost & Found**: Manage lost and found items
- **SOS Call History**: Track emergency call records
- **Feedback Management**: Handle user feedback and suggestions

### 7. Bulk Upload (`/home/<USER>
**Purpose**: Mass upload equipment checkpoint data via Excel files

**Features:**
- Excel template download
- File validation (naming convention: `wbi-checkpoint-temple-{PlantName}.xlsx`)
- Upload progress tracking
- Backup and rollback functionality
- Upload history management

**Process:**
1. Download Excel template for specific plant
2. Fill equipment checkpoint data
3. Upload file (system validates format and plant name)
4. Review upload results
5. Backup/rollback options available

### 8. User Registry (`/home/<USER>
**Purpose**: Register new users in the system

**Features:**
- User information collection
- Department and designation assignment
- Plant access provisioning
- Contact validation
- Role assignment

### 9. Provision Section (`/home/<USER>
**Purpose**: Assign sections to users for access control

**Features:**
- User-section mapping
- Bulk section assignment
- Access permission management
- Section-based filtering

## API Integration

### Base Configuration
- **API Base URL**: `https://bogapi.adani.com/`
- **Authentication**: Bearer token-based
- **Request Format**: JSON
- **Response Format**: JSON with standardized structure

### Key Endpoints

#### Authentication
- `POST /admins/wbisuperadmin-otp` - Send OTP
- `POST /admins/verify-otp` - Verify OTP
- `POST /admins/logout` - Logout

#### User Management
- `GET /admins` - Get users list
- `POST /admins/wbiuser-register` - Register new user
- `POST /update` - Update user information

#### Equipment Management
- `GET /wbi-standby` - Get standby equipment
- `GET /wbi-equipment` - Get equipment by section
- `POST /wbi-equipment/bulkuploadxl` - Bulk upload equipment data

#### Reporting
- `POST /admins/dayWise-currentweek-graph` - Weekly report data
- `POST /admins/currentdayreport-graph` - Current day report data

#### Security Module
- `GET /sos` - Get security data
- `POST /sos` - Register new plant/incident
- Various endpoints for advisory, incident, and lost & found management

### Error Handling
The application implements comprehensive error handling:
- **Network Errors**: Automatic retry mechanisms
- **Authentication Errors**: Redirect to login
- **Validation Errors**: User-friendly error messages
- **Server Errors**: Graceful degradation with error notifications

## Troubleshooting

### Common Issues

#### 1. Login Problems
**Issue**: Cannot receive OTP
**Solutions:**
- Verify email address is correctly entered
- Check spam/junk folder
- Ensure email is registered in the system
- Contact system administrator for account verification

#### 2. Plant Selection Issues (Super Admin)
**Issue**: Plant dropdown not loading
**Solutions:**
- Refresh the page
- Clear browser cache
- Verify network connectivity
- Check if user has proper Super Admin permissions

#### 3. File Upload Failures
**Issue**: Excel upload fails
**Solutions:**
- Verify file naming convention: `wbi-checkpoint-temple-{PlantName}.xlsx`
- Check file format (must be .xlsx)
- Ensure file size is within limits
- Validate data format matches template

#### 4. Performance Issues
**Issue**: Slow loading or timeouts
**Solutions:**
- Check network connectivity
- Clear browser cache and cookies
- Disable browser extensions
- Try different browser
- Contact IT support for server status

### Browser Compatibility
- **Recommended**: Chrome 90+, Firefox 88+
- **Supported**: Safari 14+, Edge 90+
- **Not Supported**: Internet Explorer

### Network Requirements
- **Stable Internet Connection**: Required for real-time data
- **Firewall Settings**: Ensure access to `https://bogapi.adani.com/`
- **CORS**: Application handles cross-origin requests

## Development Guidelines

### Code Structure
```
src/
├── app/
│   ├── common/           # Shared components
│   ├── core/            # Core utilities and endpoints
│   ├── models/          # TypeScript interfaces
│   ├── pages/           # Feature modules
│   ├── services/        # API services
│   └── shared/          # Shared utilities
├── assets/              # Static assets
└── environments/        # Environment configurations
```

### Key Technologies
- **Frontend Framework**: Angular 17.3.0
- **UI Components**: Angular Material + Bootstrap 5.3.3
- **Charts**: ApexCharts via ng-apexcharts
- **State Management**: NgRx Store
- **HTTP Client**: Angular HttpClient with Axios integration
- **File Handling**: xlsx library for Excel operations
- **PDF Generation**: jsPDF and html2canvas

### Development Commands
```bash
# Start development server
ng serve

# Run tests
ng test

# Build for production
ng build --configuration production

# Generate new component
ng generate component component-name

# Generate new service
ng generate service service-name
```

### Deployment
The application is configured for Azure Static Web Apps deployment with:
- **Build Configuration**: Angular CLI build process
- **Routing**: SPA routing with fallback to index.html
- **Security Headers**: Configured in staticwebapp.config.json
- **Environment Variables**: Managed through Azure portal

---

**Note**: This user guide covers the core functionality of WBI ADANI UI. For specific technical implementation details or advanced configuration, please refer to the source code documentation or contact the development team.
