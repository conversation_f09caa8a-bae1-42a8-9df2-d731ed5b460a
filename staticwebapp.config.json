{"routes": [{"route": "/", "serve": "/index.html"}, {"route": "/login", "serve": "/index.html"}, {"route": "/home", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/home/<USER>", "serve": "/index.html"}, {"route": "/saml/login", "serve": "/index.html"}, {"route": "/auth-redirect", "serve": "/index.html"}, {"route": "/404", "serve": "/index.html"}, {"route": "/401", "serve": "/index.html"}, {"route": "/redirect/:path*", "serve": "/index.html"}, {"route": "/forgotPassword", "serve": "/index.html"}, {"route": "/masters", "serve": "/index.html"}, {"route": "/reportsmasters", "serve": "/index.html"}, {"route": "/reportsmastersObservation", "serve": "/index.html"}], "navigationFallback": {"rewrite": "/index.html"}, "globalHeaders": {"Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "X-XSS-Protection": "1; mode=block", "X-Frame-Options": "SAMEORIGIN", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "strict-origin-when-cross-origin"}}