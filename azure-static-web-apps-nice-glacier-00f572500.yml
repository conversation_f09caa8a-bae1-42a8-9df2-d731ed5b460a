name: Azure Static Web Apps CI/CD

pr:
  branches:
    include:
      - main

trigger:
  branches:
    include:
      - main

jobs:
  - job: build_and_deploy_job
    displayName: Build and Deploy Job
    condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI')))
    pool:
      vmImage: ubuntu-latest
    variables:
      - group: Azure-Static-Web-Apps-nice-glacier-00f572500-variable-group
    steps:
      - checkout: self
        submodules: true

      - script: |
          node -v
        displayName: "checking node version"

      - script: |
          npm install -g @angular/cli@16.0.0
        displayName: "Install Angular CLI"

      # - script: |
      #     npm install --legacy-peer-deps
      #     npm dedupe
      #   displayName: "Resolve Dependency Conflicts"

      - script: |
          yarn
        displayName: "Resolve Dependency Conflicts"

      # - script: |
      #     npm run build
      #   displayName: "Build Angular Project"

      - script: |
          yarn build
        displayName: "Build Angular Project"

      # - script: |
      #     ls -la dist/wbi-adani-ui
      #   displayName: "Validate Build Output"
      - task: AzureStaticWebApp@0
        inputs:
          azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_NICE_GLACIER_00F572500)
          app_location: "/" # Adjust this based on your Angular project location
          api_location: "" # Leave empty if you don't have an API
          output_location: "dist/wbi/browser" # Replace <project-name> with your Angular project's build folder name

