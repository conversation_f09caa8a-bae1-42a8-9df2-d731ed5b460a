{"name": "wbi", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/cdk": "^19.1.1", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.0", "@ng-select/ng-select": "^13.9.1", "@ngrx/store": "^18.1.1", "@popperjs/core": "^2.11.8", "apexcharts": "^4.0.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "ng-apexcharts": "^1.12.0", "ng-otp-input": "^1.9.3", "ngx-spinner": "^17.0.0", "pdfjs-dist": "^4.8.69", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.8", "@angular/cli": "^17.3.8", "@angular/compiler-cli": "^17.3.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}